
package cn.genn.ai.hub.app.application.storage;

import cn.genn.ai.hub.app.application.assembler.FileStorageAssembler;
import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.core.api.tool.FileStorageConfig;
import cn.genn.ai.hub.core.api.tool.FileStorageTypeEnum;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.KVStruct;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.util.ObjUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class FileStorageOperation {
    private final OBSOperationStrategy obsOperationStrategy;
    private final TOSOperationStrategy tosOperationStrategy;
    private final GennAIHubProperties properties;
    private final FileStorageAssembler fileStorageAssembler;

    public boolean saveFile(MultipartFile file, String objectKey) {
        Long tenantId = null;
        try {
            tenantId = CurrentUserHolder.getTenantId();
        } catch (Exception ignore) {
            log.info("get tenantId is null");
        }
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.saveFile(file, objectKey);
    }

    public boolean saveContentTOFile(String content, String objectKey) {
        Long tenantId = null;
        try {
            tenantId = CurrentUserHolder.getTenantId();
        } catch (Exception ignore) {
            log.info("get tenantId is null");
        }
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.saveContentTOFile(content, objectKey);
    }

    public boolean saveOutPutStream(ByteArrayOutputStream content, String objectKey, Long tenantId) {
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.saveOutPutStream(content, objectKey);
    }

    public boolean saveByteArray(byte[] byteArray, String objectKey, Long tenantId) {
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.saveByteArray(byteArray, objectKey);
    }

    public InputStream getFile(String objectKey) {
        Long tenantId = null;
        try {
            tenantId = CurrentUserHolder.getTenantId();
        } catch (Exception ignore) {
            log.info("get tenantId is null");
        }
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.getObject(objectKey);
    }

    public String preSignedUrlGetObject(String objectKey, String originFileName) {
        Long tenantId = null;
        try {
            tenantId = CurrentUserHolder.getTenantId();
        } catch (Exception ignore) {
            log.info("get tenantId is null");
        }
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.preSignedUrlGetObject(objectKey, originFileName);
    }

    public String getCachedPreSignedUrl(String objectKey, String originFileName) {
        Long tenantId = null;
        try {
            tenantId = CurrentUserHolder.getTenantId();
        } catch (Exception ignore) {
            log.info("get tenantId is null");
        }
        StorageStrategy strategy = getStorageStrategy(tenantId);
        return strategy.getCachedPreSignedUrl(objectKey, originFileName);
    }

    private StorageStrategy getStorageStrategy(Long tenant) {
        String storage = null;
        FileStorageConfig config = null;

        if (Objects.nonNull(tenant)) {
            // 获取租户对应的存储配置
            KVStruct<String, GennAIFileStorageProperties> tenantConfig = properties.getTenantStorageConfigs().get(tenant);
            if (tenantConfig != null) {
                storage = tenantConfig.getKey();
                GennAIFileStorageProperties storageProperties = tenantConfig.getValue();
                config = fileStorageAssembler.toFileStorageConfig(storageProperties);
                if ("obs".equals(storage)) {
                    config.setFileStorageType(FileStorageTypeEnum.OBS);
                } else if ("tos".equals(storage)) {
                    config.setFileStorageType(FileStorageTypeEnum.TOS);
                }
            } else {
                storage = properties.getDefaultStorage();
            }
        } else {
            storage = properties.getDefaultStorage();
            GennAIFileStorageProperties fileStorageProperties = null;
            switch (storage) {
                case "tos" -> {
                    fileStorageProperties = properties.getTos();
                }
                case "obs" -> {
                    fileStorageProperties = properties.getObs();
                }
                default -> throw new BusinessException("不支持的存储类型");
            }
            config = fileStorageAssembler.toFileStorageConfig(fileStorageProperties);
        }

        switch (storage) {
            case "obs":
                if (config != null) {
                    return new StorageStrategyWrapper(obsOperationStrategy, config);
                }
                return obsOperationStrategy;
            case "tos":
                if (config != null) {
                    return new StorageStrategyWrapper(tosOperationStrategy, config);
                }
                return tosOperationStrategy;
            default:
                throw new IllegalArgumentException("Unknown storage: " + storage);
        }
    }

    /**
     * 存储策略包装器，用于传递租户特定的配置
     */
    private static class StorageStrategyWrapper implements StorageStrategy {
        private final StorageStrategy delegate;
        private final FileStorageConfig config;

        public StorageStrategyWrapper(StorageStrategy delegate, FileStorageConfig config) {
            this.delegate = delegate;
            this.config = config;
        }

        @Override
        public boolean saveFile(MultipartFile file, String objectKey) {
            return delegate.saveFile(file, objectKey, config);
        }

        @Override
        public boolean saveContentTOFile(String content, String objectKey) {
            return delegate.saveContentTOFile(content, objectKey);
        }

        @Override
        public boolean saveOutPutStream(ByteArrayOutputStream content, String objectKey) {
            return delegate.saveOutPutStream(content, objectKey);
        }

        @Override
        public boolean saveByteArray(byte[] byteArray, String objectKey) {
            return delegate.saveByteArray(byteArray, objectKey);
        }

        @Override
        public InputStream getObject(String objectKey) {
            return delegate.getObject(objectKey);
        }

        @Override
        public String preSignedUrlGetObject(String objectKey, String originFileName) {
            return delegate.preSignedUrlGetObject(objectKey, originFileName, null, config);
        }

        @Override
        public String getCachedPreSignedUrl(String objectKey, String originFileName) {
            return delegate.preSignedUrlGetObject(objectKey, originFileName, null, config);
        }

        @Override
        public boolean saveFile(MultipartFile file, String objectKey, FileStorageConfig config) {
            return delegate.saveFile(file, objectKey, this.config);
        }

        @Override
        public String preSignedUrlGetObject(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
            return delegate.preSignedUrlGetObject(objectKey, originFileName, expireSeconds, this.config);
        }

        @Override
        public String preSignedUrlGetObjectByCustomDomain(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
            return delegate.preSignedUrlGetObjectByCustomDomain(objectKey, originFileName, expireSeconds, this.config);
        }
    }

    public boolean saveFile(MultipartFile file, String objectKey, FileStorageConfig config) {
        StorageStrategy strategy = getStorageStrategy(config.getFileStorageType());
        return strategy.saveFile(file, objectKey, config);
    }

    public String preSignedUrlGetObject(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
        StorageStrategy strategy = getStorageStrategy(config.getFileStorageType());
        return strategy.preSignedUrlGetObject(objectKey, originFileName, expireSeconds, config);
    }

    public String preSignedUrlGetObjectByCustomDomain(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
        StorageStrategy strategy = getStorageStrategy(config.getFileStorageType());
        return strategy.preSignedUrlGetObjectByCustomDomain(objectKey, originFileName, expireSeconds, config);
    }

    private StorageStrategy getStorageStrategy(FileStorageTypeEnum fileStorageType) {
        StorageStrategy strategy;
        switch (fileStorageType) {
            case OBS -> strategy = obsOperationStrategy;
            case TOS -> strategy = tosOperationStrategy;
            default -> throw new IllegalArgumentException("Unknown storage type: " + fileStorageType);
        }
        return strategy;
    }

    public boolean saveImageFromUrl(String imageUrl, String objectKey) {
        Long tenantId = 1L;
        if(ObjUtil.isNotNull(CurrentUserHolder.getCurrentUser()) && ObjUtil.isNotNull(CurrentUserHolder.getCurrentUser().getTenantId())){
            tenantId = CurrentUserHolder.getCurrentUser().getTenantId();
        }

        try (InputStream inputStream = new URL(imageUrl).openStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return saveOutPutStream(outputStream, objectKey, tenantId);
        } catch (Exception e) {
            log.error("从URL保存图片失败，imageUrl: {}, objectKey: {}", imageUrl, objectKey, e);
            return false;
        }
    }

    public boolean saveImageFromBase64(String imageBase64, String objectKey) {
        Long tenantId = 1L;
        if(ObjUtil.isNotNull(CurrentUserHolder.getCurrentUser()) && ObjUtil.isNotNull(CurrentUserHolder.getCurrentUser().getTenantId())){
            tenantId = CurrentUserHolder.getCurrentUser().getTenantId();
        }

        try {
            byte[] imageBytes = Base64.getDecoder().decode(imageBase64);

            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                outputStream.write(imageBytes);
                return saveOutPutStream(outputStream, objectKey, tenantId);
            }
        } catch (Exception e) {
            log.error("从Base64保存图片失败，objectKey: {}", objectKey, e);
            return false;
        }
    }
}
