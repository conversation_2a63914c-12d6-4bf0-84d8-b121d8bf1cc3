package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ASRProviderParams {
   private String mode = "bigmodel";
   private String appId = "**********";
   private String accessToken = "S7wi1qMspgog-_8ZH4n0hdf-7RD2_8ob";
    /**
     * volc.bigasr.sauc.duration：小时版。
     * volc.bigasr.sauc.concurrent：并发版。
     * 默认值为 volc.bigasr.sauc.duration。
     */
   private String apiResourceId = "volc.bigasr.sauc.duration";
   private Integer streamMode;
   private String context;
   private String boostingTableId;
   private String boostingTableName;
   private Integer contextHistoryLength;
   private String correctTableId;
   private String correctTableName;
   private String cluster;
}
