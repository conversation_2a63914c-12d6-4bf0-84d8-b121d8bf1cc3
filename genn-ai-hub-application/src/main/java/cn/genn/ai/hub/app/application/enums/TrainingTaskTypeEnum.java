package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum TrainingTaskTypeEnum {

    FILE_PARSE("file_parse", "文件解析"),
    ANALYZER("analyzer", "内容分词"),
    SEGMENT("segment", "内容分块"),
    INDEX("index", "数据索引"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    TrainingTaskTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}

