package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.KbRepoCollectionAssembler;
import cn.genn.ai.hub.app.application.command.FSSheetSyncCommand;
import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.dto.feishu.FSSyncParams;
import cn.genn.ai.hub.app.application.dto.request.*;
import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.feishu.FSFileType;
import cn.genn.ai.hub.app.application.processor.NoModelDataListener;
import cn.genn.ai.hub.app.application.service.query.KbRepoBaseInfoQueryService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataIndexQueryService;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.external.feishu.FSUtil;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepCollectionRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataIndexRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepFileRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.idev.excel.ExcelReader;
import cn.idev.excel.FastExcel;
import cn.idev.excel.read.metadata.ReadSheet;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoCollectionActionService {

    private final KbRepCollectionRepositoryImpl collectionRepository;
    private final KbRepDataRepositoryImpl dataRepository;
    private final KbRepDataIndexRepositoryImpl dataIndexRepository;
    private final KbRepoCollectionAssembler assembler;
    private final KbRepFileRepositoryImpl fileRepository;
    private final FileStorageOperation fileStorageOperation;
    private final KbRepoDataIndexActionService dataIndexActionService;
    private final KbRepoDataActionService dataActionService;
    private final KbRepoDataIndexQueryService dataIndexQueryService;
    private final KbRepoBaseInfoQueryService repoQueryService;

    /**
     * 移动数据集
     */
    public Boolean move(KbRepoCollectionMoveCommand command) {
        KbRepoCollectionPO po = new KbRepoCollectionPO();
        po.setId(command.getId());
        po.setPid(command.getPid());
        return collectionRepository.updateById(po);
    }

    /**
     * 重命名数据集
     */
    public Boolean rename(KbRepoCollectionRenameCommand command) {
        KbRepoCollectionPO po = new KbRepoCollectionPO();
        po.setId(command.getId());
        po.setName(command.getName());
        return collectionRepository.updateById(po);
    }

    /**
     * 删除数据集
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        KbRepoCollectionPO collectionPO = collectionRepository.getById(id);
        if (Objects.isNull(collectionPO)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 判断是否是文件夹类型的数据集
        if (CollectionTypeEnum.FOLDER.equals(collectionPO.getCollectionType())) {
            // 判断有没有子集
            List<KbRepoCollectionDTO> children = collectionRepository.selectCollectionByPId(id);
            if (CollectionUtil.isNotEmpty(children)) {
                throw new BusinessException(MessageCode.COLLECTION_HAS_CHILDREN);
            }
        }
        collectionRepository.deleteById(id);
        // 通知算法删除数据集下的向量索引
        dataIndexActionService.deleteDataIndexVectorOfCollection(collectionPO.getRepoId(), collectionPO.getId(), collectionPO.getTenantId());
        return true;
    }

    /**
     * 更新数据集启用状态
     */
    public Boolean updateStatus(KbRepoCollectionUpdateStatusCommand command) {
        KbRepoCollectionPO po = new KbRepoCollectionPO();
        po.setId(command.getId());
        po.setEnabled(command.getEnabled());
        return collectionRepository.updateById(po);
    }

    /**
     * 更新数据集状态
     */
    @IgnoreTenant
    public Boolean updateCollectionStatus(Long collectionId, CollectionStatusEnum collectionStatusEnum) {
        // 先查询当前状态
        KbRepoCollectionPO oldCollPO = collectionRepository.getById(collectionId);
        if (Objects.isNull(oldCollPO)) {
            log.error("更新数据集状态失败,数据集不存在,collectionId:{}", collectionId);
            return false;
        }
        if (oldCollPO.getCollectionStatus().equals(collectionStatusEnum)) {
            return true;
        }
        KbRepoCollectionPO po = new KbRepoCollectionPO();
        po.setId(collectionId);
        po.setCollectionStatus(collectionStatusEnum);
        return collectionRepository.updateById(po);
    }

    /**
     * 新建文件夹
     */
    public Long createFolder(KbRepoCollectionFolderCreateCommand command) {
        KbRepoCollectionPO po = KbRepoCollectionPO.builder()
            .pid(command.getPid())
            .repoId(command.getRepoId())
            .name(command.getName())
            .collectionType(CollectionTypeEnum.FOLDER)
            .collectionStatus(CollectionStatusEnum.COMPLETED)
            .build();
        boolean isSuccess = collectionRepository.save(po);
        return isSuccess ? po.getId() : null;
    }

    /**
     * 新建手动数据集  没有分段参数 分块由自己后续录入
     */
    public Long createManualCollection(KbRepoCollectionManualCreateCommand command) {
        KbRepoCollectionPO po = KbRepoCollectionPO.builder()
            .pid(command.getPid())
            .repoId(command.getRepoId())
            .name(command.getName())
            .trainingType(command.getTrainingType())
            .collectionType(CollectionTypeEnum.MANUAL_TEXT)
            .collectionStatus(CollectionStatusEnum.COMPLETED)
            .build();
        boolean isSuccess = collectionRepository.save(po);
        return isSuccess ? po.getId() : null;
    }

    /**
     * 新建文本数据集 本地上传
     */
    public Long createTextCollectionOfFile(KbRepoCollectionTextOfFileCreateCommand command) {
        // 先查询文件信息
        KbRepoFilePO fileInfo = fileRepository.getBaseMapper().selectById(command.getFileId());
        if (fileInfo == null) {
            throw new BusinessException(MessageCode.KB_REPO_FILE_NOT_EXIST);
        }
        KbRepoCollectionPO po = assembler.KbRepoCollectionTextOfFileCreateCommandToPO(command);
        po.setFileId(fileInfo.getId());
        po.setRawTextLength(fileInfo.getLength());
        po.setExternalFileId(fileInfo.getExternalFileId());
        po.setExternalFileUrl(fileInfo.getExternalFileUrl());
        po.setCollectionStatus(CollectionStatusEnum.WAIT_PARSE);
        po.setCollectionType(command.getCollectionType());
        boolean isSuccess = collectionRepository.save(po);
        if (!isSuccess) {
            throw new BusinessException(MessageCode.KB_REPO_COLLECTION_CREATE_FAIL);
        }
        // 调用算法进行异步文件解析 解析后再调用算法进行分块
        KbRepoTaskOfFileParseBody taskBody = KbRepoTaskOfFileParseBody.builder()
            .tenantId(command.getTenantId())
            .repoId(command.getRepoId())
            .collectionId(po.getId())
            .fileId(fileInfo.getId())
            .externalFileId(fileInfo.getExternalFileId())
            .externalFileUrl(fileStorageOperation.preSignedUrlGetObject(fileInfo.getExternalFileId(), fileInfo.getFileName()))
            .fileName(fileInfo.getFileName())
            .contentType(fileInfo.getContentType())
            .deepParse(command.getDeepParse())
            .smartChunkSummary(command.getSmartChunkSummary())
            .build();
        SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfFileParse(taskBody);
        return po.getId();
    }

    /**
     * 新建文本数据集 网页链接
     */
    public Long createTextCollectionOfUrl(KbRepoCollectionTextOfUrlCreateCommand command) {
        KbRepoCollectionPO po = assembler.KbRepoCollectionTextOfUrlCreateCommandToPO(command);
        boolean isSuccess = collectionRepository.save(po);
        // TODO 通知算法分块
        return isSuccess ? po.getId() : null;
    }

    /**
     * 新建文本数据集 自定义文本
     */
    public Long createTextCollectionOfText(KbRepoCollectionTextOfTextCreateCommand command) {
        KbRepoCollectionPO po = assembler.KbRepoCollectionTextOfTextCreateCommandToPO(command);
        // 计算原始文本长度 和 hash
        po.setRawTextLength(StrUtil.length(command.getRawText()));
        po.setHashRawText(MD5.create().digestHex(command.getRawText()));
        po.setCollectionStatus(CollectionStatusEnum.WAIT_SEGMENT);
        boolean isSuccess = collectionRepository.save(po);
        if (!isSuccess) {
            throw new BusinessException(MessageCode.KB_REPO_COLLECTION_CREATE_FAIL);
        }
        // 通知算法分块
        KbRepoTaskOfTextChunkBody taskBody = KbRepoTaskOfTextChunkBody.builder()
            .tenantId(command.getTenantId())
            .repoId(command.getRepoId())
            .collectionId(po.getId())
            .contentType("text/plain")
            .rawText(command.getRawText())
            .smartChunkSummary(command.getSmartChunkSummary())
            .build();
        SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfTextChunk(taskBody);
        return po.getId();
    }

    @Transactional
    public Long afreshTrain(KbRepoAfreshTrainCommand command) {
        // 先查询文件信息
        if (command.getFileId() == null || command.getFileId() == 0L) {
            // todo 没有文件的直接创建，分块
            return -1L;
        }
        KbRepoFilePO fileInfo = fileRepository.getBaseMapper().selectById(command.getFileId());
        if (fileInfo == null) {
            throw new BusinessException(MessageCode.KB_REPO_FILE_NOT_EXIST);
        }
        KbRepoCollectionPO oldCollPO = collectionRepository.getById(command.getCollectionId());
        if (oldCollPO == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        KbRepoCollectionPO newCollPO = assembler.convertAfreshTrain(command);
        newCollPO.setPid(oldCollPO.getPid());
        newCollPO.setRawTextLength(fileInfo.getLength());
        newCollPO.setExternalFileId(fileInfo.getExternalFileId());
        newCollPO.setDeepImport(oldCollPO.getDeepImport());
        newCollPO.setExternalFileUrl(fileInfo.getExternalFileUrl());
        newCollPO.setCollectionStatus(CollectionStatusEnum.WAIT_PARSE);
        boolean isSuccess = collectionRepository.save(newCollPO);
        if (!isSuccess) {
            throw new BusinessException(MessageCode.KB_REPO_COLLECTION_CREATE_FAIL);
        }
        // 删除分块，向量
        collectionRepository.removeById(oldCollPO.getId());
        deleteCollAndDataAndIndex(oldCollPO.getId(), oldCollPO.getRepoId(), oldCollPO.getTenantId());
        // 调用算法进行异步文件解析 解析后再调用算法进行分块
        KbRepoTaskOfFileParseBody taskBody = KbRepoTaskOfFileParseBody.builder()
            .tenantId(command.getTenantId())
            .repoId(command.getRepoId())
            .collectionId(newCollPO.getId())
            .fileId(fileInfo.getId())
            .externalFileId(fileInfo.getExternalFileId())
            .externalFileUrl(fileStorageOperation.preSignedUrlGetObject(fileInfo.getExternalFileId(), fileInfo.getFileName()))
            .fileName(fileInfo.getFileName())
            .contentType(fileInfo.getContentType())
            .deepParse(command.getDeepParse())
            .smartChunkSummary(command.getSmartChunkSummary())
            .build();
        SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfFileParse(taskBody);
        return newCollPO.getPid();
    }

    /**
     * 指定数据集，知识库，删除分块，向量，索引
     *
     * @param collId collId
     * @param repoId repoId
     */
    public void deleteCollAndDataAndIndex(Long collId, Long repoId, Long tenantId) {
        dataRepository.removeByCollId(repoId, collId);
        dataIndexRepository.removeByCollId(repoId, collId);
        dataIndexActionService.deleteDataIndexVectorOfCollection(repoId, collId, tenantId);
    }


    @Transactional(rollbackFor = Exception.class)
    public Long createSheetColl(KbRepoCollectionSheetCreateCommand command) {
        // 先查询文件信息
        KbRepoFilePO fileInfo = fileRepository.getBaseMapper().selectById(command.getFileId());
        if (fileInfo == null) {
            throw new BusinessException(MessageCode.KB_REPO_FILE_NOT_EXIST);
        }
        // 检查文件类型
        String originalFilename = fileInfo.getFileName();
        if (originalFilename == null || !(originalFilename.endsWith(".xlsx") || originalFilename.endsWith(".xls"))) {
            log.error("文件格式不正确，只支持.xlsx或.xls格式");
            throw new BusinessException(MessageCode.KB_REPO_FILE_CHECK_EXCEL_FAIL);
        }
        KbRepoCollectionPO po = assembler.sheetCommandConvertPO(command);
        po.setFileId(fileInfo.getId());
        po.setRawTextLength(fileInfo.getLength());
        po.setExternalFileId(fileInfo.getExternalFileId());
        po.setExternalSheetId(command.getSheetIndex().toString());
        po.setExternalFileUrl(fileInfo.getExternalFileUrl());
        po.setCollectionStatus(CollectionStatusEnum.WAIT_PARSE);
        po.setCollectionType(CollectionTypeEnum.SHEET);
        boolean isSuccess = collectionRepository.save(po);
        if (!isSuccess) {
            throw new BusinessException(MessageCode.KB_REPO_COLLECTION_CREATE_FAIL);
        }
        // sheet文件解析 转数组+分块
        InputStream inputStream = fileStorageOperation.getFile(fileInfo.getExternalFileId());
        NoModelDataListener noModelDataListener = new NoModelDataListener();
        FastExcel.read(inputStream, noModelDataListener).sheet(command.getSheetIndex()).doRead();
        List<Map<Integer, String>> cachedDataList = noModelDataListener.getDataList();
        List<String> headers = noModelDataListener.getHeader();
        headers = headers.stream().map(value -> value == null ? "" : value).collect(Collectors.toList());
        Long repoId = po.getRepoId();
        Long collectionId = po.getId();

        List<String> markdownTable = FSUtil.getSheetMarkTable(headers, cachedDataList, command.getChunckSize());
        dataActionService.saveChunkedText(repoId, collectionId, markdownTable);

        createDataIndexAndUpdateStatus(repoId, collectionId, command.getSmartChunkSummary());
        return po.getId();
    }

    private void createDataIndexAndUpdateStatus(Long repoId, Long collectionId, Boolean smartChunkSummary) {
        KbRepoBaseInfoDTO repoInfo = repoQueryService.get(IdQuery.builder().id(repoId).build());
        if (Objects.isNull(repoInfo)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 创建分块下所有的多个索引任务
        List<KbRepoDataIndexDTO> dataIndexDtoList = dataIndexQueryService.getByCollectionId(repoId, collectionId);
        for (KbRepoDataIndexDTO dataIndexDto : dataIndexDtoList) {
            if (Boolean.TRUE.equals(smartChunkSummary)) {
                KbRepoTaskOfSmartChunkSummaryBody kbRepoTaskOfSmartChunkSummaryBody = KbRepoTaskOfSmartChunkSummaryBody.builder()
                    .tenantId(CurrentUserHolder.getTenantId())
                    .repoId(repoId)
                    .agentModelKey(repoInfo.getAgentModelKey())
                    .collectionId(collectionId)
                    .dataKey(dataIndexDto.getDataKey())
                    .indexKey(dataIndexDto.getIndexKey())
                    .vectorModelKey(repoInfo.getVectorModelKey())
                    .build();
                SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfSmartChunkSummary(kbRepoTaskOfSmartChunkSummaryBody);
            } else {
                KbRepoTaskOfIndexVectorBody kbRepoTaskOfIndexVectorBody = KbRepoTaskOfIndexVectorBody.builder()
                    .tenantId(CurrentUserHolder.getTenantId())
                    .repoId(repoId)
                    .collectionId(collectionId)
                    .dataKey(dataIndexDto.getDataKey())
                    .indexKey(dataIndexDto.getIndexKey())
                    .rawText(dataIndexActionService.handleRawText(dataIndexDto.getDataKey(), dataIndexDto.getIndexKey()))
                    .vectorModelKey(repoInfo.getVectorModelKey())
                    .build();
                SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfIndexVector(kbRepoTaskOfIndexVectorBody);
            }
        }
        // 更新collection状态为 等待索引 CollectionStatusEnum.WAIT_INDEX
        updateCollectionStatus(collectionId, CollectionStatusEnum.WAIT_INDEX);
    }


    @Transactional(rollbackFor = Exception.class)
    public void createFSCollection(FSSheetSyncCommand command, List<Map<Integer, String>> cachedDataList, String objectKey) {
        List<String> markdownTable = FSUtil.getSheetMarkTable(null, cachedDataList, command.getChunkSize());
        KbRepoCollectionPO po = assembler.fsSheetConvertPO(command);
        po.setRawTextLength(markdownTable.stream()
            .mapToInt(String::length)
            .sum());
        po.setExternalFileId(objectKey);
        po.setExternalSheetId(command.getSheetId());
        po.setSyncParams(JsonUtils.toJson(assembler.syncParams(command)));
        po.setExternalFileType(FSFileType.SHEET.getCode());
        po.setCollectionStatus(CollectionStatusEnum.WAIT_PARSE);
        boolean isSuccess = collectionRepository.save(po);
        if (!isSuccess) {
            throw new BusinessException(MessageCode.KB_REPO_COLLECTION_CREATE_FAIL);
        }
        // sheet文件解析 转数组+分块
        dataActionService.saveChunkedText(po.getRepoId(), po.getId(), markdownTable);
        createDataIndexAndUpdateStatus(po.getRepoId(), po.getId(), command.getSmartChunkSummary());

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFSSheetCollection(KbRepoCollectionDTO collDTO, List<Map<Integer, String>> cachedDataList) {
        List<String> markdownTable = FSUtil.getSheetMarkTable(null, cachedDataList, collDTO.getChunkSize());
        KbRepoCollectionPO update = new KbRepoCollectionPO();
        update.setId(collDTO.getId());
        update.setRawTextLength(markdownTable.stream()
            .mapToInt(String::length)
            .sum());
        update.setCollectionStatus(CollectionStatusEnum.WAIT_PARSE);
        if (collectionRepository.updateById(update)) {
            // sheet文件解析 转数组+分块
            deleteCollAndDataAndIndex(collDTO.getId(), collDTO.getRepoId(), collDTO.getTenantId());
            dataActionService.saveChunkedText(collDTO.getRepoId(), collDTO.getId(), markdownTable);
            createDataIndexAndUpdateStatus(collDTO.getRepoId(), collDTO.getId(), collDTO.getSmartChunkSummary());
        }

    }

    public List<LocalSheetContentResp> sheetContent(KbSheetContentCommand command) {
        // 先查询文件信息
        KbRepoFilePO fileInfo = fileRepository.getBaseMapper().selectById(command.getFileId());
        if (fileInfo == null) {
            throw new BusinessException(MessageCode.KB_REPO_FILE_NOT_EXIST);
        }
        // 检查文件类型
        String originalFilename = fileInfo.getFileName();
        if (originalFilename == null || !(originalFilename.endsWith(".xlsx") || originalFilename.endsWith(".xls"))) {
            log.error("文件格式不正确，只支持.xlsx或.xls格式");
            throw new BusinessException(MessageCode.KB_REPO_FILE_CHECK_EXCEL_FAIL);
        }

        List<LocalSheetContentResp> result = new ArrayList<>();
        int sheetCount = 0;
        List<String> sheetNames = new ArrayList<>();
        try (InputStream inputStream = fileStorageOperation.getFile(fileInfo.getExternalFileId())) {
            cn.hutool.poi.excel.ExcelReader reader = ExcelUtil.getReader(inputStream);
            sheetCount = reader.getSheetCount();
            sheetNames = reader.getSheetNames();
            reader.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (sheetCount == 0) {
            return result;
        }
        // sheet文件解析 转数组+分块
        InputStream inputStream = fileStorageOperation.getFile(fileInfo.getExternalFileId());

        // 读取指定 Sheet
        try (ExcelReader excelReader = FastExcel.read(inputStream).build()) {
            for (int i = 0; i < sheetCount; i++) {
                NoModelDataListener listener = new NoModelDataListener();
                ReadSheet sheet = FastExcel.readSheet(i).registerReadListener(listener).build();
                excelReader.read(sheet);
                List<Map<Integer, String>> cachedDataList = listener.getDataList();
                List<List<String>> values = cachedDataList.stream().map(map -> map.values()
                    .stream()
                    .map(value -> value == null ? "" : value)
                    .collect(Collectors.toList())).toList();
                result.add(LocalSheetContentResp.builder()
                    .sheetIndex(i)
                    .sheetName(sheetNames.get(i))
                    .header(listener.getHeader())
                    .values(values)
                    .build());
            }
        }
        return result;
    }

}
