package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.assembler.PluginAssembler;
import cn.genn.ai.hub.app.application.command.PluginInvokeCommand;
import cn.genn.ai.hub.app.infrastructure.utils.AsyncExecutor;
import cn.genn.ai.hub.core.manager.PluginRegistry;
import cn.genn.ai.hub.core.plugin.*;
import cn.genn.ai.hub.core.transport.PluginTransport;
import cn.genn.ai.hub.plugin.common.exception.AIPluginErrorCode;
import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.ProductExEnum;
import cn.genn.core.exception.ServiceExEnum;
import cn.genn.core.utils.ResUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.http.ContentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PluginService {

    public static final String PLUGIN_STREAM_END = "PLUGIN_STREAM_END: ";
    private final PluginRegistry pluginRegistry;
    private final PluginAssembler pluginAssembler;
    private final ObjectMapper objectMapper = new ObjectMapper();


    public Object invoke(PluginInvokeCommand command) {
        HttpServletResponse response = ServletUtils.getResponse();
        PluginRequest pluginRequest = pluginAssembler.toPluginRequest(command);
        PluginTransport transport = pluginRegistry.getPluginInstance(command.getPluginKey(), command.getVersion());
        PluginDefinition pluginDefinition = pluginRegistry.getPluginDefinition(command.getPluginKey(), command.getVersion());
        PluginContext pluginContext = initPluginContext(command, pluginDefinition, pluginRequest);
        if (!command.isStream()) {
            try {
                PluginContextHolder.set(pluginContext);
                PluginResponse resp = transport.callPlugin(command.getPluginKey(), pluginRequest, pluginContext);
                return ResUtils.ok(resp);
            } finally {
                PluginContextHolder.clear();
            }
        }
        response.setContentType(ContentType.EVENT_STREAM.getValue());
        SseEmitter emitter = new SseEmitter(0L);
        CompletableFuture.runAsync(() -> {
            try {
                PluginContextHolder.set(pluginContext);
                transport.callPluginStream(command.getPluginKey(), pluginRequest, pluginContext, partialData -> {
                    try {
                        ObjectNode text = objectMapper.createObjectNode().put("text", partialData);
                        emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(text)));
                    } catch (IOException e) {
                        doWithException(e, pluginRequest, emitter);
                    }
                }, resp -> {
                    try {
                        emitter.send(SseEmitter.event().data(PLUGIN_STREAM_END + JsonUtils.toJson(ResUtils.ok(resp))));
                        emitter.complete();
                    } catch (IOException e) {
                        doWithException(e, pluginRequest, emitter);
                    }
                }, e -> doWithException(e, pluginRequest, emitter));
            } catch (Exception e) {
                doWithException(e, pluginRequest, emitter);
            } finally {
                PluginContextHolder.clear();
            }
        }, AsyncExecutor.PLUGIN_STREAM_EXECUTOR_SERVICE);
        return emitter;
    }

    private PluginContext initPluginContext(PluginInvokeCommand command, PluginDefinition pluginDefinition, PluginRequest pluginRequest) {
        return PluginContext.builder()
            .pluginKey(command.getPluginKey())
            .pluginDefinition(pluginDefinition)
            .version(command.getVersion())
            .request(pluginRequest)
            .build();
    }

    private void doWithException(Throwable t, PluginRequest request, SseEmitter emitter) {
        log.error("PluginService invoke error, workflowId: [{}], requestId: [{}], pluginKey: [{}], version: [{}]",
            request.getWorkflowId(), request.getRequestId(), request.getPluginKey(), request.getVersion(), t);
        try {
            String code;
            String message;
            if (t instanceof BaseException) {
                code = ServiceExEnum.AI_HUB.getCode() + ProductExEnum.AI.getCode() + ((BaseException) t).getCode();
                message = t.getMessage();
            } else {
                code = ServiceExEnum.AI_HUB.getCode() + ProductExEnum.AI.getCode() + AIPluginErrorCode.FAIL.buildCode();
                message = AIPluginErrorCode.FAIL.getDescription();
            }
            emitter.send(SseEmitter.event().data(PLUGIN_STREAM_END + JsonUtils.toJson(ResUtils.error(code, message))));
        } catch (IOException ignore) {
        } finally {
            emitter.complete();
        }
    }
}
