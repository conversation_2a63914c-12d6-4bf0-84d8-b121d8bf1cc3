package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.KbRepoCategoryAssembler;
import cn.genn.ai.hub.app.application.dto.KbCatFullPath;
import cn.genn.ai.hub.app.application.dto.KbCatPathQuery;
import cn.genn.ai.hub.app.application.dto.KbRepoCategoryDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCategoryQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCategoryMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCollectionMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCategoryPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoCategoryQueryService {

    private final KbRepoCategoryMapper categoryMapper;
    private final KbRepoBaseInfoMapper repoBaseInfoMapper;
    private final KbRepoCategoryAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoCategoryDTO分页对象
     */
    public PageResultDTO<KbRepoCategoryDTO> page(KbRepoCategoryQuery query) {
        KbRepoCategoryPO po = assembler.query2PO(query);
        return assembler.toPageResult(categoryMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoCategoryDTO
     */
    public KbRepoCategoryDTO get(IdQuery query) {
        return assembler.PO2DTO(categoryMapper.selectById(query.getId()));
    }

    public List<KbCatFullPath> fullPath(KbCatPathQuery query) {
        LinkedList<KbCatFullPath> result = Lists.newLinkedList();
        if (Boolean.TRUE.equals(query.getIsCat())) {
            findRootPatch(query.getId(), result);
        } else {
            KbRepoBaseInfoPO baseInfoPO = repoBaseInfoMapper.selectById(query.getId());
            if (baseInfoPO != null) {
                result.push(KbCatFullPath
                    .builder()
                    .isCat(false)
                    .id(baseInfoPO.getId())
                    .name(baseInfoPO.getName())
                    .build());
                findRootPatch(baseInfoPO.getCatId(), result);
            }
        }
        return result;
    }

    private void findRootPatch(Long catId, LinkedList<KbCatFullPath> result) {
        if (catId == null || catId == 0) {
            result.push(KbCatFullPath
                .builder()
                .isCat(true)
                .id(0L)
                .name("根目录")
                .build());
            return;
        }
        KbRepoCategoryPO categoryPO = categoryMapper.selectById(catId);
        result.push(KbCatFullPath
            .builder()
            .isCat(true)
            .id(categoryPO.getId())
            .name(categoryPO.getCatName())
            .build());
        findRootPatch(categoryPO.getPid(), result);
    }

}

