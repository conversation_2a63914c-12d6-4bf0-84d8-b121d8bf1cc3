package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.expression;

import cn.genn.ai.hub.app.domain.auth.model.valobj.FieldWhereExpression;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthExpressionInterceptorParam;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DefaultAuthExpressionResolver implements AuthExpressionResolver {

    @Override
    public FieldWhereExpression resolve(AuthExpressionInterceptorParam param) {
        return FieldWhereExpression.buildAllAuthExpression();
    }
}
