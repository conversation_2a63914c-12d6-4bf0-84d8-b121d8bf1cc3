package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.dto.feishu.NodeData;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 飞书文档同步
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书文档同步")
public class FSDocSyncCommand {

    @Schema(description = "知识库ID", required = true)
    @NotEmpty(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "应用ID", required = true)
    @NotEmpty(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "选中的文件", required = true)
    @NotEmpty(message = "知识库ID不能为空")
    private List<NodeData> nodeData;

    @Schema(description = "分块大小，文本分割的字符数限制（单位：字符）")
    private Integer chunkSize;

    @Schema(description = "分块分隔符，用于文本分割的符号（如换行符）")
    private String chunkSplitter;

    @Schema(description = "深度解析开关")
    private Boolean deepParse;

    @Schema(description = "智能分块摘要开关")
    private Boolean smartChunkSummary;

    @Schema(description = "租户信息", hidden = true)
    private Long tenantId;
}
