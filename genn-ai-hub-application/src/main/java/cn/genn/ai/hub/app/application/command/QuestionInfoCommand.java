package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * QuestionInfo操作对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionInfoCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流id")
    private String appId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "会话的关联id")
    private String chatId;

    @Schema(description = "一次问答关联id")
    private String taskId;

    @Schema(description = "原始问题")
    private String question;

    @Schema(description = "最终回答")
    private String answer;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户飞书openid")
    private String openId;

}

