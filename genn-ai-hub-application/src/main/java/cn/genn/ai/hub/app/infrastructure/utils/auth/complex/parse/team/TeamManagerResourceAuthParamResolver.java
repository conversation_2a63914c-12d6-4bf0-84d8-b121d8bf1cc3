package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParamResolver;
import cn.genn.core.exception.CheckException;

import java.util.List;

/**
 * 团队管理者对资源的权限解析器
 *
 * <AUTHOR>
 */
public abstract class TeamManagerResourceAuthParamResolver implements AuthParamResolver {

    @Override
    public List<AuthParam> authParams(AuthInterceptorParam interceptor) {
        ActionType[] actionTypes = interceptor.getAuth().actionType();
        Long teamId = getTeamId(interceptor);
        if (teamId == null) {
            throw new CheckException(MessageCode.AUTH_API_AUTH_FAILED);
        }
        //判断是否对团队有管理权限
        return List.of(
            AuthParam.builder()
                .subjectType(SubjectType.USER)
                .resourceType(ResourceType.TEAM)
                .resourceKey(teamId.toString())
                .actionType(actionTypes)
                .build()
        );
    }

    protected abstract Long getTeamId(AuthInterceptorParam interceptor);
}
