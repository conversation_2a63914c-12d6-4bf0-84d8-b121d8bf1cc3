package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskOfIndexVectorBody;
import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.handler.algorithm.FileParseResponse;
import cn.genn.ai.hub.app.application.processor.handler.algorithm.IndexVectorRequest;
import cn.genn.ai.hub.app.application.service.action.KbRepoCollectionActionService;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import com.google.gson.reflect.TypeToken;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.INDEX_VECTOR;

@Component
@Slf4j
public class IndexVectorTaskHandler extends AbstractTaskHandler {

    @Resource
    private KbRepoCollectionActionService collectionActionService;

    @Override
    public TaskTypeEnum getType() {
        return INDEX_VECTOR;
    }


    @Override
    public void invoke(KbRepoTaskDTO task) {
        // 异步调用算法接口的实现
        // 接受算法的异步回调结果 更新任务状态 更新库里索引的算法处理状态
        async(() -> {
            KbRepoTaskOfIndexVectorBody body = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfIndexVectorBody.class);
            // 构建请求实体
            IndexVectorRequest request = new IndexVectorRequest();
            request.setTenantId(task.getTenantId());
            request.setTaskId(task.getId());
            request.setUserId(body.getUserId());
            request.setDataKey(body.getDataKey());
            request.setIndexKey(body.getIndexKey());
            request.setRepoId(body.getRepoId());
            request.setCollectionId(body.getCollectionId());
            request.setRawText(body.getRawText());
            request.setVectorModelKey(body.getVectorModelKey());
            try {
                String url = properties.getKbInvokeUrl().getRequestUrl(getType());
                TypeToken<FileParseResponse> typeToken = new TypeToken<FileParseResponse>() {
                };
                ResponseResult<FileParseResponse> result = invokeService.fetchPost(url, JsonUtils.toJson(request), typeToken);
                log.info("IndexVectorTaskHandler invoke result:{}", JsonUtils.toJson(result));
            } catch (Exception e) {
                log.error("IndexVectorTaskHandler invoke error:{}", e);
                throw new RuntimeException(e);
            }
            // 更新数据集状态为索引处理中
            collectionActionService.updateCollectionStatus(body.getCollectionId(), CollectionStatusEnum.PROCESSING);
        });
    }

}
