package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionAnalysisMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionAnalysisPO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 补全question_info表type字段历史数据的定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class QuestionInfoTypeUpdateTask extends IJobHandler {

    @Resource
    private QuestionAnalysisMapper questionAnalysisMapper;

    @Resource
    private QuestionInfoMapper questionInfoMapper;

    @Override
    @IgnoreTenant
    public void execute() throws Exception {
        log.info("QuestionInfoTypeUpdateTask begin");

        try {
            int pageSize = 500;
            int pageNum = 1;
            int totalUpdated = 0;

            while (true) {
                // 分页查询question_analysis表
                Page<QuestionAnalysisPO> page = new Page<>(pageNum, pageSize);
                LambdaQueryWrapper<QuestionAnalysisPO> wrapper = new QueryWrapper<QuestionAnalysisPO>()
                    .lambda()
                    .isNotNull(QuestionAnalysisPO::getRelInfoId)
                    .ne(QuestionAnalysisPO::getRelInfoId, "");

                Page<QuestionAnalysisPO> result = questionAnalysisMapper.selectPage(page, wrapper);

                if (!result.hasNext()) {
                    break;
                }

                // 处理当前页数据
                int batchUpdated = processBatch(result.getRecords());
                totalUpdated += batchUpdated;

                log.info("第{}页处理完成，更新了{}条记录", pageNum, batchUpdated);

                pageNum++;
            }

            log.info("QuestionInfoTypeUpdateTask completed, total updated {} records", totalUpdated);
            XxlJobHelper.handleSuccess("成功更新 " + totalUpdated + " 条记录");

        } catch (Exception e) {
            log.error("QuestionInfoTypeUpdateTask failed", e);
            XxlJobHelper.handleFail("任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 处理一批数据
     */
    private int processBatch(List<QuestionAnalysisPO> analysisList) {
        int updatedCount = 0;

        for (QuestionAnalysisPO analysis : analysisList) {
            try {
                // 解析rel_info_id字段
                List<Long> questionIds = JsonUtils.parseToList(analysis.getRelInfoId(), Long.class);
                String type = analysis.getType();

                if (questionIds != null && !questionIds.isEmpty() && type != null) {
                    // 批量更新question_info表的type字段
                    questionInfoMapper.updateTypeByIds(questionIds, type);
                    updatedCount += questionIds.size();
                }
            } catch (Exception e) {
                log.error("处理数据失败，analysis: {}", analysis, e);
            }
        }

        return updatedCount;
    }
}
