package cn.genn.ai.hub.app.infrastructure.utils.auth.complex;

import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuthParam {

    private SubjectType subjectType;

    private ActionType[] actionType;

    private String resourceKey;

    private ResourceType resourceType;

}
