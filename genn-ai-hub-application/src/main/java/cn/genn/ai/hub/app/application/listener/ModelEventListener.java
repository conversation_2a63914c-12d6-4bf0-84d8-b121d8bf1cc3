package cn.genn.ai.hub.app.application.listener;

import cn.genn.ai.hub.app.application.assembler.ModelManageAssembler;
import cn.genn.ai.hub.app.application.enums.OpType;
import cn.genn.ai.hub.app.application.listener.event.ModelManageEvent;
import cn.genn.ai.hub.app.application.service.SyncAIGatewayCacheService;
import cn.genn.ai.hub.app.infrastructure.repository.config.impl.LLMConfig;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelManageMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelManagePO;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.ai.constant.AIProviderType;
import cn.genn.spring.boot.starter.ai.properties.AIProviderProperties;
import cn.genn.spring.boot.starter.event.redis.component.RedisEventListener;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ModelEventListener extends RedisEventListener<ModelManageEvent> {


    @Resource
    private AIModelManager modelManager;
    @Resource
    private ModelManageMapper manageMapper;
    @Resource
    private ModelManageAssembler mangleAssembler;
    @Resource
    private SyncAIGatewayCacheService syncAIGatewayCacheService;

    @Value("${genn.event.redis.channel-model:model-update}")
    private String channelModel;

    @Override
    protected void onMessage(ModelManageEvent event) {
        ModelManagePO model = manageMapper.selectById(event.getModelId());
        syncAIGatewayCacheService.asyncModel(mangleAssembler.convert(model), event.getType());

        AIProviderProperties properties = getProperties(model);
        if (OpType.isUpdate(event.getType())) {
            // 注册/更新 聊天模型
            switch (model.getType()) {
                case LLM -> {
                    modelManager.registerChatModel(properties);
                }
                case INDEX -> modelManager.registerEmbeddingModel(properties);
                case RERANK -> modelManager.registerRerankModel(properties);
            }
        }

        if (OpType.isDelete(event.getType())) {
            switch (model.getType()) {
                case LLM -> modelManager.deregisterChatModel(properties.getModelKey(), properties.getModelId());
                case INDEX -> modelManager.deregisterEmbeddingModel(properties.getModelKey(), properties.getModelId());
                case RERANK -> modelManager.deregisterRerankModel(properties.getModelKey(), properties.getModelId());
            }
        }
    }

    private AIProviderProperties getProperties(ModelManagePO model) {
        AIProviderProperties properties = new AIProviderProperties();
        properties.setModelId(Long.toString(model.getId()));
        properties.setProviderType(AIProviderType.OPENAI);
        properties.setBaseUrl(model.getBaseUrl());
        properties.setApiKey(model.getApiKey());
        properties.setModelKey(model.getName());
        properties.setModel(model.getModel());
        switch (model.getType()) {
            case LLM -> {
                properties.setCompletionsPath(model.getApiPath());
                LLMConfig llmConfig = (LLMConfig) model.getCommonConfig();
                properties.setVision(llmConfig.getVision() != null && llmConfig.getVision());
                properties.setToolChoice(llmConfig.getToolChoice() != null && llmConfig.getToolChoice());
                properties.setReasoning(llmConfig.getReasoning() != null && llmConfig.getReasoning());
                properties.setFunctionCall(llmConfig.getFunctionCall() != null && llmConfig.getFunctionCall());
            }
            case INDEX -> properties.setEmbeddingsPath(model.getApiPath());
            case RERANK -> properties.setRerankPath(model.getApiPath());
        }
        return properties;
    }

    @Override
    public String getChannel() {
        return channelModel;
    }


}
