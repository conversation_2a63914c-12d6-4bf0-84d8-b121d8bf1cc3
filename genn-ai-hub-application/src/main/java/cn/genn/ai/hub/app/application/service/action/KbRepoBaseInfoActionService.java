package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.api.UserAbilityImpl;
import cn.genn.ai.hub.app.application.command.ResourceInviteCommand;
import cn.genn.ai.hub.app.application.command.ResourceRemoveInviteCommand;
import cn.genn.ai.hub.app.application.command.TagsAssignCommand;
import cn.genn.ai.hub.app.application.dto.TeamInfoDTO;
import cn.genn.ai.hub.app.application.dto.TeamMemberDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FSAppConfig;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoEditCommand;
import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.application.service.query.TeamInfoQueryService;
import cn.genn.ai.hub.app.application.service.query.TeamMemberQueryService;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.event.AuthCommonModifyEvent;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.external.feishu.FSUtil;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ChannelBaseRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TenantChannelRefRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ChannelBasePO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TenantChannelRefPO;
import cn.genn.ai.hub.app.infrastructure.utils.auth.AuthUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import com.lark.oapi.Client;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoBaseInfoActionService {

    private final KbRepoBaseInfoRepositoryImpl baseInfoRepository;
    private final TagsActionService tagsActionService;
    private final SpringEventPublish springEventPublish;
    private final UserAbilityImpl userAbility;
    private final TeamMemberQueryService teamMemberQueryService;
    private final TeamInfoQueryService teamInfoQueryService;
    private final ChannelBaseRepositoryImpl channelBaseRepository;
    private final TenantChannelRefRepositoryImpl tenantChannelRefRepository;
    private final FeishuClientService clientService;

    /**
     * 新建知识库
     *
     * @param command
     * @return
     */
    @Transactional
    public Long createKbRepo(KbRepoBaseInfoCreateCommand command) {
        if (RepoTypeEnum.isFS(command.getRepoType())) {
            if (command.getFeishuServerConfig() == null) {
                throw new BusinessException("飞书配置不能为空");
            }
            FSAppConfig fsConfig = command.getFeishuServerConfig();
            TenantChannelRefPO tenantChannelRefPO = tenantChannelRefRepository.getById(command.getChannelRefId());
            if (tenantChannelRefPO == null) {
                throw new BusinessException("未查询到飞书应用");
            }
            ChannelBasePO channelBasePO = channelBaseRepository.getById(tenantChannelRefPO.getChannelId());
            if (channelBasePO == null) {
                throw new BusinessException("未查询到飞书应用");
            }
            checkFolderToken(fsConfig, channelBasePO);
            command.setChannelId(channelBasePO.getId());
        }

        Long kbId = baseInfoRepository.createKbRepo(command);
        if (CollUtil.isNotEmpty(command.getTagIds())) {
            tagsActionService.updateTagRefByResource(String.valueOf(kbId), TagTypeEnum.KNOWLEDGE, command.getTeamId(), command.getTagIds());
        }
        handlerCreateKbRepoAuth(kbId, command);
        return kbId;
    }

    private void checkFolderToken(FSAppConfig fsConfig, ChannelBasePO channelBasePO) {
        Client client = clientService.getClient(channelBasePO.getUniqueIdentifier());
        String baseUrl = FSUtil.getBaseUrl(fsConfig.getFolderToken());
        String folderToken = FSUtil.getFolderToken(fsConfig.getFolderToken());
        String nodeToken = FSUtil.getFSNodeToken(folderToken, client);
        if(StringUtils.isBlank(nodeToken)){
            throw new BusinessException("无法识别飞书文档地址");
        }
        fsConfig.setAppId(channelBasePO.getUniqueIdentifier());
        fsConfig.setBaseUrl(baseUrl);
        fsConfig.setFolderToken(folderToken);
        fsConfig.setAppId(channelBasePO.getUniqueIdentifier());
    }


    public Boolean editKbRepoDetail(KbRepoBaseInfoEditCommand command) {
        return baseInfoRepository.editKbRepoDetail(command);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteKbRepo(IdCommand command) {
        KbRepoBaseInfoPO kbRepoBaseInfo = baseInfoRepository.getById(command.getId());
        if (kbRepoBaseInfo == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // todo 确认是否要删除以下内容 TODO
        //  知识库、分块数据、索引、数据集、队列任务、文件
        baseInfoRepository.deleteById(command.getId());
        // 通知算法删除数据集下的所有分块
        //删除标签
        tagsActionService.deleteTagRefByResource(String.valueOf(command.getId()), TagTypeEnum.KNOWLEDGE, kbRepoBaseInfo.getTeamId());
        //删除权限
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.KB_REPO,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(command.getId())), this));
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void assignTags(TagsAssignCommand command) {
        KbRepoBaseInfoPO kbRepoBaseInfo = baseInfoRepository.getById(command.getId());
        if (kbRepoBaseInfo == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        tagsActionService.updateTagRefByResource(String.valueOf(command.getId()), TagTypeEnum.KNOWLEDGE, kbRepoBaseInfo.getTeamId(), command.getTagIds());
    }

    public Boolean inviteCollaborator(ResourceInviteCommand command) {
        KbRepoBaseInfoPO kbRepoBaseInfo = baseInfoRepository.getById(command.getId());
        if (kbRepoBaseInfo.getTeamId() == null) {
            throw new CheckException(MessageCode.PERSONAL_SPACE_NOT_INVITE);
        }
        userAbility.checkUserValidByIds(command.getUserIds());
        List<TeamMemberDTO> teamMemberDTOS = teamMemberQueryService.listUserByTeam(kbRepoBaseInfo.getTeamId());
        Set<Long> memberUserIds = teamMemberDTOS.stream().map(TeamMemberDTO::getUserId).collect(Collectors.toSet());
        if (!memberUserIds.containsAll(command.getUserIds())) {
            throw new CheckException(MessageCode.USER_NOT_TEAM_MEMBER);
        }
        springEventPublish.publish(AuthCommonModifyEvent.buildAddEvent(command.getUserIds()
            .stream()
            .map(userId -> AuthUtils.buildUserKbRepoAuth(command.getId(), userId, ActionType.EDIT))
            .collect(Collectors.toList()), this));
        return true;
    }

    public Boolean removeCollaborator(ResourceRemoveInviteCommand command) {
        Long userId = command.getUserId();
        //团队所有者不允许移除
        KbRepoBaseInfoPO kbRepoBaseInfo = baseInfoRepository.getById(command.getId());
        if (kbRepoBaseInfo == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }
        if (kbRepoBaseInfo.getTeamId() == null) {
            throw new CheckException(MessageCode.PERSONAL_SPACE_NOT_REMOVE);
        }
        TeamInfoDTO teamInfoDTO = teamInfoQueryService.get(IdQuery.builder().id(kbRepoBaseInfo.getTeamId()).build());
        if (teamInfoDTO.getOwnerUserId().equals(userId)) {
            throw new CheckException(MessageCode.TEAM_OWNER_NOT_REMOVE);
        }
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.SUBJECT_TYPE, SubjectType.USER,
                AuthUniqueCriteriaEnum.SUBJECT_KEY, String.valueOf(command.getUserId()),
                AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.KB_REPO,
                AuthUniqueCriteriaEnum.ACTION_TYPE, ActionType.EDIT,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(command.getId())), this));
        return true;
    }

    private void handlerCreateKbRepoAuth(Long kbId, KbRepoBaseInfoCreateCommand command) {
        List<AuthCommon> authCommonList = new ArrayList<>();
        authCommonList.add(AuthUtils.buildUserKbRepoAuth(kbId, CurrentUserHolder.getUserId(), ActionType.MANAGER));
        springEventPublish.publish(AuthCommonModifyEvent.buildAddEvent(authCommonList, this));
    }


}

