package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.AgentFeedbackCreateCommand;
import cn.genn.ai.hub.app.application.dto.AgentFeedbackDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentFeedbackPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体反馈转换器
 * @date 2025-07-02
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AgentFeedbackAssembler {

    AgentFeedbackPO toPO(AgentFeedbackCreateCommand command);

    List<AgentFeedbackDTO> toDTOList(List<AgentFeedbackPO> agentFeedbackPOList);
}
