package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 外部渠道查询
 * @date 2025-04-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelQuery extends PageSortQuery {

    @Schema(description = "团队id，如果为空，代表是个人空间创建")
    private Long teamId;

    @Schema(description = "渠道类型, feishu, wechat")
    private ChannelTypeEnum channelType;
}
