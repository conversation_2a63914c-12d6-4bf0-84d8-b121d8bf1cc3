package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.CodeRepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.CodeTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 代码库查询
 * @date 2025-05-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CodeRepoQuery extends PageSortQuery {

    @Schema(description = "团队空间id")
    private Long teamId;

    @Schema(description = "代码库名称")
    private String name;

    @Schema(description = "代码库类型")
    private CodeTypeEnum codeType;

    @Schema(description = "代码库类型 1：系统代码库 2：用户代码库")
    private CodeRepoTypeEnum codeRepoType;
}
