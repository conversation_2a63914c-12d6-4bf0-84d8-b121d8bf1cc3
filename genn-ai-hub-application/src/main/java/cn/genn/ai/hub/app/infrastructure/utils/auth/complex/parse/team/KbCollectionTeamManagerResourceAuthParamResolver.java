package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.KbRepoQaPairCreateCommand;
import cn.genn.ai.hub.app.application.command.KbRepoQaPairUploadCommand;
import cn.genn.ai.hub.app.application.command.KbRepoTermCreateCommand;
import cn.genn.ai.hub.app.application.command.KbRepoTermUploadCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoAfreshTrainCommand;
import cn.genn.ai.hub.app.application.dto.request.*;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCollectionMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbCollectionTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver{

    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;
    private final KbRepoCollectionMapper kbRepoCollectionMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long kbId = switch (uniqueId) {
            case "pageCollection" -> ((KbRepoCollectionQuery) interceptor.getParamsMap().get("query")).getRepoId();
            case "getCollection" -> {
                Long collectionId = ((IdQuery) interceptor.getParamsMap().get("query")).getId();
                yield kbRepoCollectionMapper.selectById(collectionId).getRepoId();
            }
            case "getCollectionList" -> ((KbRepoCollectionListQuery) interceptor.getParamsMap().get("query")).getRepoId();
            case "renameCollection" -> {
                Long collectionId = ((KbRepoCollectionRenameCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoCollectionMapper.selectById(collectionId).getRepoId();
            }
            case "deleteCollection" -> {
                Long collectionId = ((IdCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoCollectionMapper.selectById(collectionId).getRepoId();
            }
            case "updateCollectionStatus" -> {
                Long collectionId = ((KbRepoCollectionUpdateStatusCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoCollectionMapper.selectById(collectionId).getRepoId();
            }
            case "createManualCollection" -> ((KbRepoCollectionManualCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "createTextCollectionOfFile" -> ((KbRepoCollectionTextOfFileCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "createSheetCollection" -> ((KbRepoCollectionSheetCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "afreshTrain" -> ((KbRepoAfreshTrainCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "createTextCollectionOfUrl" -> ((KbRepoCollectionTextOfUrlCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "createTextCollectionOfText" -> ((KbRepoCollectionTextOfTextCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "chunkData" -> ((KbRepoDataChunkRequestCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "updateOrCreateData", "deleteData" -> ((KbRepoDataAndIndexUpdateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "smartChunkSummaryForData" -> ((KbRepoDataSmartChunkSummaryCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "createTerm" -> ((KbRepoTermCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "importTerm" -> ((KbRepoTermUploadCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "importQA" -> ((KbRepoQaPairUploadCommand) interceptor.getParamsMap().get("command")).getRepoId();
            case "createQA" -> ((KbRepoQaPairCreateCommand) interceptor.getParamsMap().get("command")).getRepoId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return kbRepoBaseInfoMapper.selectById(kbId).getTeamId();
    }
}
