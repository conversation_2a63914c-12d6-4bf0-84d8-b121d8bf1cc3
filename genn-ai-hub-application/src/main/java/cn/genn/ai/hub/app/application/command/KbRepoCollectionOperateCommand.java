package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoCollection操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoCollectionOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "父id")
    private Long pid;

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    private Long repoId;

    @Schema(description = "集合名称，用户自定义的集合标识名称")
    private String name;

    @Schema(description = "类型, folder:FOLDER-文件夹, file:FILE-文件, web:WEB-网页, manual_text:MANUAL_TEXT-手动文本")
    private CollectionTypeEnum collectionType;

    @Schema(description = "训练模式, manual:MANUAL-手动, auto:AUTO-自动")
    private TrainingTypeEnum trainingType;

    @Schema(description = "处理方式 直接分段 增强分段 问答拆分")
    private TrainingModelEnum handleType;

    @Schema(description = "分块大小，文本分割的字符数限制（单位：字符）")
    private Integer chunkSize;

    @Schema(description = "分块分隔符，用于文本分割的符号（如换行符）")
    private String chunkSplitter;

    @Schema(description = "处理配置项")
    private String handleConfig;

    @Schema(description = "深度解析开关")
    private Byte deepParse;

    @Schema(description = "标签列表，用于分类和检索集合")
    private String tags;

    @Schema(description = "原始链接，网页或外部文件的原始URL")
    private String rawLink;

    @Schema(description = "外部文件ID，第三方存储系统中的文件标识")
    private String externalFileId;

    @Schema(description = "API文件ID，通过API上传的文件标识")
    private String apiFileId;

    @Schema(description = "外部文件URL，动态鉴权的文件访问链接（支持 {{fileId}} 变量）")
    private String externalFileUrl;

    @Schema(description = "下次同步时间，用于定时同步外部数据源")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime nextSyncTime;

    @Schema(description = "原始文本长度，记录未分割前的文本总长度（单位：字符）")
    private Integer rawTextLength;

    @Schema(description = "原始文本哈希值，用于检测内容变更")
    private String hashRawText;

    @Schema(description = "元数据，包含扩展信息如：webPageSelector（网页选择器）、relatedImgId（关联图片ID）")
    private String metadata;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "数据集处理状态,wait_segment:WAIT_SEGMENT-等待分片,wait_index:WAIT_INDEX-等待索引,processing:PROCESSING-处理中,completed:COMPLETED-已完成,failed_index:FAILED_INDEX-索引失败,failed_segment:FAILED_SEMENT-分片失败,failed_upload:FAILED_UPLOAD-上传失败")
    private CollectionStatusEnum collectionStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

