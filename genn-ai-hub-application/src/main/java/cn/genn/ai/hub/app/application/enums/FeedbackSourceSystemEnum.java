package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 反馈来源系统
 * @date 2025-07-04
 */
@Getter
@AllArgsConstructor
public enum FeedbackSourceSystemEnum {

    CERE_BRO(1, "Cerebro"),
    ENTERPRISE_BRAIN(2, "Enterprise Brain"),
    ;

    @EnumValue
    @JsonValue
    private final Integer type;

    private final String desc;
}
