package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CollectionStatusEnum {

    WAIT_PARSE("wait_parse", "等待解析"),
    PARSING("parsing", "解析中"),
    WAIT_SEGMENT("wait_segment", "等待分片"),
    SEGMENTING("segmenting", "分片中"),
    WAIT_INDEX("wait_index", "等待索引"),
    PROCESSING("processing", "索引处理中"),
    COMPLETED("completed", "已完成"),
    FAILED_INDEX("failed_index", "索引失败"),
    FAILED_SEMENT("failed_segment", "分片失败"),
    FAILED_UPLOAD("failed_upload", "上传失败"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    CollectionStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}

