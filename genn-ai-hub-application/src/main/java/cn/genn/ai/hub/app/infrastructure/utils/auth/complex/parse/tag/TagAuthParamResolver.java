package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.tag;

import cn.genn.ai.hub.app.application.command.TagsCreateCommand;
import cn.genn.ai.hub.app.application.command.TagsUpdateCommand;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.TagsMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.TagsPO;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParamResolver;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TagAuthParamResolver implements AuthParamResolver {

    public static final String ADD_TAG = "addTag";
    public static final String DELETE_TAG = "deleteTag";
    public static final String UPDATE_TAG = "updateTag";
    private final TagsMapper tagsMapper;

    @Override
    public List<AuthParam> authParams(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        switch (uniqueId) {
            case ADD_TAG:
                TagsCreateCommand createCommand = (TagsCreateCommand) interceptor.getParamsMap().get("command");
                validTagType(createCommand.getTagType());
                if (createCommand.getTeamId() != null) {
                    return buildTeamAuth(createCommand.getTeamId());
                }
                return null;
            case UPDATE_TAG:
                TagsUpdateCommand updateCommand = (TagsUpdateCommand) interceptor.getParamsMap().get("command");
                TagsPO tagsPO = tagsMapper.selectById(updateCommand.getId());
                if (tagsPO.getTeamId() != null) {
                    return buildTeamAuth(tagsPO.getTeamId());
                }
                checkUserAuth(tagsPO.getCreateUserId());
                return null;
            case DELETE_TAG:
                IdCommand command = (IdCommand) interceptor.getParamsMap().get("command");
                TagsPO tags = tagsMapper.selectById(command.getId());
                if (tags.getTeamId() != null) {
                    return buildTeamAuth(tags.getTeamId());
                }
                checkUserAuth(tags.getCreateUserId());
                return null;
        }
        return null;
    }

    private void checkUserAuth(Long createUserId) {
        if (!Objects.equals(createUserId, CurrentUserHolder.getUserId())) {
            throw new CheckException(MessageCode.AUTH_API_AUTH_FAILED);
        }
    }

    private List<AuthParam> buildTeamAuth(Long teamId) {
        return List.of(AuthParam.builder()
            .subjectType(SubjectType.USER)
            .resourceType(ResourceType.TEAM)
            .resourceKey(teamId.toString())
            .actionType(new ActionType[]{ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT})
            .build());
    }

    private void validTagType(TagTypeEnum tagType) {
        if (tagType == TagTypeEnum.GENERAL_TOOL) {
            throw new CheckException(MessageCode.TAG_TYPE_GENERAL_TOOL_NOT_SUPPORT);
        }
    }
}
