package cn.genn.ai.hub.app.infrastructure.config.auth;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface CheckAuth {

    /**
     * 是否启用权限校验
     * @return
     */
    boolean enable() default true;

    /**
     * 是否检查数据权限
     * @return
     */
    boolean checkDataAuth() default true;

    /**
     * 图表 id 的 spel 表达式
     * 仅在 checkDataAuth 为 true 时有效
     * @return
     */
    String chartKeyExpression() default "";

    /**
     * 是否只检查数据集权限
     * @return
     */
    boolean onlyDatasetAuth() default false;

    /**
     * 数据集 id 的 spel 表达式
     * @return
     */
    String datasetExpression() default "";

}
