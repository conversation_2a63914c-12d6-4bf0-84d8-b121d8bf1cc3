package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb;

import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbRepoDataAuthParamResolver extends AbstractRepoAuthParamResolver {

    private final KbRepDataRepositoryImpl dataRepository;
    @Override
    protected Long getRepoIdById(Long id) {
        KbRepoDataPO kbRepoDataPO = dataRepository.getById(id);
        return kbRepoDataPO.getRepoId();
    }
}
