package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.PromptOptimizeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 提示词优化命令
 * @date 2025-05-12
 */
@Data
public class PromptOptimizeCommand {

    @Schema(description = "用户输入")
    private String userInput;

    @Schema(description = "原始提示词内容")
    private String originalPrompt;

    @Schema(description = "提示词优化类型: 1-提示词生成 2-提示词优化 3-根据调试结果优化")
    private PromptOptimizeTypeEnum optimizeType;

    @Schema(description = "不符合预期的反馈")
    private String feedbackNegative;

    @Schema(description = "预期的反馈")
    private String feedbackExpectation;
}
