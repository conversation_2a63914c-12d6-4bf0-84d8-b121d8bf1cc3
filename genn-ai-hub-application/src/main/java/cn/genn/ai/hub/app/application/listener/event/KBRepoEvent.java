package cn.genn.ai.hub.app.application.listener.event;

import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.spring.boot.starter.event.rocketmq.model.RocketMQBaseEvent;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KBRepoEvent extends RocketMQBaseEvent<KBRepoEvent> {

    /**
     * 任务类型
     */
    private TaskTypeEnum type;

    /**
     * 任务ID
     */
    private String taskId;

    @Override
    protected KBRepoEvent self() {
        return this;
    }
}

