package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoData操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoDataOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "数据类型 文本 图片 公式 表格")
    private String dataType;

    @Schema(description = "数据分块唯一标识")
    private String dataKey;

    @Schema(description = "知识库 ID，标识数据所属的知识库")
    private Long repoId;

    @Schema(description = "知识集合 ID，与 collection 表关联，表示数据所属的集合")
    private Long collectionId;

    @Schema(description = "问题文本（Question），用于嵌入或 QA 生成的核心内容")
    private String question;

    @Schema(description = "答案文本（Answer），作为辅助内容或结构化提示的补充信息")
    private String answer;

    @Schema(description = "分块相对位置，表示数据在原始文档中的分块位置（从 0 开始）")
    private Integer chunkIndex;

    @Schema(description = "权重，控制数据在训练或检索中的优先级（数值越高优先级越高）")
    private Integer weight;

    @Schema(description = "算法处理状态,0:WAIT-未处理, 2:PROCESSING-处理中,2:DONE-已处理")
    private HandleStatusEnum handleStatus;

    @Schema(description = "重试次数，用于处理数据预处理失败时的重试机制")
    private Integer retryCount;

    @Schema(description = "扩展数据 例如 图片的url")
    private String extData;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

