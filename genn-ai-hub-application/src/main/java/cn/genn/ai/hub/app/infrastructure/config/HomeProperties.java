package cn.genn.ai.hub.app.infrastructure.config;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class HomeProperties {

    /**
     * Deep research configuration
     */
    private Map<Long, String> deepResearchConfig = new HashMap<>();

    /**
     * 格物智能体列表
     * key: 租户id
     * value: 智能体名称和workflowId的映射
     */
    private Map<Long, Map<String, String>> goVowAgentMap = new HashMap<>();
}
