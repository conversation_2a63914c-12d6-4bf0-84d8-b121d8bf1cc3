package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.TeamInfoAssembler;
import cn.genn.ai.hub.app.application.dto.TeamInfoDTO;
import cn.genn.ai.hub.app.application.query.TeamInfoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.TeamInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TeamUserRefRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamInfoPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.GennUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TeamInfoQueryService {

    private final TeamInfoMapper mapper;
    private final TeamInfoAssembler assembler;
    private final TeamUserRefRepositoryImpl teamUserRefRepository;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return TeamInfoDTO分页对象
     */
    public PageResultDTO<TeamInfoDTO> page(TeamInfoQuery query) {
        // 获取当前用户ID
        Long currentUserId = CurrentUserHolder.getUserId();

        // 构建查询条件
        LambdaQueryWrapper<TeamInfoPO> queryWrapper = new LambdaQueryWrapper<>();

        // 基本条件
        queryWrapper.eq(query.getId() != null, TeamInfoPO::getId, query.getId())
                .like(query.getName() != null, TeamInfoPO::getName, query.getName())
                .eq(TeamInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED);

        // 处理团队查询类型
        if (query.getQueryType() != null) {
            switch (query.getQueryType()) {
                case OWNER_BY_ME:
                    // 我拥有的团队
                    queryWrapper.eq(TeamInfoPO::getOwnerUserId, currentUserId);
                    break;
                case JOINED_BY_ME:
                    // 我加入的团队
                    Set<Long> joinedTeamIds = teamUserRefRepository.getJoinedTeamIds(currentUserId);
                    if (joinedTeamIds.isEmpty()) {
                        // 如果没有加入任何团队，返回空结果
                        return PageResultDTO.empty();
                    }
                    queryWrapper.in(TeamInfoPO::getId, joinedTeamIds);
                    break;
            }
        }

        // 执行分页查询
        Page<TeamInfoPO> page = mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper);
        PageResultDTO<TeamInfoDTO> pageResult = assembler.toPageResult(page);

        // 如果没有数据，直接返回
        if (GennUtils.pageIsEmpty(pageResult)) {
            return pageResult;
        }

        // 获取团队ID列表
        List<Long> teamIds = pageResult.getList().stream()
                .map(TeamInfoDTO::getId)
                .collect(Collectors.toList());

        // 批量获取团队成员数量
        Map<Long, Long> memberCountMap = teamUserRefRepository.batchCountTeamMembers(teamIds);

        // 获取当前用户加入的团队ID列表
        Set<Long> userJoinedTeamIds = teamUserRefRepository.getJoinedTeamIds(currentUserId);

        // 补充团队成员数量和当前用户与团队的关系
        pageResult.getList().forEach(team -> {
            // 设置成员数量
            team.setMemberCount(memberCountMap.getOrDefault(team.getId(), 0L));
            // 设置当前用户是否是创建者
            team.setIsCreator(currentUserId.equals(team.getOwnerUserId()));
            // 设置当前用户是否是成员
            team.setIsMember(userJoinedTeamIds.contains(team.getId()));
        });

        return pageResult;
    }


    public List<TeamInfoDTO> listJoin() {
        Set<Long> joinedTeamIds = teamUserRefRepository.getJoinedTeamIds(CurrentUserHolder.getUserId());
        if (joinedTeamIds.isEmpty()) {
            // 如果没有加入任何团队，返回空结果
            return Collections.emptyList();
        }
        List<TeamInfoDTO> teamInfoDTOS = assembler.PO2DTO(mapper.selectList(new LambdaQueryWrapper<TeamInfoPO>().in(TeamInfoPO::getId, joinedTeamIds)));
        teamInfoDTOS.forEach(team -> {
            // 设置当前用户是否是创建者
            team.setIsCreator(CurrentUserHolder.getUserId().equals(team.getOwnerUserId()));
            // 设置当前用户是否是成员
            team.setIsMember(joinedTeamIds.contains(team.getId()));
        });
        return teamInfoDTOS;
    }

    /**
     * 根据id查询
     *
     * @param query ID查询条件
     * @return TeamInfoDTO
     */
    public TeamInfoDTO get(IdQuery query) {
        // 获取当前用户ID
        Long currentUserId = CurrentUserHolder.getUserId();

        // 获取团队信息
        TeamInfoDTO teamInfoDTO = assembler.PO2DTO(mapper.selectById(query.getId()));

        if (teamInfoDTO != null) {
            // 设置成员数量
            teamInfoDTO.setMemberCount(teamUserRefRepository.countTeamMembers(teamInfoDTO.getId()));
            // 设置当前用户是否是创建者
            teamInfoDTO.setIsCreator(currentUserId.equals(teamInfoDTO.getOwnerUserId()));
            // 设置当前用户是否是成员
            Set<Long> userJoinedTeamIds = teamUserRefRepository.getJoinedTeamIds(currentUserId);
            teamInfoDTO.setIsMember(userJoinedTeamIds.contains(teamInfoDTO.getId()));
        }
        return teamInfoDTO;
    }

}
