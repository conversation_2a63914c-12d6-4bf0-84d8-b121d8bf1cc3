package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.CodeRepoSaveCommand;
import cn.genn.ai.hub.app.application.dto.CodeRepoDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.CodeRepoPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 代码库转换器
 * @date 2025-05-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CodeRepoAssembler {

    CodeRepoPO toPO(CodeRepoSaveCommand saveCommand);

    List<CodeRepoDTO> toDTO(List<CodeRepoPO> codeRepoPO);
}
