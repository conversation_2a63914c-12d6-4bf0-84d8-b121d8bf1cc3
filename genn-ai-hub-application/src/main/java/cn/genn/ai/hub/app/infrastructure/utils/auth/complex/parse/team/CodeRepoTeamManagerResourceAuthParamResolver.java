package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.CodeRepoSaveCommand;
import cn.genn.ai.hub.app.application.command.SensitiveWordSaveCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.CodeRepoMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 代码库团队管理资源鉴权
 * @date 2025-05-19
 */
@Component
@RequiredArgsConstructor
public class CodeRepoTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver {

    private final CodeRepoMapper codeRepoMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long promptId = switch (uniqueId) {
            case "updateCodeRepo" -> ((CodeRepoSaveCommand) interceptor.getParamsMap().get("command")).getId();
            case "deleteCodeRepo" -> ((IdCommand) interceptor.getParamsMap().get("command")).getId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return codeRepoMapper.selectById(promptId).getTeamId();
    }
}
