package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * QuestionInfo查询对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionInfoQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "问题ID列表")
    private List<Long> ids;

    @Schema(description = "工作流id")
    private String appId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "会话的关联id")
    private String chatId;

    @Schema(description = "一次问答关联id")
    private String taskId;


}

