package cn.genn.ai.hub.app.application.processor;

import cn.genn.ai.hub.app.application.dto.feishu.SheetCellRespBody;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.reflect.TypeToken;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Slf4j
@Component
public class AIHubInvokeService {
    @Resource
    private RestTemplate restTemplate;

    public <T> ResponseResult<T> fetchPost(String url, String requestBody, TypeToken<T> responseType) throws Exception {
        try {
            log.info("invoke url: {}, body: {}", url, requestBody);
            HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, this.fetchJsonHeader(""));
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("invoke, result status code: {}", responseEntity.getStatusCode());

            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                log.error("invoke, failed with status code: {}", responseEntity.getStatusCode());
                throw new BusinessException("Failed to invoke API with status code: " + responseEntity.getStatusCode());
            }
            log.info("invoke, result: {}", responseEntity.getBody());
            return handleResponse(responseEntity, responseType);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("invoke, error", e);
            throw new Exception("Error invoking API", e);
        }
    }

    private <T> ResponseResult<T> handleResponse(ResponseEntity<String> responseEntity, TypeToken<T> responseType) {
        if (HttpStatus.OK != responseEntity.getStatusCode()) {
            throw new BusinessException(MessageCode.HTTP_ERROR);
        }
        log.info("远程调用, result: {}", responseEntity.getBody());
        TypeReference<ResponseResult<T>> typeRef = new TypeReference<ResponseResult<T>>() {
        };
        ResponseResult<T> responseResult = JsonUtils.parse(responseEntity.getBody(), typeRef);
        if (Objects.isNull(responseResult)) {
            throw new BusinessException(MessageCode.HTTP_ERROR);
        }
        if (!responseResult.isSuccess()) {
            throw new BusinessException(MessageCode.HTTP_ERROR, responseResult.getMsg());
        }
        return responseResult;
    }


    public HttpHeaders fetchJsonHeader(String authorization) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        if (authorization != null && !authorization.isEmpty()) {
            headers.add("Authorization", authorization);
        }
        return headers;
    }

    public SheetCellRespBody fetchFSGet(String url, String authorization) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Authorization", "Bearer " + authorization);
        HttpEntity<String> httpEntity = new HttpEntity<>(headers);

        try {
            ResponseEntity<SheetCellRespBody> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, SheetCellRespBody.class);
            log.info("invoke, result status code: {}", responseEntity.getStatusCode());

            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                log.error("invoke, failed with status code: {}", responseEntity.getStatusCode());
                throw new BusinessException("Failed to invoke API with status code: " + responseEntity.getStatusCode());
            }

            log.info("invoke, result: {}", responseEntity.getBody());
            return responseEntity.getBody();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("invoke, error", e);
            throw new BusinessException(e.getMessage());
        }
    }
}
