package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * KnowledgeGapAnalysis操作对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionGapAnalysisCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联问题id")
    private Long questionId;

    @Schema(description = "工作流id")
    private String appId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "会话的关联id")
    private String chatId;

    @Schema(description = "一次问答关联id")
    private String taskId;

    @Schema(description = "知识缺口标签(类型)")
    private String type;

    @Schema(description = "缺失内容(json)")
    private String missingPoints;

    @Schema(description = "缺失内容总结")
    private String summary;


}

