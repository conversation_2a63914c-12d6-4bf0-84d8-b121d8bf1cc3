package cn.genn.ai.hub.app.application.assembler;

import java.util.List;

import cn.genn.ai.hub.app.application.command.KbRepoTermEditCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import cn.genn.ai.hub.app.application.dto.KbRepoTermDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTermExcelImportDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoTermQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTermPO;
import cn.genn.core.model.assembler.QueryAssembler;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoTermAssembler extends QueryAssembler<KbRepoTermQuery, KbRepoTermPO, KbRepoTermDTO>{

    KbRepoTermAssembler INSTANCE = Mappers.getMapper(KbRepoTermAssembler.class);

    KbRepoTermPO DTO2PO(KbRepoTermDTO dto);

    List<KbRepoTermPO> kbRepoTermExcelImportDTO2PO(List<KbRepoTermExcelImportDTO> dataList);

    @Mapping(target = "nameAliases", expression = "java(java.util.stream.Stream.of(dto.getAlias1(), dto.getAlias2(), dto.getAlias3()).filter(cn.hutool.core.util.StrUtil::isNotBlank).collect(java.util.stream.Collectors.toList()))")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "description", source = "description")
    KbRepoTermDTO kbRepoTermExcelImportDTO2PO(KbRepoTermExcelImportDTO dto);

    KbRepoTermPO kbRepoTermEditCommand2KbRepoTermPO(KbRepoTermEditCommand command);
}

