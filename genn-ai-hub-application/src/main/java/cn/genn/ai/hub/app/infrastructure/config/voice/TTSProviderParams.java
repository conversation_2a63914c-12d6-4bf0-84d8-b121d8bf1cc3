package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 */
@Data
public class TTSProviderParams {

    @NestedConfigurationProperty
    private App app = new App();
    @NestedConfigurationProperty
    private Audio audio = new Audio();
    @NestedConfigurationProperty
    private Additions additions;
    private String resourceId = "volc.service_type.10029";

    private Boolean stream;
    private String languageBooststring;


    @Data
    public static class App {
        private String appId = "**********";
        private String cluster;
        private String token = "S7wi1qMspgog-_8ZH4n0hdf-7RD2_8ob";
    }

    @Data
    public static class Audio {
        String voiceType = "zh_male_wennuanahu_moon_bigtts";
        Float speedRatio;
        Float volumeRatio;
        Float pitchRatio;
        Integer pitchRate;
        Integer speechRate;
    }

    @Data
    public class Additions {
        Boolean enableLatexTn;
        Boolean disableMarkdownFilter;
        Boolean enableLanguageDetector;
    }
}
