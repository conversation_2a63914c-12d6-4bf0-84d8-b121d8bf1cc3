package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.KbRepoQaPairAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoQaPairDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoQaPairQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoQaPairMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoQaPairQueryService {

    private final KbRepoQaPairMapper mapper;
    private final KbRepoQaPairAssembler assembler;
    private final IUpmUserService upmUserService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoQaPairDTO分页对象
     */
    public PageResultDTO<KbRepoQaPairDTO> page(KbRepoQaPairQuery query) {
        QueryWrapper<KbRepoQaPairPO> queryWrapper = buildQuery(query);
        PageResultDTO<KbRepoQaPairDTO> result = assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
        List<KbRepoQaPairDTO> dataList = result.getList();
        if (CollectionUtil.isNotEmpty(dataList)) {
            // 补充下用户头像
            UpmUserQuery upmUserQuery = new UpmUserQuery();
            List<Long> userIdList = dataList.stream().map(KbRepoQaPairDTO::getCreateUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            upmUserQuery.setUserIdList(userIdList);
            List<UpmUserDTO> upmUserDTOList = upmUserService.conditionList(upmUserQuery);
            Map<Long, UpmUserDTO> resultMap = upmUserDTOList.stream()
                .collect(Collectors.toMap(UpmUserDTO::getId, Function.identity(), (k1, k2) -> k2));
            dataList.forEach(item -> {
                if (Objects.nonNull(item.getCreateUserId())) {
                    UpmUserDTO userDTO = resultMap.get(item.getCreateUserId());
                    if (Objects.nonNull(userDTO)) {
                        item.setNick(userDTO.getNick());
                        item.setAvatar(userDTO.getAvatar());
                    }
                }
            });
        }
        return result;
    }

    private QueryWrapper<KbRepoQaPairPO> buildQuery(KbRepoQaPairQuery query) {
        QueryWrapper<KbRepoQaPairPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            .eq(Objects.nonNull(query.getRepoId()), KbRepoQaPairPO::getRepoId, query.getRepoId())
            .and(StrUtil.isNotBlank(query.getKeyword()), wrapper -> wrapper
                .like(KbRepoQaPairPO::getQuestion, query.getKeyword())
                .or()
                .like(KbRepoQaPairPO::getSimilarQuestions, query.getKeyword())
                .or()
                .like(KbRepoQaPairPO::getAnswer, query.getKeyword()))
            .eq(KbRepoQaPairPO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByDesc(KbRepoQaPairPO::getCreateTime);
        return queryWrapper;
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoQaPairDTO
     */
    @IgnoreTenant
    public KbRepoQaPairDTO get(IdQuery query) {
        return assembler.PO2DTO(mapper.selectById(query.getId()));
    }

    @IgnoreTenant
    public KbRepoQaPairDTO getByQaPairKey(String qaPairKey) {
        QueryWrapper<KbRepoQaPairPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            .eq(KbRepoQaPairPO::getQaPairKey, qaPairKey)
            .eq(KbRepoQaPairPO::getDeleted, DeletedEnum.NOT_DELETED);
        return assembler.PO2DTO(mapper.selectOne(queryWrapper));
    }
}

