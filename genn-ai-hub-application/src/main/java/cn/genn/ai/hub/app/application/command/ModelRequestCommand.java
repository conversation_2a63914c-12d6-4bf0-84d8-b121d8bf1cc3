package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ModelRequestCommand {

    @Schema(description = "模型请求参数")
    private String requestBody;

    @Schema(description = "对话渠道相关信息")
    private ChatInfo chatInfo;


    @Data
    public static class ChatInfo {
        /**
         * 应用唯一标识
         */
        private String appId;

        /**
         * 会话唯一标识
         */
        private String chatId;

        /**
         * 数据唯一标识
         */
        private String dataId;

        /**
         * 响应节点唯一标识
         */
        private String respId;

        /**
         * 渠道唯一ID
         */
        private String channelId;

        /**
         * 渠道类型
         */
        private String channelType;

    }
}
