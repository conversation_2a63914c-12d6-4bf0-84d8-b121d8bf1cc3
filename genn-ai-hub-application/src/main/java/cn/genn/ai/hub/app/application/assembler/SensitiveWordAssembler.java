package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.SensitiveWordSaveCommand;
import cn.genn.ai.hub.app.application.dto.SensitiveWordDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.SensitiveWordPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 敏感词转换器
 * @date 2025-05-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SensitiveWordAssembler {

    SensitiveWordPO toPO(SensitiveWordSaveCommand command);

    List<SensitiveWordDTO> toDTO(List<SensitiveWordPO> po);
}
