package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.IdListCommand;
import cn.genn.ai.hub.app.application.command.KbRepoTermEditCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoTermUpdateStatusCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoTermMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbTermTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver{

    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;
    private final KbRepoTermMapper kbRepoTermMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long kbId = switch (uniqueId) {
            case "updateTermStatus" -> {
                Long termId = ((KbRepoTermUpdateStatusCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoTermMapper.selectById(termId).getRepoId();
            }
            case "editTerm" -> {
                Long termId = ((KbRepoTermEditCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoTermMapper.selectById(termId).getRepoId();
            }
            case "batchDeleteTerm" -> {
                Long termId = ((IdListCommand) interceptor.getParamsMap().get("command")).getIds().getFirst();
                yield kbRepoTermMapper.selectById(termId).getRepoId();
            }
            case "deleteTerm" -> {
                Long termId = ((IdCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoTermMapper.selectById(termId).getRepoId();
            }
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return kbRepoBaseInfoMapper.selectById(kbId).getTeamId();
    }
}
