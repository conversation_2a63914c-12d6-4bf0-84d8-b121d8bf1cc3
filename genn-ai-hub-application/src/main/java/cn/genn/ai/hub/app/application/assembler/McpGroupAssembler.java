package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.McpGroupCreateCommand;
import cn.genn.ai.hub.app.application.command.McpGroupUpdateCommand;
import cn.genn.ai.hub.app.application.dto.mcp.McpGroupDTO;
import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroup;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpGroupPO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * MCP分组装配器，负责DTO与领域实体之间的转换
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface McpGroupAssembler {

    McpGroupAssembler INSTANCE = Mappers.getMapper(McpGroupAssembler.class);

    /**
     * 将领域实体转换为DTO
     *
     * @param mcpGroup 领域实体
     * @return DTO
     */
    McpGroupDTO toDTO(McpGroup mcpGroup);

    /**
     * 将DTO转换为领域实体
     *
     * @param mcpGroupDTO DTO
     * @return 领域实体
     */
    McpGroup toEntity(McpGroupDTO mcpGroupDTO);

    /**
     * 将创建命令转换为领域实体
     *
     * @param command 创建命令
     * @return 领域实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "groupKey", ignore = true)
    McpGroup toEntity(McpGroupCreateCommand command);

    /**
     * 将更新命令转换为领域实体
     *
     * @param command 更新命令
     * @return 领域实体
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "groupKey", ignore = true)
    McpGroup toEntity(McpGroupUpdateCommand command);

    /**
     * 将分页结果转换为DTO分页结果
     *
     * @param page 分页结果
     * @return DTO分页结果
     */
    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<McpGroupDTO> toPageResult(IPage<McpGroupPO> page);
}
