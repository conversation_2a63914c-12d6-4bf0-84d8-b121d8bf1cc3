package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.ToolSourceEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * ToolInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ToolInfoQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "工具key")
    private String toolKey;

    @Schema(description = "标签id列表")
    private List<Long> tagIds;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "工具来源，web：WEB-前端工具, server：SERVER-后端工具")
    private ToolSourceEnum toolSource;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String intro;



}

