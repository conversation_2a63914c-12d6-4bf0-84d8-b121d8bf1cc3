package cn.genn.ai.hub.app.application.service.action;


import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.dto.callback.KbRepoChunkCallbackCommand;
import cn.genn.ai.hub.app.application.dto.callback.KbRepoFileParseCallbackCommand;
import cn.genn.ai.hub.app.application.dto.callback.KbRepoIndexVectorCallbackCommand;
import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.listener.event.KBRepoEvent;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.application.service.query.KbRepoCollectionQueryService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataIndexQueryService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataQueryService;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.ai.hub.app.infrastructure.config.MQSendTopicConfigs;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTaskPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.event.rocketmq.component.RocketMQEventPublish;
import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoTaskActionService {

    private final RepoTaskRepositoryImpl repoTaskRepository;
    private final RocketMQEventPublish rocketMQEventPublish;
    private final MQSendTopicConfigs topicConfigs;
    private final TaskHandlerFactory handlerFactory;
    protected final RepoTaskRepositoryImpl taskRepository;
    private final KbRepoCollectionActionService collectionActionService;
    private final KbRepoCollectionQueryService collectionQueryService;
    private final KbRepoDataActionService dataActionService;
    private final KbRepoDataQueryService dataQueryService;
    private final KbRepoDataIndexActionService dataIndexActionService;
    private final KbRepoDataIndexQueryService dataIndexQueryService;
    private final KbRepoBaseInfoRepositoryImpl repoBaseInfoRepository;

    /**
     * 新增一条解析文件任务
     */
    @Transactional
    public Long createTaskOfFileParse(KbRepoTaskOfFileParseBody body) {
        return createTask(body.getTenantId(), TaskTypeEnum.FILE_PARSE, body.getRepoId(), body.getFileId().toString(), body);
    }

    /**
     * 新增一条文本分块任务
     */
    @Transactional
    public Long createTaskOfTextChunk(KbRepoTaskOfTextChunkBody body) {
        return createTask(body.getTenantId(), TaskTypeEnum.TEXT_CHUNK, body.getRepoId(), body.getCollectionId().toString(), body);
    }

    /**
     * 新增一条智能分块摘要任务
     */
    @Transactional
    public Long createTaskOfSmartChunkSummary(KbRepoTaskOfSmartChunkSummaryBody body) {
        return createTask(body.getTenantId(), TaskTypeEnum.SMART_CHUNK_SUMMARY, body.getRepoId(), body.getCollectionId().toString(), body);
    }

    /**
     * 新增一条索引向量任务
     */
    @Transactional
    public Long createTaskOfIndexVector(KbRepoTaskOfIndexVectorBody body) {
        return createTask(body.getTenantId(), TaskTypeEnum.INDEX_VECTOR, body.getRepoId(), body.getIndexKey(), body);
    }

    /**
     * 新增一条问答对向量任务
     */
    @Transactional
    public Long createTaskOfQaPairVector(KbRepoTaskOfQaPairVectorBody body) {
        return createTask(body.getTenantId(), TaskTypeEnum.QA_PAIR_VECTOR, body.getRepoId(), body.getQaPairKey(), body);
    }

    /**
     * 创建任务并添加到队列中
     *
     * @param taskType    任务类型
     * @param businessKey 业务键
     * @param body        任务体
     * @return 任务ID
     */
    private <T> Long createTask(Long tenantId, TaskTypeEnum taskType, Long repoId, String businessKey, T body) {
        KbRepoTaskPO po = KbRepoTaskPO.builder()
            .tenantId(tenantId)
            .repoId(repoId)
            .taskType(taskType)
            .businessKey(businessKey)
            .businessBody(JsonUtils.toJson(body))
            .taskStatus(TaskStatusEnum.PENDING)
            .executeCount(0)
            .build();
        boolean result = repoTaskRepository.save(po);
        if (!result) {
            log.error("新增任务失败");
            throw new RuntimeException("新增任务失败");
        }
        Long taskId = po.getId();
        TransactionSynchronizationManager.registerSynchronization(new org.springframework.transaction.support.TransactionSynchronization() {
            @Override
            public void afterCommit() {
                KBRepoEvent adjustMQEvent = KBRepoEvent
                    .builder()
                    .type(taskType)
                    .taskId(Long.toString(taskId))
                    .build();
                adjustMQEvent.setTenantId(String.valueOf(tenantId));
                rocketMQEventPublish.publish(topicConfigs.getGennAiHubAppTaskAnalysis(), adjustMQEvent);
            }
        });

        return taskId;
    }

    /**
     * 文件解析回调
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object fileParseCallback(KbRepoFileParseCallbackCommand command) {
        KbRepoTaskDTO task = taskRepository.getInfo(Long.parseLong(command.getTaskId()));
        if (task == null) {
            return null;
        }
        GennRequestContext.initTenantId(task.getTenantId());
        // 更新collection状态为 等待分片
        collectionActionService.updateCollectionStatus(Long.parseLong(command.getCollectionId()), CollectionStatusEnum.WAIT_SEGMENT);
        // 创建这个文件的对应的分块任务
        KbRepoTaskOfFileParseBody fileParseBody = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfFileParseBody.class);
        KbRepoTaskOfTextChunkBody kbRepoTaskOfTextChunkBody = KbRepoTaskOfTextChunkBody.builder()
            .tenantId(task.getTenantId())
            .repoId(Long.parseLong(command.getRepoId()))
            .collectionId(Long.parseLong(command.getCollectionId()))
            .fileId(Long.parseLong(command.getFileId()))
            .contentType(fileParseBody.getContentType())
            .smartChunkSummary(fileParseBody.getSmartChunkSummary())
            .build();
        createTaskOfTextChunk(kbRepoTaskOfTextChunkBody);
        // 更新数据库任务表状态
        taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
        // 移除进行队列任务
        handlerFactory.removeProcessTask(task);
        GennRequestContext.clear();
        return null;
    }

    /**
     * 分块结果回调
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object textChunkCallback(KbRepoChunkCallbackCommand command) {
        Long repoId = Long.parseLong(command.getRepoId());
        Long collectionId = Long.parseLong(command.getCollectionId());
        KbRepoTaskDTO task = taskRepository.getInfo(Long.parseLong(command.getTaskId()));
        if (task == null) {
            return null;
        }
        GennRequestContext.initTenantId(task.getTenantId());
        KbRepoTaskOfTextChunkBody textChunkBody = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfTextChunkBody.class);
        KbRepoBaseInfoPO repoBaseInfoPO = repoBaseInfoRepository.getById(repoId);
        if (Objects.isNull(repoBaseInfoPO)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 如果已经确认接到了collection下面的所有的分块结果
        Long dataCount = dataQueryService.countByRepoIdAndCollectionId(Long.parseLong(command.getRepoId()), Long.parseLong(command.getCollectionId()));
        log.info("分块结果回调 现有数据库的分块数:{} command.getTotalChunkSize: {}", dataCount, command.getTotalChunkSize());
        // 将分块结果分别录入数据库 没录完所有的分块时候不触发进行索引
        if (dataCount < command.getTotalChunkSize()) {
            dataActionService.saveTextChunkCallbackData(command);
        } else {
            log.info("分块结果回调 库里的分块已经全部接收完成 不做任何操作");
            return null;
        }
        dataCount = dataQueryService.countByRepoIdAndCollectionId(Long.parseLong(command.getRepoId()), Long.parseLong(command.getCollectionId()));
        if (dataCount < command.getTotalChunkSize()) {
            log.info("分块结果回调 已经录入数据库的分块数:{} command.getTotalChunkSize: {}", dataCount, command.getTotalChunkSize());
            log.info("分块结果回调 没有接收完所有的分块结果 不进行下一步");
            // 没有接收完所有的分块结果 不进行下一步
            return null;
        }
        log.info("分块结果回调 已经接收完所有的分块结果,开始处理向量索引化");
        // 创建分块下所有的多个索引任务
        List<KbRepoDataIndexDTO> dataIndexDtoList = dataIndexQueryService.getByCollectionId(repoId, collectionId);
        for (KbRepoDataIndexDTO dataIndexDto : dataIndexDtoList) {
            // 是否需要生成分块摘要 如果需要智能分块摘要 需要先生成摘要后再进行序列化
            if (Boolean.TRUE.equals(textChunkBody.getSmartChunkSummary())) {
                KbRepoTaskOfSmartChunkSummaryBody kbRepoTaskOfSmartChunkSummaryBody = KbRepoTaskOfSmartChunkSummaryBody.builder()
                    .tenantId(task.getTenantId())
                    .repoId(repoId)
                    .agentModelKey(repoBaseInfoPO.getAgentModelKey())
                    .collectionId(collectionId)
                    .dataKey(dataIndexDto.getDataKey())
                    .indexKey(dataIndexDto.getIndexKey())
                    .vectorModelKey(repoBaseInfoPO.getVectorModelKey())
                    .build();
                createTaskOfSmartChunkSummary(kbRepoTaskOfSmartChunkSummaryBody);
            } else {
                KbRepoTaskOfIndexVectorBody kbRepoTaskOfIndexVectorBody = KbRepoTaskOfIndexVectorBody.builder()
                    .tenantId(task.getTenantId())
                    .repoId(repoId)
                    .collectionId(collectionId)
                    .dataKey(dataIndexDto.getDataKey())
                    .indexKey(dataIndexDto.getIndexKey())
                    .rawText(dataIndexActionService.handleRawText(dataIndexDto.getDataKey(), dataIndexDto.getIndexKey()))
                    .vectorModelKey(repoBaseInfoPO.getVectorModelKey())
                    .build();
                createTaskOfIndexVector(kbRepoTaskOfIndexVectorBody);
            }
        }
        // 更新collection状态为 等待索引 CollectionStatusEnum.WAIT_INDEX
        collectionActionService.updateCollectionStatus(Long.parseLong(command.getCollectionId()), CollectionStatusEnum.WAIT_INDEX);
        // 更新数据库任务表状态
        taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
        // 移除进行队列任务
        handlerFactory.removeProcessTask(task);
        GennRequestContext.clear();
        return null;
    }

    /**
     * 向量化完成回调
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object indexVectorCallback(KbRepoIndexVectorCallbackCommand command) {
        Long repoId = Long.parseLong(command.getRepoId());
        Long collectionId = Long.parseLong(command.getCollectionId());
        KbRepoTaskDTO task = taskRepository.getInfo(Long.parseLong(command.getTaskId()));
        if (task == null) {
            return null;
        }
        GennRequestContext.initTenantId(task.getTenantId());
        // 更新索引的处理状态为完成
        List<String> dataKeyListOfSuccess = command.getResult()
            .stream()
            .filter(KbRepoIndexVectorCallbackCommand.Result::getSuccess)
            .map(KbRepoIndexVectorCallbackCommand.Result::getDataKey)
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dataKeyListOfSuccess)) {
            dataIndexActionService.updateHandleStatusOfDataKeyList(repoId, collectionId, dataKeyListOfSuccess, HandleStatusEnum.DONE);
        }
        List<String> dataKeyListOfFail = command.getResult()
            .stream()
            .filter(o -> o.getSuccess().equals(false))
            .map(KbRepoIndexVectorCallbackCommand.Result::getDataKey)
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dataKeyListOfFail)) {
            dataIndexActionService.updateHandleStatusOfDataKeyList(repoId, collectionId, dataKeyListOfFail, HandleStatusEnum.PROCESSING);
            taskRepository.updateTaskStatusOfFail(task.getId(), JsonUtils.toJson(command.getResult()));
        } else {
            taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
        }
        // 判断分块下的所有索引是否已经处理完成 完成更新分块处理状态为完成
        dataActionService.updateHandleStatusOfDataKeyList(repoId, collectionId, dataKeyListOfSuccess, HandleStatusEnum.DONE);
        // 更新集合为已完成
        if (dataIndexQueryService.getByCollectionId(repoId, collectionId).stream().allMatch(o -> o.getHandleStatus().equals(HandleStatusEnum.DONE))) {
            collectionActionService.updateCollectionStatus(collectionId, CollectionStatusEnum.COMPLETED);
        }
        // 移除进行队列任务
        handlerFactory.removeProcessTask(task);
        GennRequestContext.clear();
        return null;
    }


    public Object taskStatus(Long repoId) {
        List<KbRepoTaskDTO> pendingAndProcessTasks = repoTaskRepository.findTaskByRepoId(repoId);

        // 初始化包含所有 TaskStatusEnum 的 Map，初始值为 0
        Map<TaskStatusEnum, Integer> allStatusMap = Stream.of(TaskStatusEnum.values())
            .collect(Collectors.toMap(
                status -> status,
                status -> 0
            ));
        // 统计实际任务状态的计数
        Map<TaskStatusEnum, Integer> taskStatusMap = pendingAndProcessTasks.stream()
            .collect(Collectors.toMap(
                KbRepoTaskDTO::getTaskStatus,
                task -> 1,
                Integer::sum
            ));
        // 合并两个 Map
        allStatusMap.putAll(taskStatusMap);

        return allStatusMap;
    }
//
//    public Object taskStatus() {
//        Map<String, Long> map = Maps.newHashMap();
//        for (TaskTypeEnum type : TaskTypeEnum.values()) {
//            TaskHandler handler = handlerFactory.getHandler(type);
//            Map<String, Long> queueState = handler.getQueueState(CurrentUserHolder.getTenantId().toString());
//            map.putAll(queueState);
//        }
//        return map;
//    }
}
