package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 新建术语
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoTermEditCommand implements Serializable {


    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空")
    @Schema(description = "id")
    private Long id;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    private Long repoId;

    @Schema(description = "术语名称")
    @Size(max = 255, message = "术语名称长度不能超过255个字符")
    private String name;

    @Schema(description = "术语别名")
    private List<String> nameAliases;

    @Schema(description = "术语含义")
    @Size(max = 255, message = "术语含义长度不能超过255个字符")
    private String description;


}

