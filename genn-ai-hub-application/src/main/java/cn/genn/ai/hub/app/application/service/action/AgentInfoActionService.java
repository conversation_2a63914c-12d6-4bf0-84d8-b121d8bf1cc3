package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.api.UserAbilityImpl;
import cn.genn.ai.hub.app.application.assembler.AgentInfoAssembler;
import cn.genn.ai.hub.app.application.command.*;
import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import cn.genn.ai.hub.app.application.enums.McpTypeEnum;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.application.service.SyncAIGatewayCacheService;
import cn.genn.ai.hub.app.application.service.query.TeamInfoQueryService;
import cn.genn.ai.hub.app.application.service.query.TeamMemberQueryService;
import cn.genn.ai.hub.app.domain.agent.model.valobj.AgentExtraConfig;
import cn.genn.ai.hub.app.domain.agent.model.valobj.AgentMcpConfig;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.event.AuthCommonModifyEvent;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.OpenApiMcpConfig;
import cn.genn.ai.hub.app.domain.sensitiveword.matcher.ScopedSensitiveWordProvider;
import cn.genn.ai.hub.app.domain.sensitiveword.model.config.SensitiveWordConfig;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentInfoPO;
import cn.genn.ai.hub.app.infrastructure.utils.McpGatewayInvokeUtils;
import cn.genn.ai.hub.app.infrastructure.utils.auth.AuthUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.utils.Assert;
import cn.genn.core.utils.TransactionUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AgentInfoActionService {

    private final AgentInfoRepositoryImpl agentInfoRepository;
    private final AgentInfoAssembler agentInfoAssembler;
    private final TagsActionService tagsActionService;
    private final UserAbilityImpl userAbility;
    private final TeamInfoQueryService teamInfoQueryService;
    private final TeamMemberQueryService teamMemberQueryService;
    private final SpringEventPublish springEventPublish;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ScopedSensitiveWordProvider scopedSensitiveWordProvider;
    private final SyncAIGatewayCacheService syncAIGatewayCacheService;

    /**
     * 创建智能体
     *
     * @param command 创建智能体命令
     * @return 智能体ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(AgentInfoCreateCommand command) {
        if (command.getAgentType() == AgentTypeEnum.TOOL_MCQ) {
            validMcpConfig(command.getExtraConfig());
        }
        // 创建智能体
        Long agentId = agentInfoRepository.createAgent(agentInfoAssembler.createCommand2PO(command));
        // 处理标签关联
        handleTagAssociations(agentId, command.getAgentType(), command.getTeamId(), command.getTagIds());
        // 处理权限相关
        handlerCreateAgentAuth(agentId, command);

        if (command.getExtraConfig() != null) {
            Map<String, Object> mergeExtraConfig = Optional.of(command.getExtraConfig()).orElse(new HashMap<>());
            SensitiveWordConfig sensitiveWordConfig = JsonUtils.parse(JsonUtils.toJson(mergeExtraConfig.get("sensitiveWordConfig")), SensitiveWordConfig.class);
            boolean sensitiveCheck = Optional.ofNullable(sensitiveWordConfig)
                .map(SensitiveWordConfig::isEnableVerify)
                .orElse(false);
            if (sensitiveCheck) {
                TransactionUtils.doAfterCommit(() -> {
                    scopedSensitiveWordProvider.refreshAgentVerifyCache();
                    AgentInfoPO agentInfoPO = agentInfoRepository.findByIdIgnoreTenant(agentId);
                    syncAIGatewayCacheService.asyncAgent(agentInfoAssembler.convertVAD(agentInfoPO));
                });
            }
        }
        return agentId;
    }

    /**
     * 更新智能体
     *
     * @param command 更新智能体命令
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(AgentInfoUpdateCommand command) {
        // 检查智能体是否存在
        AgentInfoPO existingAgent = agentInfoRepository.getById(command.getId());
        if (existingAgent == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        if (existingAgent.getAgentType() == AgentTypeEnum.TOOL_MCQ) {
            validMcpConfig(command.getExtraConfig());
            McpGatewayInvokeUtils.deleteUserDefinedServer(existingAgent.getWorkflowId());
        }
        String cacheKey = CacheConstants.AGENT_LOCK_KEY + command.getId();
        Object lockInfo = redisTemplate.opsForValue().get(cacheKey);
        if (lockInfo != null) {
            LockStatusDTO lockStatus = (LockStatusDTO) lockInfo;
            if (!lockStatus.getOwnerUserId().equals(CurrentUserHolder.getUserId())) {
                throw new CheckException(MessageCode.AGENT_LOCKED);
            }
            redisTemplate.delete(cacheKey);
        }
        // 扩展字段合并处理，避免更新时丢失原有的扩展字段，刷新缓存
        AgentInfoPO agentInfoPO = agentInfoAssembler.updateCommand2PO(command);
        boolean updateCache = false;
        if (command.getExtraConfig() != null) {
            Map<String, Object> mergeExtraConfig = Optional.ofNullable(existingAgent.getExtraConfig()).orElse(new HashMap<>());
            mergeExtraConfig.putAll(command.getExtraConfig());
            agentInfoPO.setExtraConfig(mergeExtraConfig);
            updateCache = true;
        }
        // 更新智能体
        Boolean success = agentInfoRepository.updateAgent(agentInfoPO);
        if (updateCache) {
            TransactionUtils.doAfterCommit(() -> {
                scopedSensitiveWordProvider.refreshAgentVerifyCache();
                syncAIGatewayCacheService.asyncAgent(agentInfoAssembler.convertVAD(agentInfoPO));
            });

        }
        return success;
    }

    /**
     * 锁定智能体,同时仅允许一人编辑
     */
    public LockStatusDTO lock(IdCommand command) {
        Long agentId = command.getId();
        String cacheKey = CacheConstants.AGENT_LOCK_KEY + agentId;
        Long currentUserId = CurrentUserHolder.getUserId();
        SsoUserAuthInfoDTO currentUser = CurrentUserHolder.getCurrentUser();
        SimpleUserDTO simpleUserDTO = SimpleUserDTO.builder()
            .userId(currentUser.getUserId())
            .username(currentUser.getUsername())
            .nick(currentUser.getNick())
            .avatar(currentUser.getAvatar())
            .build();

        LockStatusDTO lockStatus = (LockStatusDTO) redisTemplate.opsForValue().get(cacheKey);
        if (lockStatus != null) {
            // 匹配是不是当前用户
            boolean isOwner = currentUserId.equals(lockStatus.getOwnerUserId());
            lockStatus.setOwnerByCurrentUser(isOwner);
            redisTemplate.expire(cacheKey, CacheConstants.AGENT_LOCK_STATUS_CHANGE_TIMEOUT, TimeUnit.MILLISECONDS);
            return lockStatus;
        }

        LockStatusDTO newLockStatus = LockStatusDTO.builder()
            .ownerUserId(currentUserId)
            .userInfo(simpleUserDTO)
            .resourceId(agentId)
            .ownerByCurrentUser(true)
            .build();

        Boolean locked = redisTemplate.opsForValue().setIfAbsent(
            cacheKey, newLockStatus,
            CacheConstants.AGENT_LOCK_STATUS_CHANGE_TIMEOUT,
            TimeUnit.MILLISECONDS);

        if (Boolean.TRUE.equals(locked)) {
            return newLockStatus;
        } else {
            LockStatusDTO reGet = (LockStatusDTO) redisTemplate.opsForValue().get(cacheKey);
            boolean isOwner = currentUserId.equals(reGet.getOwnerUserId());
            redisTemplate.expire(cacheKey, CacheConstants.AGENT_LOCK_STATUS_CHANGE_TIMEOUT, TimeUnit.MILLISECONDS);
            reGet.setOwnerByCurrentUser(isOwner);
            return reGet;
        }
    }


    /**
     * 删除智能体
     *
     * @param command 智能体ID查询
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(IdCommand command) {
        // 检查智能体是否存在
        AgentInfoPO existingAgent = agentInfoRepository.getById(command.getId());
        if (existingAgent == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        //删除标签关联
        String resourceKey = String.valueOf(existingAgent.getId());
        tagsActionService.deleteTagRefByResource(resourceKey, AgentTypeEnum.convertToTagType(existingAgent.getAgentType()), existingAgent.getTeamId());
        //删除权限
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.AGENT,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(command.getId())), this));
        return agentInfoRepository.deleteAgent(command.getId());
    }

    /**
     * 分配标签
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignTags(TagsAssignCommand command) {
        // 检查智能体是否存在
        AgentInfoPO existingAgent = agentInfoRepository.getById(command.getId());
        if (existingAgent == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 处理标签关联
        handleTagAssociations(existingAgent.getId(), existingAgent.getAgentType(), existingAgent.getTeamId(), command.getTagIds());
    }


    /**
     * 邀请智能体协作
     */
    public Boolean inviteCollaborator(ResourceInviteCommand command) {
        AgentInfoDTO agentInfoDTO = agentInfoRepository.getAgent(command.getId());
        if (agentInfoDTO.getTeamId() == null) {
            throw new CheckException(MessageCode.PERSONAL_SPACE_NOT_INVITE);
        }
        userAbility.checkUserValidByIds(command.getUserIds());
        List<TeamMemberDTO> teamMemberDTOS = teamMemberQueryService.listUserByTeam(agentInfoDTO.getTeamId());
        Set<Long> memberUserIds = teamMemberDTOS.stream().map(TeamMemberDTO::getUserId).collect(Collectors.toSet());
        if (!memberUserIds.containsAll(command.getUserIds())) {
            throw new CheckException(MessageCode.USER_NOT_TEAM_MEMBER);
        }
        springEventPublish.publish(AuthCommonModifyEvent.buildAddEvent(command.getUserIds()
            .stream()
            .map(userId -> AuthUtils.buildUserAgentAuth(command.getId(), userId, ActionType.EDIT))
            .collect(Collectors.toList()), this));
        return true;
    }

    /**
     * 移除智能体协作者
     */
    public Boolean removeCollaborator(ResourceRemoveInviteCommand command) {
        Long userId = command.getUserId();
        //团队所有者不允许移除
        AgentInfoDTO agentInfoDTO = agentInfoRepository.getAgent(command.getId());
        if (agentInfoDTO == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }
        if (agentInfoDTO.getTeamId() == null) {
            throw new CheckException(MessageCode.PERSONAL_SPACE_NOT_REMOVE);
        }
        TeamInfoDTO teamInfoDTO = teamInfoQueryService.get(IdQuery.builder().id(agentInfoDTO.getTeamId()).build());
        if (teamInfoDTO.getOwnerUserId().equals(userId)) {
            throw new CheckException(MessageCode.TEAM_OWNER_NOT_REMOVE);
        }
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.SUBJECT_TYPE, SubjectType.USER,
                AuthUniqueCriteriaEnum.SUBJECT_KEY, String.valueOf(command.getUserId()),
                AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.AGENT,
                AuthUniqueCriteriaEnum.ACTION_TYPE, ActionType.EDIT,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(command.getId())), this));
        return true;
    }

    /**
     * 处理标签关联
     *
     * @param agentId 智能体ID
     * @param tagIds  标签ID列表
     */
    private void handleTagAssociations(Long agentId, AgentTypeEnum agentType, Long teamId, List<Long> tagIds) {
        String resourceKey = agentId.toString();
        tagsActionService.updateTagRefByResource(resourceKey, AgentTypeEnum.convertToTagType(agentType), teamId, tagIds);
    }


    private void handlerCreateAgentAuth(Long agentId, AgentInfoCreateCommand command) {
        List<AuthCommon> authCommonList = new ArrayList<>();
        authCommonList.add(AuthUtils.buildUserAgentAuth(agentId, CurrentUserHolder.getUserId(), ActionType.MANAGER));
        springEventPublish.publish(AuthCommonModifyEvent
            .buildAddEvent(authCommonList, this));
    }


    private void validMcpConfig(Object extraConfig) {
        AgentExtraConfig agentExtraConfig = JsonUtils.parse(JsonUtils.toJson(extraConfig), AgentExtraConfig.class);
        if (agentExtraConfig == null) {
            throw new CheckException(MessageCode.MCP_CONFIG_NOT_VALID);
        }
        AgentMcpConfig mcpConfig = agentExtraConfig.getMcpConfig();
        if (McpTypeEnum.OPENAPI == mcpConfig.getImportType()) {
            OpenApiMcpConfig openapiConfig = agentExtraConfig.getOpenapi();
            Assert.nonNull(openapiConfig, MessageCode.MCP_CONFIG_NOT_VALID);
            Assert.nonNull(openapiConfig.getBaseUrl(), "baseUrl");
            if (openapiConfig.getConfig() == null && CharSequenceUtil.isEmpty(openapiConfig.getConfigUrl())) {
                throw new CheckException(MessageCode.MCP_CONFIG_NOT_VALID, "OpenAPI配置不能为空");
            }
        }

    }


}

