package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SpeechProperties {

    private String endpoint = "wss://openspeech.bytedance.com/api/v3/sami/podcasttts";

    private String appId = "8928904598";

    private String appKey = "aGjiRDfUWi";

    private String accessKey = "003bvNI1veIQc4kgIE6OAz7YUxX9PJC2";

    private String secretKey = "Rg-_S0QZK2FRCoW4PN9Q49MmhXR18Z3L";

    private String encoding = "mp3";
    // 播客文本关联的唯一ID
    private String inputId = "podcast";

    private String scene = "deep_research";

    private String resourceId = "volc.service_type.10050";

    private Boolean useHeadMusic = true;

}
