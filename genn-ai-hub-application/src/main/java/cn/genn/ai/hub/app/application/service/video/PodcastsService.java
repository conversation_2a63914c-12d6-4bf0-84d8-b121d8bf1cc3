package cn.genn.ai.hub.app.application.service.video;

import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.feishu.video.SpeechLabRequest;
import cn.genn.ai.hub.app.application.service.action.KbRepoFileActionService;
import cn.genn.ai.hub.app.application.service.video.protocol.EventType;
import cn.genn.ai.hub.app.application.service.video.protocol.Message;
import cn.genn.ai.hub.app.application.service.video.websocket.SpeechWebSocketClient;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.ai.hub.app.infrastructure.config.voice.SpeechProperties;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.lock.base.LockTemplate;
import cn.genn.lock.base.properties.LockInfo;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * 飞书用户查询相关
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PodcastsService {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private GennAIHubProperties aiHubProperties;
    @Resource
    private FileStorageOperation storageOperation;
    @Resource
    private KbRepoFileActionService repoFileActionService;
    @Resource
    protected StringRedisTemplate stringRedisTemplate;
    @Resource
    protected LockTemplate lockTemplate;

    public String speechLab(SpeechLabRequest command) {

        String processKey = CacheConstants.FS_VOICE_SPEECH + command.getTaskId();
        checkSpeechKey(processKey);
        SpeechProperties speech = aiHubProperties.getSpeech();
        speech.setInputId(command.getTaskId());
        String fileKey = command.getTaskId();
        String fileName = fileKey + "." + speech.getEncoding();
        String objectKey = "/fs/video/speech/" + fileName;
        KbRepoFilePO file = repoFileActionService.getFile(objectKey);
        if (file != null) {
            return storageOperation.preSignedUrlGetObject(objectKey, fileName);
        }
        validateSpeechProperties(speech);

        // 获取锁
        LockInfo lockInfo = lockTemplate.lock(CacheConstants.FS_VOICE_SPEECH_LOCK + ":" + command.getTaskId(),
            CacheConstants.LOCK_ORDER_STATUS_CHANGE_TIMEOUT,
            CacheConstants.LOCK_ORDER_STATUS_CHANGE_TIMEOUT);

        if (lockInfo == null) {
            log.warn("Failed to acquire lock for speechLab {}.", command.getTaskId());
            throw new BusinessException("正在合成音频，请稍后查看....");
        }
        try {
            checkSpeechKey(processKey);
            stringRedisTemplate.opsForValue().set(processKey, "speech", 1, TimeUnit.DAYS);
            processSpeechAsync(command, speech, objectKey, fileName);
        } catch (Exception e) {
            log.error("PodcastsService  Exception", e);
            throw e;
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
        throw new BusinessException("正在合成音频，请稍后查看....");

    }

    private void checkSpeechKey(String processKey) {
        String storedValue = stringRedisTemplate.opsForValue().get(processKey);
        if (StringUtils.equals(storedValue, "speech")) {
            throw new BusinessException("正在合成音频，请稍后查看....");
        }
    }

    private void processSpeechAsync(SpeechLabRequest command, SpeechProperties speech, String objectKey, String fileName) {
        Long tenantId = CurrentUserHolder.getTenantId();
        Thread.ofVirtual()
            .name("thread_speechLab_" + command.getTaskId())
            .start(() -> {
                GennRequestContext.initTenantId(tenantId);
                SpeechWebSocketClient client = null;
                try {
                    // 需要合成的文本内容
                    String text = command.getContent();
                    if (command.getUseHeadMusic() != null) {
                        speech.setUseHeadMusic(command.getUseHeadMusic());
                    }

                    Map<String, String> headers = buildHeaders(speech);

                    SpeechContext context = new SpeechContext();
                    context.retryNum = 5;
                    context.taskId = "";
                    while (context.retryNum > 0) {
                        client = createAndConnectClient(speech.getEndpoint(), headers);
                        if (processSpeech(client, speech, text, context)) {
                            if (storageOperation.saveByteArray(context.podcastAudio, objectKey, null)) {
                                log.info("本次播客音频已保存到文件: {}", objectKey);
                                String preSignedUrl = storageOperation.preSignedUrlGetObject(objectKey, fileName);
                                GennAIFileStorageProperties tos = aiHubProperties.getTos();
                                repoFileActionService.uploadLog(KbRepoFileDTO.builder()
                                    .repoId(0L)
                                    .filePlatform(tos.getBucketName())
                                    .externalFileId(objectKey)
                                    .externalFileUrl(preSignedUrl)
                                    .fileName(fileName)
                                    .contentType("audio/mp3")
                                    .length(context.podcastAudio.length)
                                    .build());
                            }
                        }
                        if (!context.isPodcastRoundEnd) {
                            handleRetry(context);
                        }
                    }
                } catch (Exception e) {
                    log.error("音频合成异常: ", e);
                } finally {
                    stringRedisTemplate.delete(CacheConstants.FS_VOICE_SPEECH + command.getTaskId()); // 使用后立即清除
                    GennRequestContext.clear();
                    // 结束连接
                    if (client != null) {
                        try {
                            client.closeBlocking();
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }

                }
            });
    }

    private void validateSpeechProperties(SpeechProperties speech) {
        if (speech.getAppId() == null || speech.getAccessKey() == null) {
            throw new BusinessException("请设置appId和accessKey系统属性");
        }
    }

    private Map<String, String> buildHeaders(SpeechProperties speech) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Api-App-Id", speech.getAppId());
        headers.put("X-Api-App-Key", speech.getAppKey());
        headers.put("X-Api-Access-Key", speech.getAccessKey());
        headers.put("X-Api-Resource-Id", speech.getResourceId());
        headers.put("X-Api-Connect-Id", UUID.randomUUID().toString());
        return headers;
    }

    private SpeechWebSocketClient createAndConnectClient(String endpoint, Map<String, String> headers) {
        try {
            SpeechWebSocketClient client = new SpeechWebSocketClient(new URI(endpoint), headers);
            client.connectBlocking();
            return client;
        } catch (Exception e) {
            log.error("create websocket exception: ", e);
            throw new BusinessException("create websocket exception");
        }
    }

    private boolean processSpeech(SpeechWebSocketClient client, SpeechProperties speech, String text, SpeechContext context) throws Exception {
        Map<String, Object> requestParams = buildRequestParams(speech, text, context);
        log.info("request: {}", JsonUtils.toJson(requestParams));

        client.sendStartConnection();

        String sessionId = UUID.randomUUID().toString();
        if (context.taskId.isEmpty()) {
            context.taskId = sessionId;
        }

        client.sendStartSession(objectMapper.writeValueAsBytes(requestParams), sessionId);
        client.sendFinishSession(sessionId);

        processMessages(client, context);

        if (!context.audioReceived) {
            throw new RuntimeException("未收到音频数据");
        }

        client.sendFinishConnection();
        return context.isPodcastRoundEnd && context.podcastAudio.length > 0;
    }

    private Map<String, Object> buildRequestParams(SpeechProperties speech, String text, SpeechContext context) {
        Map<String, Object> audioConfig = Map.of(
            "format", speech.getEncoding(),
            "sample_rate", 24000,
            "speech_rate", 0
        );

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("input_id", speech.getInputId());
        reqParams.put("input_text", text);
        reqParams.put("action", 0);
        reqParams.put("scene", speech.getScene());
        reqParams.put("use_head_music", speech.getUseHeadMusic());
        reqParams.put("audio_config", audioConfig);

        if (!context.isPodcastRoundEnd) {
            reqParams.put("retry_info", Map.of(
                "retry_task_id", context.taskId,
                "last_finished_round_id", context.lastRoundId
            ));
        }

        return reqParams;
    }

    private void processMessages(SpeechWebSocketClient client, SpeechContext context) throws Exception {
        String voice = "";
        int round = -10000;

        while (true) {
            Message msg = client.receiveMessage();

            switch (msg.getType()) {
                case MSG_TYPE_AUDIO_ONLY_SERVER:
                    if (msg.getEvent() == EventType.TYPE_PODCAST_ROUND_RESPONSE) {
                        handleAudioMessage(msg, context);
                    }
                    break;
                case MSG_TYPE_ERROR:
                    throw new RuntimeException("服务端返回错误: " + new String(msg.getPayload()));
                case MSG_TYPE_FULL_SERVER:
                    if (msg.getEvent() == EventType.TYPE_PODCAST_ROUND_START) {
                        handleRoundStart(msg, context, voice, round);
                    } else if (msg.getEvent() == EventType.TYPE_PODCAST_ROUND_END) {
                        handleRoundEnd(context, round);
                    }
                    break;
            }

            if (msg.getEvent() == EventType.TYPE_SESSION_FINISHED) {
                break;
            }
        }
    }

    private void handleAudioMessage(Message msg, SpeechContext context) {
        if (!context.audioReceived && context.audio.length > 0) {
            context.audioReceived = true;
        }
        byte[] newAudio = new byte[context.audio.length + msg.getPayload().length];
        System.arraycopy(context.audio, 0, newAudio, 0, context.audio.length);
        System.arraycopy(msg.getPayload(), 0, newAudio, context.audio.length, msg.getPayload().length);
        context.audio = newAudio;
    }

    private void handleRoundStart(Message msg, SpeechContext context, String voice, int round) throws Exception {
        String jsonString = new String(msg.getPayload(), StandardCharsets.UTF_8);
        Map<String, Object> map = objectMapper.readValue(jsonString, Map.class);
        voice = (String) map.get("speaker");
        round = (int) map.get("round_id");
        if (round == -1) {
            voice = "head_music";
        }
        context.isPodcastRoundEnd = false;
        log.info("开始新 round: {}", jsonString);
    }

    private void handleRoundEnd(SpeechContext context, int round) {
        context.isPodcastRoundEnd = true;
        context.lastRoundId = round;
        byte[] newAudio = new byte[context.podcastAudio.length + context.audio.length];
        System.arraycopy(context.podcastAudio, 0, newAudio, 0, context.podcastAudio.length);
        System.arraycopy(context.audio, 0, newAudio, context.podcastAudio.length, context.audio.length);
        context.podcastAudio = newAudio;
        context.audio = new byte[0];
    }

    private void handleRetry(SpeechContext context) {
        context.retryNum -= 1;
        log.info("本次播客未结束，断点续传从 {} 轮播客开始", context.lastRoundId);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Data
    private static class SpeechContext {
        private boolean isPodcastRoundEnd = true;
        private int lastRoundId = -1;
        private String taskId;
        private int retryNum;
        private byte[] podcastAudio = new byte[0];
        private byte[] audio = new byte[0];
        private boolean audioReceived = false;
    }
}
