package cn.genn.ai.hub.app.application.storage;

import cn.genn.ai.hub.app.application.assembler.FileStorageAssembler;
import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.core.api.tool.FileStorageConfig;
import cn.genn.ai.hub.core.api.tool.FileStorageTypeEnum;
import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.StrUtil;
import com.obs.services.ObsClient;
import com.obs.services.exception.ObsException;
import com.obs.services.model.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 华为云存储
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OBSOperationStrategy implements StorageStrategy {

    private final GennAIHubProperties properties;
    private final StringRedisTemplate stringRedisTemplate;
    private final FileStorageAssembler fileStorageAssembler;

    @Override
    public boolean saveFile(MultipartFile file, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getObs());
        config.setFileStorageType(FileStorageTypeEnum.OBS);
        return saveFile(file, objectKey, config);
    }
    @Override
    public boolean saveContentTOFile(String content, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getObs());
        validateStorageConfig(config);
        ObsClient obsClient = buildObsClient(config);
        try (InputStream inputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8))) {
            PutObjectRequest request = new PutObjectRequest(
                config.getBucketName(),
                objectKey,
                inputStream
            );
            PutObjectResult result = obsClient.putObject(request);
            log.info("OBS upload success. Key: {}, ETag: {}, Status: {}",
                objectKey, result.getEtag(), result.getStatusCode());
            return true;
        } catch (ObsException e) {
            log.error("OBS upload failed. Key: {}, error: {}", objectKey, e.getMessage(), e);
            // 请求失败,打印http状态码
            log.error("HTTP Code:" + e.getResponseCode());
            // 请求失败,打印服务端错误码
            log.error("Error Code:" + e.getErrorCode());
            // 请求失败,打印详细错误信息
            log.error("Error Message:" + e.getErrorMessage());
            // 请求失败,打印请求id
            log.error("Request ID:" + e.getErrorRequestId());
            log.error("Host ID:" + e.getErrorHostId());
        } catch (Exception e) {
            log.error("OBS upload unexpected error. Key: {}", objectKey, e);
        }
        return false;
    }
    @Override
    public InputStream getObject(String objectKey) {
        GennAIFileStorageProperties obsProperties = properties.getObs();
        String endpoint = obsProperties.getEndpoint();
        String accessKey = obsProperties.getAccessKey();
        String secretKey = obsProperties.getSecretKey();
        String bucketName = obsProperties.getBucketName();
        // 创建ObsClient实例
        ObsClient obsClient = new ObsClient(accessKey, secretKey, endpoint);
        try {
            // 文件上传
            GetObjectRequest request = new GetObjectRequest();
            request.setBucketName(bucketName);
            request.setObjectKey(objectKey);
            ObsObject output = obsClient.getObject(request);
            return output.getObjectContent();
        } catch (ObsException e) {
            log.error("putObject failed");
            // 请求失败,打印http状态码
            log.error("HTTP Code:" + e.getResponseCode());
            // 请求失败,打印服务端错误码
            log.error("Error Code:" + e.getErrorCode());
            // 请求失败,打印详细错误信息
            log.error("Error Message:" + e.getErrorMessage());
            // 请求失败,打印请求id
            log.error("Request ID:" + e.getErrorRequestId());
            log.error("Host ID:" + e.getErrorHostId());
            e.printStackTrace();
        } catch (Exception e) {
            log.error("putObject failed");
            // 其他异常信息打印
            e.printStackTrace();
        }
       throw new RuntimeException();
    }

    @SneakyThrows
    @Override
    public String preSignedUrlGetObject(String objectKey, String originFileName) {
        return preSignedUrlGetObject(objectKey, originFileName, null, fileStorageAssembler.toFileStorageConfig(properties.getObs()));
    }

    @Override
    public String getCachedPreSignedUrl(String objectKey, String originFileName) {
        String cacheKey = CacheConstants.TOS_PRE_SIGNED_URL_KEY + objectKey;
        if (stringRedisTemplate.hasKey(cacheKey)) {
            return stringRedisTemplate.opsForValue().get(cacheKey);
        } else {
            if (StrUtil.isBlank(originFileName)) {
                originFileName = objectKey;
            }
            String newUrl = preSignedUrlGetObject(objectKey, originFileName);
            // 设置160天过期
            stringRedisTemplate.opsForValue().set(cacheKey, newUrl, 160, TimeUnit.DAYS);
            return newUrl;
        }
    }

    @Override
    public boolean saveFile(MultipartFile file, String objectKey, FileStorageConfig config) {
        validateStorageConfig(config);
        ObsClient obsClient = buildObsClient(config);

        try (InputStream inputStream = new BufferedInputStream(file.getInputStream())) {
            PutObjectRequest request = new PutObjectRequest(
                config.getBucketName(),
                objectKey,
                inputStream
            );
            PutObjectResult result = obsClient.putObject(request);
            log.info("OBS upload success. Key: {}, ETag: {}, Status: {}",
                objectKey, result.getEtag(), result.getStatusCode());
            return true;
        } catch (ObsException e) {
            log.error("OBS upload failed. Key: {}, error: {}", objectKey, e.getMessage(), e);
            // 请求失败,打印http状态码
            log.error("HTTP Code:" + e.getResponseCode());
            // 请求失败,打印服务端错误码
            log.error("Error Code:" + e.getErrorCode());
            // 请求失败,打印详细错误信息
            log.error("Error Message:" + e.getErrorMessage());
            // 请求失败,打印请求id
            log.error("Request ID:" + e.getErrorRequestId());
            log.error("Host ID:" + e.getErrorHostId());
        } catch (Exception e) {
            log.error("OBS upload unexpected error. Key: {}", objectKey, e);
        }
        return false;
    }

    @Override
    public String preSignedUrlGetObject(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
        validateStorageConfig(config);
        Map<String, String> headers = new HashMap<>();
        if (StrUtil.isNotBlank(originFileName)) {
            headers.put("response-content-disposition",
                String.format("attachment;filename=%s",
                    URLEncoder.encode(originFileName, StandardCharsets.UTF_8)));
        }

        try {
            TemporarySignatureRequest request = new TemporarySignatureRequest(
                HttpMethodEnum.GET,
                Optional.ofNullable(expireSeconds).orElse(180 * 24 * 3600L)
            );
            request.setBucketName(config.getBucketName());
            request.setObjectKey(objectKey);
            request.setHeaders(headers);

            return buildObsClient(config)
                .createTemporarySignature(request)
                .getSignedUrl();
        } catch (ObsException e) {
            log.error("Failed to create pre-signed URL. Key: {}, error: {}", objectKey, e.getMessage(), e);
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
    }

    private ObsClient buildObsClient(FileStorageConfig config) {
        return new ObsClient(
            config.getAccessKey(),
            config.getSecretKey(),
            config.getEndpoint()
        );
    }

    private void validateStorageConfig(FileStorageConfig config) {
        if (config == null) {
            throw new BusinessException(MessageCode.INVALID_STORAGE_CONFIG);
        }

        if (StrUtil.hasBlank(
            config.getEndpoint(),
            config.getAccessKey(),
            config.getSecretKey(),
            config.getBucketName())) {
            log.error("Invalid OBS config: {}", config);
            throw new BusinessException(MessageCode.INVALID_STORAGE_CONFIG);
        }
    }

    @Override
    public boolean saveOutPutStream(ByteArrayOutputStream content, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getObs());
        validateStorageConfig(config);
        ObsClient obsClient = buildObsClient(config);
        try (InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(content.toByteArray()))) {
            PutObjectRequest request = new PutObjectRequest(
                config.getBucketName(),
                objectKey,
                inputStream
            );
            PutObjectResult result = obsClient.putObject(request);
            log.info("OBS upload success. Key: {}, ETag: {}, Status: {}",
                objectKey, result.getEtag(), result.getStatusCode());
            return true;
        } catch (ObsException e) {
            log.error("OBS upload failed. Key: {}, error: {}", objectKey, e.getMessage(), e);
            // 请求失败,打印http状态码
            log.error("HTTP Code:" + e.getResponseCode());
            // 请求失败,打印服务端错误码
            log.error("Error Code:" + e.getErrorCode());
            // 请求失败,打印详细错误信息
            log.error("Error Message:" + e.getErrorMessage());
            // 请求失败,打印请求id
            log.error("Request ID:" + e.getErrorRequestId());
            log.error("Host ID:" + e.getErrorHostId());
        } catch (Exception e) {
            log.error("OBS upload unexpected error. Key: {}", objectKey, e);
        }
        return false;
    }

    @Override
    public boolean saveByteArray(byte[] byteArray, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getObs());
        validateStorageConfig(config);
        ObsClient obsClient = buildObsClient(config);
        try (InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(byteArray))) {
            PutObjectRequest request = new PutObjectRequest(
                config.getBucketName(),
                objectKey,
                inputStream
            );
            PutObjectResult result = obsClient.putObject(request);
            log.info("OBS upload success. Key: {}, ETag: {}, Status: {}",
                objectKey, result.getEtag(), result.getStatusCode());
            return true;
        } catch (ObsException e) {
            log.error("OBS upload failed. Key: {}, error: {}", objectKey, e.getMessage(), e);
            // 请求失败,打印http状态码
            log.error("HTTP Code:" + e.getResponseCode());
            // 请求失败,打印服务端错误码
            log.error("Error Code:" + e.getErrorCode());
            // 请求失败,打印详细错误信息
            log.error("Error Message:" + e.getErrorMessage());
            // 请求失败,打印请求id
            log.error("Request ID:" + e.getErrorRequestId());
            log.error("Host ID:" + e.getErrorHostId());
        } catch (Exception e) {
            log.error("OBS upload unexpected error. Key: {}", objectKey, e);
        }
        return false;
    }
}
