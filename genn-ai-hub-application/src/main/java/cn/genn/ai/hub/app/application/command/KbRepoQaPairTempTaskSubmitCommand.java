package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 智能创建问答对任务提交
 *
 * @Date: 2025/4/10
 * @Author: kangjian
 */
@Data
public class KbRepoQaPairTempTaskSubmitCommand {

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    @NotNull(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "数据集id")
//    @NotNull(message = "数据集id不能为空")
    private Long collectionId;

    @Schema(description = "文件id")
    private Long fileId;

}
