package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.AgentChannelDTO;
import cn.genn.ai.hub.app.application.dto.request.AgentChannelSaveCommand;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description 智能体渠道转换器
 * @date 2025-04-27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AgentChannelAssembler {

    AgentChannelAssembler INSTANCE = Mappers.getMapper(AgentChannelAssembler.class);

    AgentChannelDTO po2Dto(AgentChannelPO agentChannelPO);

    AgentChannelPO command2Po(AgentChannelSaveCommand saveCommand);
}
