package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.KbRepoDataAssembler;
import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.dto.callback.KbRepoChunkCallbackCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataAndIndexUpdateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataChunkRequestCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataSmartChunkSummaryCommand;
import cn.genn.ai.hub.app.application.enums.*;
import cn.genn.ai.hub.app.infrastructure.config.MQSendTopicConfigs;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataIndexRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.lock.base.LockException;
import cn.genn.lock.base.LockTemplate;
import cn.genn.lock.base.properties.LockInfo;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.ai.utils.AIUtils;
import cn.genn.spring.boot.starter.event.rocketmq.component.RocketMQEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoDataActionService {

    private final KbRepDataRepositoryImpl dataRepository;
    private final KbRepoBaseInfoRepositoryImpl repoBaseInfoRepository;
    private final KbRepDataIndexRepositoryImpl dataIndexRepository;
    private final KbRepoDataAssembler assembler;
    private final KbRepoDataIndexActionService dataIndexActionService;
    private final RocketMQEventPublish rocketMQEventPublish;
    private final MQSendTopicConfigs mqSendTopicConfigs;
    private final LockTemplate lockTemplate;

    /**
     * 按指定/新增的数据条目进行分词分块,可以整个数据集/某个条目/用户录入文本
     */
    @Transactional(rollbackFor = Exception.class)
    public Long chunk(KbRepoDataChunkRequestCommand command) {
        // 分情况处理
        if (StrUtil.isNotBlank(command.getDataKey())) {
            // 更新具体的某个分块的分词分块结果
            return chunkForOneData(assembler.kbRepoDataChunkRequestCommand2DTO(command), null);
        } else if (StrUtil.isBlank(command.getDataKey()) && StrUtil.isNotBlank(command.getQuestion())) {
            // 按用户录入的一个分块进行分词 不分块
            return chunkForData(command.getRawText(), assembler.kbRepoDataChunkRequestCommand2DTO(command), null);
        } else if (StrUtil.isNotBlank(command.getRawText())) {
            // 按用户录入的原始文本进行分词分块 未分块
            return chunkForRawText(command);
        }
        return null;
    }

    /**
     * 更新某个分块的分词分块结果 并触发索引重建 有dataKey
     *
     * @param dto
     * @return
     */
    private Long chunkForOneData(KbRepoDataDTO dto, List<KbRepoDataIndexDTO> indexInfoList) {
        KbRepoBaseInfoPO repoBaseInfoPO = repoBaseInfoRepository.getById(dto.getRepoId());
        if (Objects.isNull(repoBaseInfoPO)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 按具体的分块进行更新
        dataRepository.updateDataAndHandleStatus(assembler.DTO2PO(dto));
        // 如果没有默认索引 新增一条
        KbRepoDataIndexDTO indexDTO = KbRepoDataIndexDTO.builder()
            .repoId(dto.getRepoId())
            .collectionId(dto.getCollectionId())
            .dataKey(dto.getDataKey())
            .indexType(IndexTypeEnum.DEFAULT)
            .build();
        dataIndexActionService.createDataIndexOfDefault(dto.getRepoId(), dto.getCollectionId(), Arrays.asList(indexDTO));
        if (CollectionUtil.isNotEmpty(indexInfoList)) {
            // 去除默认索引 录入其他用户自定义索引
            List<KbRepoDataIndexDTO> customerindexInfoList = indexInfoList.stream().filter(index -> index.getIndexType().equals(IndexTypeEnum.CUSTOMER)).collect(Collectors.toList());
            customerindexInfoList.forEach(index -> index.setDataKey(dto.getDataKey()));
            dataIndexActionService.createDataIndexOfCustomer(dto.getRepoId(), dto.getCollectionId(),dto.getDataKey(), customerindexInfoList);
        }
        // 查询所有的索引 并更新为处理中
        dataIndexRepository.updateHandleStatusOfDataKey(dto.getRepoId(), dto.getCollectionId(), Arrays.asList(dto.getDataKey()), HandleStatusEnum.WAIT);
        List<KbRepoDataIndexPO> dataIndexPOList = dataIndexRepository.selectDataIndexOfDataKey(dto.getRepoId(), dto.getCollectionId(), dto.getDataKey());
        // 调用算法先删除这个分块下的所有向量
        dataIndexActionService.deleteDataIndexVectorOfData(dto.getRepoId(), dto.getCollectionId(), dto.getDataKey(), repoBaseInfoPO.getTenantId());
        if (CollectionUtil.isNotEmpty(dataIndexPOList)) {
            dataIndexPOList.forEach(index -> {
                // 发送mq 添加到队列中调用算法进行索引向量化
                KbRepoTaskOfIndexVectorBody body = KbRepoTaskOfIndexVectorBody.builder()
                    .tenantId(CurrentUserHolder.getTenantId())
                    .repoId(dto.getRepoId())
                    .collectionId(dto.getCollectionId())
                    .dataKey(dto.getDataKey())
                    .indexKey(index.getIndexKey())
                    .rawText(dataIndexActionService.handleRawText(dto.getDataKey(), index.getIndexKey()))
                    .vectorModelKey(repoBaseInfoPO.getVectorModelKey())
                    .build();
                SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfIndexVector(body);
            });
        }

        return dto.getId();
    }

    /**
     * 按用户录入的一个分块进行分词 不分块 并触发索引重建(队列异步) 没有dataKey
     *
     * @param dto
     * @return
     */
    private Long chunkForData(String rawText, KbRepoDataDTO dto, List<KbRepoDataIndexDTO> indexInfoList) {
        KbRepoBaseInfoPO repoBaseInfoPO = repoBaseInfoRepository.getById(dto.getRepoId());
        if (Objects.isNull(repoBaseInfoPO)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 对分块数据进行新增 需要计算序号到最大 追加在后面
        List<KbRepoDataPO> records = dataRepository.selectRepoDataListByCollectionId(dto.getCollectionId());
        // 查找出记录里面chunkIndex最大的序号
        int maxChunkIndex = records.stream().mapToInt(KbRepoDataPO::getChunkIndex).max().orElse(0);
        dto.setChunkIndex(maxChunkIndex + 1);
        KbRepoDataPO kbRepoDataPO = assembler.DTO2PO(dto);
        dataRepository.saveKbRepoData(kbRepoDataPO);
        String dataKey = kbRepoDataPO.getDataKey();
        // 如果没有默认索引 新增一条
        KbRepoDataIndexDTO indexDTO = KbRepoDataIndexDTO.builder()
            .repoId(dto.getRepoId())
            .collectionId(dto.getCollectionId())
            .dataKey(dataKey)
            .indexType(IndexTypeEnum.DEFAULT)
            .build();
        dataIndexActionService.createDataIndexOfDefault(dto.getRepoId(), dto.getCollectionId(), Arrays.asList(indexDTO));
        if (CollectionUtil.isNotEmpty(indexInfoList)) {
            // 去除默认索引 录入其他用户自定义索引
            List<KbRepoDataIndexDTO> customerindexInfoList = indexInfoList.stream().filter(index -> index.getIndexType().equals(IndexTypeEnum.CUSTOMER)).collect(Collectors.toList());
            customerindexInfoList.forEach(index -> index.setDataKey(dataKey));
            dataIndexActionService.createDataIndexOfCustomer(dto.getRepoId(), dto.getCollectionId(),null, customerindexInfoList);
        }
        // 查询所有的索引 并更新为处理中
        dataIndexRepository.updateHandleStatusOfDataKey(dto.getRepoId(), dto.getCollectionId(), Arrays.asList(dto.getDataKey()), HandleStatusEnum.WAIT);
        // 发送mq 添加到队列中调用算法
        List<KbRepoDataIndexPO> indexPOList = dataIndexRepository.selectDataIndexOfDataKey(dto.getRepoId(), dto.getCollectionId(), dataKey);
        if (CollectionUtil.isNotEmpty(indexPOList)) {
            indexPOList.forEach(index -> {
                KbRepoTaskOfIndexVectorBody body = KbRepoTaskOfIndexVectorBody.builder()
                    .tenantId(CurrentUserHolder.getTenantId())
                    .repoId(dto.getRepoId())
                    .collectionId(dto.getCollectionId())
                    .dataKey(dataKey)
                    .indexKey(index.getIndexKey())
                    .rawText(dataIndexActionService.handleRawText(dataKey, index.getIndexKey()))
                    .vectorModelKey(repoBaseInfoPO.getVectorModelKey())
                    .build();
                SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfIndexVector(body);
            });
        }
        return null;
    }

    /**
     * 按用户录入的原始文本进行分词分块 并触发索引重建
     *
     * @param command
     * @return
     */
    private Long chunkForRawText(KbRepoDataChunkRequestCommand command) {
        KbRepoTaskOfTextChunkBody body = KbRepoTaskOfTextChunkBody.builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .repoId(command.getRepoId())
            .collectionId(command.getCollectionId())
            .rawText(command.getDataKey())
            .build();
        SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfTextChunk(body);
        return null;
    }

    /**
     * 更新或新增数据条目的内容和索引内容
     */
    @Transactional(rollbackFor = Exception.class)
    public Long updateOrCreateData(KbRepoDataAndIndexUpdateCommand command) {
        // 分情况处理
        List<KbRepoDataIndexDTO> indexInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(command.getIndexInfoList())) {
            indexInfoList = assembler.kbRepoDataIndexUpdateCommand2DTO(command.getIndexInfoList());
            indexInfoList.forEach(index -> {
                index.setHandleStatus(HandleStatusEnum.WAIT);
                index.setDataKey(command.getDataKey());
                index.setRepoId(command.getRepoId());
                index.setCollectionId(command.getCollectionId());
            });
        }
        if (StrUtil.isNotBlank(command.getDataKey())) {
            // 更新某个分块的分词分块结果
            return chunkForOneData(assembler.kbRepoDataAndIndexUpdateCommand2DTO(command), indexInfoList);
        } else {
            // 新增分块 需要计算分块的最大序号
            return chunkForData(command.getQuestion(), assembler.kbRepoDataAndIndexUpdateCommand2DTO(command), indexInfoList);
        }
    }

    /**
     * 删除某个数据条目 同时要删除索引 并且通知到算法进行删除
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDataByDataKey(KbRepoDataAndIndexUpdateCommand command) {
        // 删除数据条目
        dataRepository.deleteDataByDataKey(command.getDataKey());
        // 删除数据条目绑定的所有索引
        dataIndexRepository.deleteDataIndexVectorOfData(command.getRepoId(), command.getCollectionId(), command.getDataKey());
        // 通知算法删除数据条目对应的所有索引
        dataIndexActionService.deleteDataIndexVectorOfData(command.getRepoId(), command.getCollectionId(), command.getDataKey(), CurrentUserHolder.getTenantId());
        return true;
    }

    /**
     * 保存算法返回的分块结果
     *
     * @param command
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveTextChunkCallbackData(KbRepoChunkCallbackCommand command) {
        Long repoId = Long.parseLong(command.getRepoId());
        Long collectionId = Long.parseLong(command.getCollectionId());
        // 加分布式锁
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock("saveTextChunkCallbackData_repo_" + command.getRepoId() + "_collection_" + command.getCollectionId());
            if (lockInfo != null) {
                List<KbRepoDataPO> kbRepoDataPOList = new ArrayList<>();
                command.getResult().forEach(result -> {
                    KbRepoDataPO po = KbRepoDataPO.builder()
                        .repoId(Long.parseLong(command.getRepoId()))
                        .collectionId(Long.parseLong(command.getCollectionId()))
                        .dataType(result.getDataType())
                        .dataKey(result.getDataKey())
                        .question(result.getContent())
                        .chunkIndex(result.getChunkIndex())
                        .handleStatus(HandleStatusEnum.DONE)
                        .build();
                    if ("image".equalsIgnoreCase(result.getDataType())) {
                        KbRepoDataExtDataDTO dto = KbRepoDataExtDataDTO.builder()
                            .url(result.getUrl())
                            .path(result.getPath())
                            .build();
                        po.setExtData(JsonUtils.toJson(dto));
                    }
                    kbRepoDataPOList.add(po);
                });
                dataRepository.batchSaveKbRepoData(kbRepoDataPOList);
                command.getResult().forEach(result -> {
                    String dataKey = result.getDataKey();
                    // 如果没有默认索引 新增一条
                    KbRepoDataIndexDTO indexDTO = KbRepoDataIndexDTO.builder()
                        .repoId(repoId)
                        .collectionId(collectionId)
                        .dataKey(dataKey)
                        .indexType(IndexTypeEnum.DEFAULT)
                        .build();
                    dataIndexActionService.createDataIndexOfDefault(repoId, collectionId, Arrays.asList(indexDTO));
                });
            } else {
                throw new LockException();
            }
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    public void saveChunkedText(Long repoId, Long collectionId, List<String> contentList) {
        // 加分布式锁
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock("saveChunkedText_repo_" + repoId + "_collection_" + collectionId);
            if (lockInfo != null) {
                List<KbRepoDataPO> kbRepoDataPOList = new ArrayList<>();
                int chunkIndex = 0;
                for (String content : contentList) {
                    KbRepoDataPO po = KbRepoDataPO.builder()
                        .repoId(repoId)
                        .collectionId(collectionId)
                        .dataType(DataTypeEnum.TEXT.getCode())
                        .dataKey(UUID.randomUUID().toString())
                        .question(content)
                        .chunkIndex(chunkIndex)
                        .handleStatus(HandleStatusEnum.DONE)
                        .build();
                    kbRepoDataPOList.add(po);
                    chunkIndex++;
                }
                dataRepository.batchSaveKbRepoData(kbRepoDataPOList);
                kbRepoDataPOList.forEach(result -> {
                    String dataKey = result.getDataKey();
                    // 如果没有默认索引 新增一条
                    KbRepoDataIndexDTO indexDTO = KbRepoDataIndexDTO.builder()
                        .repoId(repoId)
                        .collectionId(collectionId)
                        .dataKey(dataKey)
                        .indexType(IndexTypeEnum.DEFAULT)
                        .build();
                    dataIndexActionService.createDataIndexOfDefault(repoId, collectionId, Arrays.asList(indexDTO));
                });
            } else {
                throw new LockException();
            }
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    public void updateHandleStatusOfDataKeyList(Long repoId, Long collectionId, List<String> dataKeyListOfSuccess, HandleStatusEnum handleStatusEnum) {
        // 遍历dataKey 然后获取dataKey对应的所有索引 如果索引都为处理成功 更新分块算法处理状态为处理完成
        dataKeyListOfSuccess.forEach(dataKey -> {
            List<KbRepoDataIndexPO> indexPOList = dataIndexRepository.selectDataIndexOfDataKey(repoId, collectionId, dataKey);
            if (indexPOList.stream().allMatch(o -> o.getHandleStatus().equals(HandleStatusEnum.DONE))) {
                dataIndexActionService.updateHandleStatusOfDataKeyList(repoId, collectionId, Arrays.asList(dataKey), handleStatusEnum);
            }
        });
    }

    @IgnoreTenant
    public void updateDataContent(String dataKey, String question, String answer) {
        dataRepository.updateDataContent(dataKey, question, answer);
    }

    public String smartChunkSummaryForData(@Valid KbRepoDataSmartChunkSummaryCommand command) {
        KbRepoBaseInfoPO repoBaseInfoPO = repoBaseInfoRepository.getById(command.getRepoId());
        if (Objects.isNull(repoBaseInfoPO)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        /**
         * 调用大模型生成智能分块摘要
         */
        String smartSummary = "";
        try {
            AIModelManager modelManager = SpringUtil.getBean(AIModelManager.class);
            ChatClient chatClient = modelManager.getChatClient(repoBaseInfoPO.getAgentModelKey());
            String prompt = KbRepoDataActionService.DEFAULT_OPTIMIZATION_PROMPT.replace("{{cluster_content}}", command.getQuestion());
            String aiAnswer = chatClient.prompt(prompt)
                .call()
                .content();
            smartSummary = AIUtils.removeCodeBlock(aiAnswer);
            log.info("智能分块摘要生成成功  smartSummary:{}", smartSummary);
            return smartSummary;
        } catch (Exception e) {
            log.error("智能分块摘要生成失败", e);
            throw new BusinessException(MessageCode.HTTP_ERROR);
        }
    }

    public static final String DEFAULT_OPTIMIZATION_PROMPT = """
            ## 你的任务
            你作为一个段落总结助手，你的任务是总结以下的段落， 小心数字，不要编造。生成的总结要求简洁清晰明确，并与原问题语言相同， 段落如下：。
            {{cluster_content}}
            以上就是你需要总结的内容。
            """;
}

