package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新标签命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagsUpdateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "标签ID")
    @NotNull(message = "标签ID不能为空")
    private Long id;

    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "图标链接")
    private String avatar;
}
