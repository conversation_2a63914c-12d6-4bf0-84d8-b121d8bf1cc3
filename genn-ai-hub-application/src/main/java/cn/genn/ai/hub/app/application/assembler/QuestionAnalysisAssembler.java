package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.QuestionAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.QuestionAnalysisDTO;
import cn.genn.ai.hub.app.application.query.QuestionAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionAnalysisPO;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.utils.jackson.JsonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuestionAnalysisAssembler extends QueryAssembler<QuestionAnalysisQuery, QuestionAnalysisPO, QuestionAnalysisDTO> {

    QuestionAnalysisAssembler INSTANCE = Mappers.getMapper(QuestionAnalysisAssembler.class);

    QuestionAnalysisPO convertPO(QuestionAnalysisCommand command);


    @Mapping(source = "po", target = "relInfoId", qualifiedByName = "toRelInfoIds")
    QuestionAnalysisDTO PO2DTO(QuestionAnalysisPO po);

    @Named(value = "toRelInfoIds")
    default List<Long> toRelInfoIds(QuestionAnalysisPO po) {
        return JsonUtils.parseToList(po.getRelInfoId(), Long.class);
    }

}

