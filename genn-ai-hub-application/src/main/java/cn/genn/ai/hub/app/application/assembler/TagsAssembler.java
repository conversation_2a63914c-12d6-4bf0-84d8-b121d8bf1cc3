package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.TagsCreateCommand;
import cn.genn.ai.hub.app.application.command.TagsRefCreateCommand;
import cn.genn.ai.hub.app.application.command.TagsUpdateCommand;
import cn.genn.ai.hub.app.application.dto.TagsDTO;
import cn.genn.ai.hub.app.application.query.TagsQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.TagsPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TagsRefPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TagsAssembler extends QueryAssembler<TagsQuery, TagsPO, TagsDTO>{

    TagsAssembler INSTANCE = Mappers.getMapper(TagsAssembler.class);

    /**
     * 将创建标签命令转换为标签PO
     *
     * @param command 创建标签命令
     * @return 标签PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    TagsPO command2PO(TagsCreateCommand command);

    /**
     * 将创建标签关联命令转换为标签关联PO
     *
     * @param command 创建标签关联命令
     * @return 标签关联PO
     */
    @Mapping(target = "id", ignore = true)
    TagsRefPO refCommand2PO(TagsRefCreateCommand command);

    /**
     * 将更新标签命令转换为标签PO
     *
     * @param command 更新标签命令
     * @return 标签PO
     */
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "teamId", ignore = true)
    @Mapping(target = "tagType", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    TagsPO updateCommand2PO(TagsUpdateCommand command);
}

