package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoDataIndexDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataIndexQuery;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoDataIndexAssembler extends QueryAssembler<KbRepoDataIndexQuery, KbRepoDataIndexPO, KbRepoDataIndexDTO>{

    KbRepoDataIndexAssembler INSTANCE = Mappers.getMapper(KbRepoDataIndexAssembler.class);

    KbRepoDataIndexPO DTO2PO(KbRepoDataIndexDTO dto);
}

