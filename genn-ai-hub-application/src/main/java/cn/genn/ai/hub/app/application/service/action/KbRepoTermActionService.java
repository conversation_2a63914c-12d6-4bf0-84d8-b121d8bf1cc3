package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.KbRepoTermAssembler;
import cn.genn.ai.hub.app.application.command.IdListCommand;
import cn.genn.ai.hub.app.application.command.KbRepoTermCreateCommand;
import cn.genn.ai.hub.app.application.command.KbRepoTermEditCommand;
import cn.genn.ai.hub.app.application.command.KbRepoTermUploadCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTermExcelImportDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoTermUpdateStatusCommand;
import cn.genn.ai.hub.app.application.processor.BaseExcelListener;
import cn.genn.ai.hub.app.application.service.query.KbRepoFileQueryService;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoTermRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTermPO;
import cn.genn.ai.hub.app.infrastructure.utils.ExcelHandleUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollectionUtil;
import cn.idev.excel.FastExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 知识库术语库业务逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoTermActionService {

    private final KbRepoTermRepositoryImpl termRepository;
    private final KbRepoTermAssembler termAssembler;
    private final FileStorageOperation fileStorageOperation;
    private final KbRepoFileQueryService fileQueryService;

    public Long create(KbRepoTermCreateCommand command) {
        try {
            // 查询下术语是否已经存在
            if (termRepository.getByName(command.getRepoId(), command.getName()) != null) {
                throw new BusinessException(MessageCode.KB_REPO_TERM_NAME_EXIST);
            }
            KbRepoTermPO termPO = KbRepoTermPO.builder()
                .tenantId(CurrentUserHolder.getTenantId())
                .repoId(command.getRepoId())
                .name(command.getName())
                .description(command.getDescription())
                .nameAliases(command.getNameAliases())
                .enabled(true)
                .build();
            termRepository.insert(termPO);
            return termPO.getId();
        } catch (Exception e) {
            log.error("create term error, command: {}", command, e);
            throw e;
        }
    }

    public Boolean edit(KbRepoTermEditCommand command) {
        try {
            // 查询下术语是否已经存在
            KbRepoTermPO kbRepoTermPO = termRepository.getByName(command.getRepoId(), command.getName());
            if (Objects.nonNull(kbRepoTermPO) && !Objects.equals(kbRepoTermPO.getId(), command.getId())) {
                throw new BusinessException(MessageCode.KB_REPO_TERM_NAME_EXIST);
            }
            KbRepoTermPO po = termAssembler.kbRepoTermEditCommand2KbRepoTermPO(command);
            return termRepository.editTerm(po);
        } catch (Exception e) {
            log.error("edit term error, command: {}", command, e);
            throw e;
        }
    }

    public Boolean updateStatus(KbRepoTermUpdateStatusCommand command) {
        try {
            KbRepoTermPO po = KbRepoTermPO.builder()
                .id(command.getId())
                .enabled(command.getEnabled())
                .build();
            return termRepository.updateStatus(po);
        } catch (Exception e) {
            log.error("update term status error, command: {}", command, e);
            throw e;
        }
    }

    public Boolean delete(IdCommand command) {
        try {
            return termRepository.removeById(command.getId());
        } catch (Exception e) {
            log.error("delete term error, command: {}", command, e);
            throw e;
        }
    }

    public Boolean batchDelete(IdListCommand command) {
        try {
            List<Long> ids = command.getIds();
            return termRepository.batchDelete(ids);
        } catch (Exception e) {
            log.error("batch delete terms error, command: {}", command, e);
            throw e;
        }
    }

    public Boolean importExcel(KbRepoTermUploadCommand command) {
        try {
            KbRepoFileDTO fileInfo = fileQueryService.get(IdQuery.builder().id(command.getFileId()).build());
            if (fileInfo == null) {
                log.error("导入Excel文件为空");
                return false;
            }
            InputStream inputStream = fileStorageOperation.getFile(fileInfo.getExternalFileId());
            // 检查文件类型
            String originalFilename = fileInfo.getFileName();
            if (originalFilename == null || !(originalFilename.endsWith(".xlsx") || originalFilename.endsWith(".xls"))) {
                log.error("文件格式不正确，只支持.xlsx或.xls格式");
                throw new BusinessException(MessageCode.KB_REPO_FILE_CHECK_EXCEL_FAIL);
            }
            ExcelHandleUtil.ValidateResult validateResult = ExcelHandleUtil.validateFirstRow(inputStream, KbRepoTermExcelImportDTO.class);
            if (!validateResult.isValid()) {
                throw new BusinessException(MessageCode.IMPORT_EXCEL_HEAD_NOT_MATCH);
            }
            inputStream = validateResult.getResetInputStream();
            BaseExcelListener<KbRepoTermExcelImportDTO> baseExcelListener = new BaseExcelListener<>();
            FastExcel.read(inputStream, KbRepoTermExcelImportDTO.class, baseExcelListener).sheet().doRead();
            // 得到读取数据
            List<KbRepoTermExcelImportDTO> dataList = baseExcelListener.getDataList();
            // 过滤没有name的数据
            dataList.removeIf(item -> item.getName() == null || item.getName().trim().isEmpty());
            // 过滤术语已经存在了的记录
            List<String> nameList = dataList.stream().map(KbRepoTermExcelImportDTO::getName).distinct().toList();
            List<KbRepoTermPO> existTermList = termRepository.getByNameList(command.getRepoId(), nameList);
            dataList.removeIf(item -> existTermList.stream().anyMatch(existItem -> item.getName().equals(existItem.getName())));
            if (CollectionUtil.isEmpty(dataList)) {
                throw new BusinessException(MessageCode.KB_REPO_TERM_IMPORT_EXCEL_NO_DATA);
            }
            List<KbRepoTermPO> poList = termAssembler.kbRepoTermExcelImportDTO2PO(dataList);
            poList.forEach(item -> {
                item.setTenantId(CurrentUserHolder.getTenantId());
                item.setRepoId(command.getRepoId());
            });
            return termRepository.batchInsert(poList);
        } catch (Exception e) {
            log.error("import excel error", e);
            throw e;
        }
    }
}

