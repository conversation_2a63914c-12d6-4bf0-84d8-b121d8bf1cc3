package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * KnowledgeGapAnalysis查询对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionGapAnalysisQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流id")
    @NotEmpty(message = "工作流ID不能为空")
    private String appId;

    @Schema(description = "来源")
    private List<String> sources;

    @Schema(description = "会话ID-支持批量查询一个工作流下的回话集")
    private List<String> chatIds;

    @Schema(description = "知识缺口标签(类型)")
    private String type;

}

