package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.AgentChannelAssembler;
import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.request.AgentChannelSaveCommand;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.*;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TenantChannelRefPO;
import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import cn.genn.core.exception.BusinessException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 智能体发布渠道应用服务
 * @date 2025-04-25
 */
@Service
@RequiredArgsConstructor
public class AgentChannelActionService {

    private final TenantChannelRefRepositoryImpl tenantChannelRefRepository;

    private final AgentChannelRepositoryImpl agentChannelRepository;

    private final AgentInfoRepositoryImpl agentInfoRepository;

    private final AgentChannelAssembler agentChannelAssembler;

    public void handleChannel(AgentChannelSaveCommand command) {
        if (command.getChannelType() == ChannelTypeEnum.APP_MARKET) {
            handlerAppMarketChannel(command);
            return;
        }
        TenantChannelRefPO tenantChannelRefPO = tenantChannelRefRepository.getBaseMapper().selectById(command.getChannelRefId());
        if (tenantChannelRefPO == null) {
            throw new BusinessException("租户渠道不存在");
        }

        AgentInfoDTO agentInfoDTO = agentInfoRepository.getAgentByWorkflowId(command.getWorkflowId());
        if (agentInfoDTO == null) {
            throw new BusinessException("智能体不存在");
        }

        // 校验业务唯一性
        boolean existFlag = agentChannelRepository.exists(
            Wrappers.<AgentChannelPO>lambdaQuery()
                .eq(AgentChannelPO::getAgentId, agentInfoDTO.getId())
                .ne(command.getId() != null, AgentChannelPO::getId, command.getId())
                .eq(AgentChannelPO::getChannelRefId, command.getChannelRefId()));
        if (existFlag) {
            throw new BusinessException("已发布渠道，请勿重复发布");
        }

        AgentChannelPO agentChannelPO = Optional.ofNullable(command.getId())
            .flatMap(agentChannelRepository::findById)
            .orElse(agentChannelAssembler.command2Po(command));

        if (command.getId() != null) {
            agentChannelPO.setAliasName(command.getAliasName());
            agentChannelPO.setChannelType(command.getChannelType());
            agentChannelPO.setChannelRefId(command.getChannelRefId());
        } else {
            agentChannelPO.setAgentId(agentInfoDTO.getId());
        }

        // 智能体应用特殊校验
        checkAppBindingUniqueness(tenantChannelRefPO.getChannelId(), agentChannelPO.getAgentId(), command.getId() != null);
        if (agentChannelPO.getId() != null) {
            agentChannelRepository.updateById(agentChannelPO);
        } else {
            agentChannelRepository.save(agentChannelPO);
        }
    }

    private void handlerAppMarketChannel(AgentChannelSaveCommand command) {
        AgentInfoDTO agentInfoDTO = agentInfoRepository.getAgentByWorkflowId(command.getWorkflowId());
        if (agentInfoDTO == null) {
            throw new BusinessException("智能体不存在");
        }
        AgentChannelPO agentChannelPO = Optional.ofNullable(command.getId())
            .flatMap(agentChannelRepository::findById)
            .orElse(agentChannelAssembler.command2Po(command));
        if (command.getId() != null) {
            agentChannelPO.setAliasName(command.getAliasName());
            agentChannelPO.setChannelType(command.getChannelType());
            agentChannelPO.setChannelRefId(0L);
        } else {
            agentChannelPO.setAgentId(agentInfoDTO.getId());
        }
        if (agentChannelPO.getId() != null) {
            agentChannelRepository.updateById(agentChannelPO);
        } else {
            agentChannelRepository.save(agentChannelPO);
        }
    }

    private void checkAppBindingUniqueness(Long channelId, Long agentId, Boolean isUpdate) {
        AgentChannelPO agentChannelPO = agentChannelRepository.countByChannelId(channelId);
        if (agentChannelPO == null) {
            return;
        }
        if (!isUpdate || !agentChannelPO.getAgentId().equals(agentId)) {
            throw new BusinessException("智能体已绑定渠道");
        }
    }

    public void deleteAgentChannel(Long id) {
        agentChannelRepository.findById(id).orElseThrow(() -> new BusinessException("智能体发布渠道不存在"));
        agentChannelRepository.getBaseMapper().deleteById(id);
    }
}
