package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.KbRepoDataAssembler;
import cn.genn.ai.hub.app.application.assembler.KbRepoDataIndexAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoDataDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoDataExtDataDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataQuery;
import cn.genn.ai.hub.app.application.enums.DataTypeEnum;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoDataMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataIndexRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.OrderBy;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.page.SortOrder;
import cn.genn.core.utils.VirtualThreadUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.milvus.common.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoDataQueryService {

    private final KbRepoDataMapper mapper;
    private final KbRepoDataAssembler assembler;
    private final KbRepoDataIndexAssembler repoDataIndexAssembler;
    private final KbRepDataIndexRepositoryImpl dataIndexRepository;
    private final KbRepDataRepositoryImpl repoDataRepository;
    private final FileStorageOperation fileStorageOperation;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoDataDTO分页对象
     */
    public PageResultDTO<KbRepoDataDTO> page(KbRepoDataQuery query) {
        QueryWrapper<KbRepoDataPO> dataQuery = buildDataQuery(query);
        OrderBy sort = query.getSort();
        if (sort != null) {
            Map<String, SortOrder> orderBy = Optional.ofNullable(sort.getOrderBy()).orElse(new HashMap<>());
            orderBy.forEach((colum, type) -> {
                boolean isAsc = SortOrder.ASC.equals(type);
                dataQuery.orderBy(true, isAsc, colum);
            });
        }
        PageResultDTO<KbRepoDataDTO> pageResult = assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), dataQuery));
        List<KbRepoDataDTO> result = pageResult.getList();
        if (CollUtil.isNotEmpty(result)) {
            for (KbRepoDataDTO dto : result) {
                List<KbRepoDataIndexPO> indexList = dataIndexRepository.selectCustomerIndexFromCollection(dto.getRepoId(), dto.getCollectionId());
                dto.setIndexInfoList(repoDataIndexAssembler.PO2DTO(indexList));
                handleDataExtData(dto);
            }
        }
        return pageResult;
    }

    private QueryWrapper<KbRepoDataPO> buildDataQuery(KbRepoDataQuery query) {
        QueryWrapper<KbRepoDataPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            .eq(Objects.nonNull(query.getRepoId()), KbRepoDataPO::getRepoId, query.getRepoId())
            .eq(Objects.nonNull(query.getCollectionId()), KbRepoDataPO::getCollectionId, query.getCollectionId());

        if (StringUtils.isNotBlank(query.getSearchText()) && !query.getSearchText().matches("%+")) {
            queryWrapper
                .lambda()
                .and(wrapper -> wrapper
                    .like(KbRepoDataPO::getQuestion, query.getSearchText())
                    .or()
                    .like(KbRepoDataPO::getAnswer, query.getSearchText()));
        }
        queryWrapper
            .lambda()
            .eq(KbRepoDataPO::getDeleted, DeletedEnum.NOT_DELETED);
        OrderBy sort = query.getSort();
        if (sort != null) {
            Map<String, SortOrder> orderBy = sort.getOrderBy();
            orderBy.forEach((key, value) -> {
                SortOrder sortOrder = SortOrder.of(String.valueOf(value));
                queryWrapper.orderBy(true, sortOrder.equals(SortOrder.ASC), key);

            });
        }
        return queryWrapper;
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoDataDTO
     */
    @IgnoreTenant
    public KbRepoDataDTO get(IdQuery query) {
        KbRepoDataDTO kbRepoDataDTO = assembler.PO2DTO(mapper.selectById(query.getId()));
        List<KbRepoDataIndexPO> indexList = dataIndexRepository.selectCustomerIndexFromCollection(kbRepoDataDTO.getRepoId(), kbRepoDataDTO.getCollectionId(), kbRepoDataDTO.getDataKey());
        kbRepoDataDTO.setIndexInfoList(repoDataIndexAssembler.PO2DTO(indexList));
        handleDataExtData(kbRepoDataDTO);
        return kbRepoDataDTO;
    }

    /**
     * 按知识库id和集合id查询数据分块的总数
     */
    public Long countByRepoIdAndCollectionId(Long repoId, Long collectionId) {
        return repoDataRepository.countByRepoIdAndCollectionId(repoId, collectionId);
    }

    @IgnoreTenant
    public List<KbRepoDataDTO> selectDataByKeys(Collection<String> dateKeys) {
        List<KbRepoDataDTO> dtos = repoDataRepository.selectDateByKeys(dateKeys);
        return VirtualThreadUtils.processInParallelWithExceptionHandling(dtos, dto -> {
            handleDataExtData(dto);
            return dto;
        });
    }

    @IgnoreTenant
    public KbRepoDataDTO selectDataByKey(String dataKey) {
        KbRepoDataPO po = repoDataRepository.queryByDataKey(dataKey);
        return assembler.PO2DTO(po);
    }

    public void handleDataExtData(KbRepoDataDTO dto) {
        // 判断缓存中是否存在图片链接
        if (DataTypeEnum.IMAGE.getCode().equals(dto.getDataType()) && StrUtil.isNotBlank(dto.getExtData())) {
            try {
                KbRepoDataExtDataDTO extDataDTO = JsonUtils.fromJson(dto.getExtData(), KbRepoDataExtDataDTO.class);
                if (extDataDTO != null && StrUtil.isNotBlank(extDataDTO.getPath())) {
                    String url = fileStorageOperation.getCachedPreSignedUrl(extDataDTO.getPath(), extDataDTO.getPath());
                    // 如果返回的链接和当前不一致 更新目前库里存的链接
                    if (!extDataDTO.getUrl().equals(url)) {
                        extDataDTO.setUrl(url);
                        repoDataRepository.updateDataExtData(dto.getId(), JsonUtils.toJson(extDataDTO));
                    }
                    dto.setExtData(JsonUtils.toJson(extDataDTO));
                }
            } catch (Exception e) {
                log.error("更新分块的图片链接失败", e);
            }
        }
    }
}

