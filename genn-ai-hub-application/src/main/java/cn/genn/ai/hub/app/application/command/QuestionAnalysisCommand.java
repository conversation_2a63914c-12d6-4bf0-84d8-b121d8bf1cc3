package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * QuestionAnalysis操作对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionAnalysisCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流id")
    private String appId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "问题精炼")
    private String concise;

    @Schema(description = "问题类型")
    private String type;

    @Schema(description = "问题出现次数")
    private Integer count;

    @Schema(description = "问题来源-数组[id1, id2, id3...]")
    private List<Long> relInfoIds;

}

