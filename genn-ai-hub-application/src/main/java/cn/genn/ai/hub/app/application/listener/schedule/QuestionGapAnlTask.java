package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import cn.genn.ai.hub.app.domain.question.service.QuestionGapAnalysisService;
import cn.genn.ai.hub.app.infrastructure.config.AgentProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionRepositoryImpl;
import cn.genn.core.utils.jackson.JsonUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QuestionGapAnlTask extends IJobHandler {

    @Resource
    private QuestionRepositoryImpl questionRepository;
    @Resource
    private QuestionGapAnalysisService gapAnalysisQueryService;
    @Resource
    private GennAIHubProperties gennAIHubProperties;

    @Data
    private static class Params {

        private String appId;

        private Integer minusDays = 1;
    }

    /**
     * TODO
     *
     * @throws Exception
     */
    @Override
    public void execute() throws Exception {
        log.info("QuestionHighFreAnlTask begin");
        String jobParam = XxlJobHelper.getJobParam();
        Params params = Optional.ofNullable(JsonUtils.parse(jobParam, Params.class)).orElse(new Params());

        List<QuestionInfoDTO> infoDTOS = questionRepository.getAllAnalysisInfo(params.getAppId());
        Set<String> appIds = infoDTOS.stream().map(QuestionInfoDTO::getAppId).collect(Collectors.toSet());
        AgentProperties agent = gennAIHubProperties.getAgent();
        List<String> filter = agent.getFilter();
        for (String appId : appIds) {
            if (filter.contains(appId)) {
                continue;
            }
            gapAnalysisQueryService.gapAnalysisCallback(appId, params.getMinusDays());
        }
        log.info("QuestionHighFreAnlTask end");
    }


}
