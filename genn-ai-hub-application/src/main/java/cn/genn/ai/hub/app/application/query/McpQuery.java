package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.McpTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import cn.genn.database.mybatisplus.query.annotation.QueryType;
import cn.genn.database.mybatisplus.query.constant.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Mcp查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class McpQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "mcp名称")
    @QueryType(condition = Condition.LIKE)
    private String name;

    @Schema(description = "mcp类型，stdio:STDIO-标准输入输出,sse:SSE-服务器发送事件,streamablehttp:STREAMABLEHTTP-可流式HTTP,openapi:OPENAPI-开放API")
    @QueryType
    private McpTypeEnum mcpType;

}

