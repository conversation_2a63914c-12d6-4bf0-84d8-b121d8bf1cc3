package cn.genn.ai.hub.app.application.service.govow;

import cn.genn.ai.hub.app.application.assembler.GovowOperationDrainageAssembler;
import cn.genn.ai.hub.app.application.command.GovowFsQuestionAddCacheCommand;
import cn.genn.ai.hub.app.application.command.GovowOperationDrainageCreateCommand;
import cn.genn.ai.hub.app.application.dto.GovowCacheDTO;
import cn.genn.ai.hub.app.application.dto.request.WorkFlowWebModel;
import cn.genn.ai.hub.app.application.listener.govow.GovowFsQuestionPushParam;
import cn.genn.ai.hub.app.application.service.GovowOperationDrainageService;
import cn.genn.ai.hub.app.domain.question.service.QuestionGapAnalysisService;
import cn.genn.ai.hub.app.infrastructure.config.AgentProperties;
import cn.genn.ai.hub.app.infrastructure.config.DailyAnalysisProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.AgentAppConstants;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.repository.po.GovowOperationDrainagePO;
import cn.genn.ai.hub.app.infrastructure.utils.AsyncExecutor;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.feishu.base.FeishuBaseInputParam;
import cn.genn.feishu.contact.FeishuContactQueryUtils;
import cn.genn.feishu.contact.model.FeishuContactDepartmentChildrenParam;
import cn.genn.feishu.contact.model.FeishuContactDepartmentParam;
import cn.genn.feishu.contact.model.FeishuContactTelephoneParam;
import cn.genn.feishu.contact.model.FeishuContactUserByDepartmentParam;
import cn.genn.feishu.table.BitableRecordParser;
import cn.genn.feishu.table.FeishuBitableQueryUtils;
import cn.genn.feishu.table.model.FeishuBitableQueryParam;
import cn.genn.feishu.table.model.FeishuBitableQueryResult;
import cn.genn.feishu.table.model.field.FieldValue;
import cn.genn.feishu.table.model.field.UserFieldValue;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import com.lark.oapi.service.bitable.v1.model.Condition;
import com.lark.oapi.service.bitable.v1.model.FilterInfo;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GovowOperationService {

    private final StringRedisTemplate stringRedisTemplate;
    private final QuestionGapAnalysisService questionGapAnalysisService;
    private final GennAIHubProperties gennAIHubProperties;
    private final GovowOperationDrainageService govowOperationDrainageService;
    private final GovowOperationDrainageAssembler govowOperationDrainageAssembler;

    private final static String CREATE_USER = "创建人";
    private final static String TODAY_WORK_DIFFICULTY = "今日工作困难点及原因";
    private final static String STUDY_AND_INNOVATION = "学习与创新";
    private final static String CENTER_DEPARTMENT = "中心事业部";
    private final static String DEPARTMENT = "部门";
    private final static String TODAY_WORK_COMPLETION = "今日工作完成情况";
    private final static String JOB_RESPONSIBILITY = "核心工作职责";
    private final static String REPORT_DATE = "日报日期";

    public void pushQuestionToFeishu(GovowFsQuestionPushParam pushParam) {
        //查询人员范围
        List<String> openIds = new ArrayList<>();
        if (ObjUtil.isNotNull(pushParam.getSendRange())) {
            openIds = getUserRange(pushParam.getSendRange());
            if (CollUtil.isEmpty(openIds)) {
                log.warn("GovowOperationService pushQuestionToFeishu users is empty");
                return;
            }
        }

        FeishuBitableQueryParam tableParam = buildFeishuBitableQueryParam(openIds, pushParam.getQueryTimeRange());
        log.info("tableParam:{}", JsonUtils.toJson(tableParam));
        //查询多维表格记录
        FeishuBitableQueryResult feishuBitableQueryResult = FeishuBitableQueryUtils.queryRecords(tableParam);
        //提取关键列
        List<AppTableRecord> records = feishuBitableQueryResult.getRecords();
        if (CollUtil.isEmpty(records)) {
            log.warn("GovowOperationService pushQuestionToFeishu records is empty");
            return;
        }
        List<Map<String, FieldValue>> items = BitableRecordParser.parseRecords(records, tableParam.getFieldNames());

        //用户分组处理
        Map<UserFieldValue.UserInfo, List<Map<String, FieldValue>>> userFieldValueMap = groupByUser(items,openIds);
        List<String> finalOpenIds = openIds;
        userFieldValueMap.forEach((user, fieldValues) -> {
            CompletableFuture.runAsync(() -> pushQuestionSimple(finalOpenIds, user, fieldValues), AsyncExecutor.GOVOW_OPERATION_SERVICE)
                    .exceptionally((ex -> {
                        log.error("GovowOperationService pushQuestionToFeishu error: ", ex);
                        return null;
                    }));
        });
    }

    /**
     * 处理单个用户
     *
     * @param openIds
     * @param user
     * @param fieldValues
     */
    private void pushQuestionSimple(List<String> openIds, UserFieldValue.UserInfo user, List<Map<String, FieldValue>> fieldValues) {
        if (CollUtil.isNotEmpty(openIds) && !openIds.contains(user.getId())) {
            return;
        }
        //提取字段
        log.info("fieldValues:{}", JsonUtils.toJson(fieldValues));
        List<String> questions = extractValueFromField(fieldValues, TODAY_WORK_DIFFICULTY);
        List<String> innovations = extractValueFromField(fieldValues, STUDY_AND_INNOVATION);
        List<String> coreJobs = extractValueFromField(fieldValues, JOB_RESPONSIBILITY);
        List<String> todayWorks = extractValueFromField(fieldValues, TODAY_WORK_COMPLETION);
        List<String> departments = extractDepartmentFromSelect(fieldValues, DEPARTMENT);
        if (CollUtil.isEmpty(questions) && CollUtil.isEmpty(innovations) && CollUtil.isEmpty(coreJobs) && CollUtil.isEmpty(todayWorks)) {
            return;
        }

        //添加缓存
        String cacheKey = IdUtil.fastSimpleUUID();
        String chatId = IdUtil.fastSimpleUUID();
        GovowCacheDTO cacheDTO = new GovowCacheDTO()
                .setOpenId(user.getId())
                .setCacheKey(cacheKey)
                .setName(user.getName())
                .setOriginalQuestion(JsonUtils.toJson(questions))
                .setInnovations(JsonUtils.toJson(innovations))
                .setCoreJobs(JsonUtils.toJson(coreJobs))
                .setTodayWorks(JsonUtils.toJson(todayWorks))
                .setChatId(chatId)
                .setDepartment(Optional.ofNullable(departments)
                        .filter(list -> !list.isEmpty())
                        .map(List::getFirst)
                        .orElse(""));
        stringRedisTemplate.opsForValue().set(CacheConstants.CACHE_PRE + CacheConstants.FS_MULTI_TABLE_DATA_CACHE + cacheKey, JsonUtils.toJson(cacheDTO), 7, TimeUnit.DAYS);

        //执行agent
        AgentProperties agent = gennAIHubProperties.getAgent();
        String url = agent.getInvokeDomain() + AgentAppConstants.INVOKE_APP_URL;

        WorkFlowWebModel workFlowWebModel = buildRequestModel(cacheKey, questions, innovations, coreJobs, todayWorks, user.getId(),chatId);
        boolean result = questionGapAnalysisService.invokeWorkflow(url, JsonUtils.toJson(workFlowWebModel), agent.getGovowTableApiKey(), 1);
        if (!result) {
            log.warn("GovowOperationService pushQuestionToFeishu invokeWorkflow fail, user: {}, cacheKey: {}", JsonUtils.toJson(user), cacheKey);
        }

    }

    /**
     * 添加缓存
     *
     * @param command
     */
    public void addAgentCache(GovowFsQuestionAddCacheCommand command) {
        String key = CacheConstants.CACHE_PRE + CacheConstants.FS_MULTI_TABLE_DATA_CACHE + command.getCacheKey();
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isNotBlank(value)) {
            GovowCacheDTO govowCacheDTO = JsonUtils.parse(value, GovowCacheDTO.class);
            if (StrUtil.isNotBlank(command.getQuestionShort()) && StrUtil.isNotBlank(command.getQuestionDetail())) {
                govowCacheDTO.setQuestionShort(command.getQuestionShort());
                govowCacheDTO.setQuestionDetail(command.getQuestionDetail());
                stringRedisTemplate.opsForValue().set(key, JsonUtils.toJson(govowCacheDTO), 7, TimeUnit.DAYS);
            } else {
                stringRedisTemplate.delete(key);
            }
        }
    }

    /**
     * 埋点数据
     *
     * @param cacheKey
     * @return
     */
    public GovowCacheDTO buryingPoint(String cacheKey) {
        String key = CacheConstants.CACHE_PRE + CacheConstants.FS_MULTI_TABLE_DATA_CACHE + cacheKey;
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isEmpty(value)) {
            GovowOperationDrainagePO drainagePO = govowOperationDrainageService.selectByCachekey(cacheKey);
            if(ObjUtil.isNotNull(drainagePO)){
                return govowOperationDrainageAssembler.po2DTO(drainagePO).setCacheKey(null);
            }
            return null;
        }
        GovowCacheDTO govowCacheDTO = JsonUtils.parse(value, GovowCacheDTO.class);
        stringRedisTemplate.delete(key);
        log.info("GovowOperationService buryingPoint govowCacheDTO: {}", JsonUtils.toJson(govowCacheDTO));

        //写入数据库;
        Map<String, Object> params = new HashMap<>();
        params.put("questions", govowCacheDTO.getOriginalQuestion());
        params.put("innovations", govowCacheDTO.getInnovations());
        params.put("coreJobs", govowCacheDTO.getCoreJobs());
        params.put("todayWorks", govowCacheDTO.getTodayWorks());
        GovowOperationDrainageCreateCommand command = new GovowOperationDrainageCreateCommand()
                .setCacheKey(cacheKey)
                .setOpenId(govowCacheDTO.getOpenId())
                .setUserName(govowCacheDTO.getName())
                .setQuestionShort(govowCacheDTO.getQuestionShort())
                .setQuestionDetail(govowCacheDTO.getQuestionDetail())
                .setDepartment(govowCacheDTO.getDepartment())
                .setChatId(govowCacheDTO.getChatId())
                .setExtraInfo(JsonUtils.toJson(params));
        govowOperationDrainageService.create(command);
        return govowCacheDTO;
    }

    /**
     * 构建多维表格请求
     */
    private FeishuBitableQueryParam buildFeishuBitableQueryParam(List<String> openIds, GovowFsQuestionPushParam.QueryTimeRange queryTimeRange) {
        DailyAnalysisProperties dailyAnalysis = gennAIHubProperties.getDailyAnalysis();
        return new FeishuBitableQueryParam()
                .setPageSize(500)
                .setAppInfo(new FeishuBaseInputParam().setAppId(dailyAnalysis.getAppId()).setAppSecret(dailyAnalysis.getAppSecret()))
                .setTableUrl(dailyAnalysis.getTableUrl())
                .setFieldNames(new String[]{CREATE_USER, TODAY_WORK_DIFFICULTY, STUDY_AND_INNOVATION, CENTER_DEPARTMENT, DEPARTMENT, TODAY_WORK_COMPLETION, REPORT_DATE, JOB_RESPONSIBILITY})
                .setFilter(buildFilter(openIds, queryTimeRange));
    }

    /**
     * 构建请求模型对象
     * innovations,coreJobs,todayWorks
     */
    private WorkFlowWebModel buildRequestModel(String cacheKey, List<String> questions, List<String> innovates, List<String> coreJobs, List<String> todayWorks, String openId,String chatId) {
        WorkFlowWebModel request = new WorkFlowWebModel();
        WorkFlowWebModel.Message message = request.getMessages().getFirst();
        message.setContent("集团日报分析");
        Map<String, Object> variables = new HashMap<>();
        variables.put("cacheKey", cacheKey);
        variables.put("questions", JsonUtils.toJson(questions));
        variables.put("innovates", JsonUtils.toJson(innovates));
        variables.put("coreJobs", JsonUtils.toJson(coreJobs));
        variables.put("todayWorks", JsonUtils.toJson(todayWorks));
        variables.put("openId", openId);
        variables.put("chatId",chatId);
        variables.put("url", gennAIHubProperties.getDailyAnalysis().getJumpUrl());
        request.setVariables(variables);
        return request;
    }

    private Map<UserFieldValue.UserInfo, List<Map<String, FieldValue>>> groupByUser(List<Map<String, FieldValue>> items,List<String> openIds) {
        Map<UserFieldValue.UserInfo, List<Map<String, FieldValue>>> resultMap = items.stream()
            .filter(item -> item.containsKey(CREATE_USER))
            .collect(Collectors.groupingBy(item -> {
                UserFieldValue userFieldValue = (UserFieldValue) item.get(CREATE_USER);
                return userFieldValue.getFirstUser();
            }));
        if(CollUtil.isNotEmpty(openIds)){
            resultMap.entrySet().removeIf(entry -> !openIds.contains(entry.getKey().getId()));
        }
        return resultMap;
    }


    private FilterInfo buildFilter(List<String> openIds, GovowFsQuestionPushParam.QueryTimeRange queryTimeRange) {
        List<Condition> conditions = new ArrayList<>();

        //日期范围处理,默认查询上周一到周日数据
        long startStamp = getLastLastSunday();
        long endStamp = getThisMonday();
        if (ObjUtil.isNotNull(queryTimeRange)) {
            if (ObjUtil.isNotNull(queryTimeRange.getEndTime()) && ObjUtil.isNotNull(queryTimeRange.getStartTime())) {
                startStamp = queryTimeRange.getStartTime().minusDays(1).atStartOfDay(ZoneOffset.of("+08:00")).toInstant().toEpochMilli();
                endStamp = queryTimeRange.getEndTime().plusDays(1).atStartOfDay(ZoneOffset.of("+08:00")).toInstant().toEpochMilli();
            } else if (StrUtil.isNotBlank(queryTimeRange.getType())) {
                switch (queryTimeRange.getType()) {
                    case "yesterday":
                        startStamp = DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -2)).getTime();
                        endStamp = DateUtil.beginOfDay(DateUtil.date()).getTime();
                        break;
                    case "thisWeek":
                        startStamp = DateUtil.lastWeek().getTime();
                        endStamp = DateUtil.nextWeek().getTime();

                        break;
                    case "lastWeek":
                        startStamp = getLastLastSunday();
                        endStamp = getThisMonday();
                        break;
                    case "customize":
                        startStamp = queryTimeRange.getStartTime().minusDays(1).atStartOfDay(ZoneOffset.of("+08:00")).toInstant().toEpochMilli();
                        endStamp = queryTimeRange.getEndTime().plusDays(1).atStartOfDay(ZoneOffset.of("+08:00")).toInstant().toEpochMilli();
                }
            }
        }
        conditions.add(Condition.newBuilder()
                .fieldName(REPORT_DATE)
                .operator("isGreater")
                .value(new String[]{"ExactDate",
                        String.valueOf(startStamp)})
                .build());
        conditions.add(Condition.newBuilder()
                .fieldName(REPORT_DATE)
                .operator("isLess")
                .value(new String[]{"ExactDate",
                        String.valueOf(endStamp)})
                .build());
        //人员范围处理(人员数量多了就会报错,功能不通用)
//        if (CollUtil.isNotEmpty(openIds)) {
//            conditions.add(Condition.newBuilder()
//                    .fieldName(CREATE_USER)
//                    .operator("contains")
//                    .value(openIds.toArray(new String[openIds.size()]))
//                    .build());
//        }
        return FilterInfo.newBuilder()
                .conjunction("and")
                .conditions(conditions.toArray(new Condition[conditions.size()]))
                .build();
    }

    private List<String> getUserRange(GovowFsQuestionPushParam.SendRange range) {
        DailyAnalysisProperties dailyAnalysis = gennAIHubProperties.getDailyAnalysis();
        FeishuBaseInputParam feishuBaseInfo = new FeishuBaseInputParam()
                .setAppId(dailyAnalysis.getAppId())
                .setAppSecret(dailyAnalysis.getAppSecret());
        //用户手机号范围
        List<String> openIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(range.getTelephones())) {
            FeishuContactTelephoneParam param = new FeishuContactTelephoneParam()
                    .setAppInfo(feishuBaseInfo)
                    .setTelephones(range.getTelephones());
            List<UserContactInfo> contactInfos = FeishuContactQueryUtils.getUserByTelephone(param);
            if (CollUtil.isNotEmpty(contactInfos)) {
                List<String> collect = contactInfos.stream().map(UserContactInfo::getUserId).distinct().toList();
                openIds.addAll(collect);
            }
        }
        //部门用户范围
        if (CollUtil.isNotEmpty(range.getDepartments())) {
            List<String> userByDepartmentIds = getUserByDepartmentIds(range.getDepartments(), feishuBaseInfo);
            if(CollUtil.isNotEmpty(userByDepartmentIds)){
                openIds.addAll(userByDepartmentIds);
            }
        }
        return openIds.stream().filter(StrUtil::isNotBlank).distinct().toList();
    }

    private List<String> extractDepartmentFromSelect(List<Map<String, FieldValue>> fieldValues, String fieldName) {
        return fieldValues.stream()
                .filter(item -> item.containsKey(fieldName))
                .map(item -> {
                    FieldValue fieldValue = item.get(fieldName);
                    Object parsedValue = fieldValue.getParsedValue();

                    if (parsedValue instanceof List) {
                        List<?> parsedList = (List<?>) parsedValue;
                        StringBuilder result = new StringBuilder();
                        for (Object element : parsedList) {
                            if (ObjUtil.isNotNull(element)) {
                                String jsonStr = JsonUtils.toJson(element);
                                List<String> strings = JsonUtils.parseToList(jsonStr, String.class);
                                return Optional.of(strings).map(string -> strings.get(0)).orElse("");
                            }
                        }
                        return result.toString();
                    } else if (parsedValue != null) {
                        return parsedValue.toString();
                    }
                    return "";
                })
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 从字段值中提取问题文本
     *
     * @param fieldValues 字段值列表
     * @param fieldName   字段名称
     * @return 问题文本列表
     */
    private List<String> extractValueFromField(List<Map<String, FieldValue>> fieldValues, String fieldName) {
        return fieldValues.stream()
                .filter(item -> item.containsKey(fieldName))
                .map(item -> {
                    FieldValue fieldValue = item.get(fieldName);
                    Object parsedValue = fieldValue.getParsedValue();

                    if (parsedValue instanceof List) {
                        List<?> parsedList = (List<?>) parsedValue;
                        StringBuilder result = new StringBuilder();
                        for (Object element : parsedList) {
                            String jsonStr = JsonUtils.toJson(element);
                            if (jsonStr.startsWith("{") && jsonStr.endsWith("}")) {
                                // 尝试解析为Map
                                Map<String, Object> map = JsonUtils.parse(jsonStr, Map.class);
                                Object textObj = map.get("text");
                                if (textObj != null) {
                                    return textObj.toString();
                                }
                            }
                        }
                        return result.toString();
                    } else if (parsedValue != null) {
                        return parsedValue.toString();
                    }
                    return "";
                })
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 获取部门下所有用户信息id
     * @param departmentIds
     * @return
     */
    private List<String> getUserByDepartmentIds(List<String> departmentIds,FeishuBaseInputParam feishuBaseInfo){
        FeishuContactDepartmentParam departmentParam = new FeishuContactDepartmentParam()
                .setDepartmentIds(departmentIds)
                .setAppInfo(feishuBaseInfo);
        // 获取部门信息,保证部门存在
        List<Department> departmentBatch = FeishuContactQueryUtils.getDepartmentBatch(departmentParam);
        Map<Boolean, List<String>> groupedDepartments = departmentBatch.stream().map(Department::getOpenDepartmentId)
                .collect(Collectors.partitioningBy(departmentIds::contains));
        if (CollUtil.isNotEmpty(groupedDepartments.get(false))) {
            log.warn("有不存在的部门:{}", JsonUtils.toJson(groupedDepartments.get(false)));
        }
        // 1.递归获取部门
        List<String> departmentIdAllList = this.getDepartments(groupedDepartments.get(true),feishuBaseInfo);
        // 2.获取以上所有部门的用户信息
        List<CompletableFuture<List<User>>> futures = departmentIdAllList.stream()
                .map(id -> CompletableFuture.supplyAsync(() -> {
                    FeishuContactUserByDepartmentParam param = new FeishuContactUserByDepartmentParam()
                            .setAppInfo(feishuBaseInfo)
                            .setDepartmentId(id);
                    List<User> userListByDepartment = FeishuContactQueryUtils.getUserListByDepartment(param);
                    return CollUtil.isNotEmpty(userListByDepartment) ? userListByDepartment : new ArrayList<User>();
                }, AsyncExecutor.RESOURCE_SERVICE).exceptionally((ex -> {
                    log.error("refreshOperators 异常: ", ex);
                    return null;
                }))).toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join(); // 等待所有任务完成
        List<User> users = futures.stream()
                .flatMap(future -> {
                    try {
                        return future.get().stream();
                    } catch (Exception e) {
                        log.error("获取部门用户列表失败", e);
                        return Stream.empty();
                    }
                }).distinct().toList();
        return users.stream().map(User::getOpenId).distinct().toList();
    }

    private List<String> getDepartments(List<String> departmentIds,FeishuBaseInputParam feishuBaseInfo) {
        Set<String> departmentIdAlls = new HashSet<>(departmentIds);
        for (String departmentId : departmentIds) {
            FeishuContactDepartmentChildrenParam childrenParam = new FeishuContactDepartmentChildrenParam()
                    .setAppInfo(feishuBaseInfo)
                    .setDepartmentId(departmentId);
            List<Department> departmentChildren = FeishuContactQueryUtils.getDepartmentChildren(childrenParam);
            if (CollUtil.isNotEmpty(departmentChildren)) {
                List<String> list = departmentChildren.stream().map(Department::getOpenDepartmentId).distinct().collect(Collectors.toList());
                departmentIdAlls.addAll(list);
                departmentIdAlls.addAll(getDepartments(list,feishuBaseInfo));
            }
        }
        return new ArrayList<>(departmentIdAlls);
    }

    /**
     * 获取上上周日,返回13位时间戳
     */
    private long getLastLastSunday() {
        return LocalDateTime.now()
                .with(TemporalAdjusters.previous(DayOfWeek.SUNDAY))
                .with(TemporalAdjusters.previous(DayOfWeek.SUNDAY))
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0).toInstant(ZoneOffset.of("+08:00")).toEpochMilli();
    }

    /**
     * 获取本周一,返回13位时间戳
     */
    private long getThisMonday() {
        return LocalDateTime.now()
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0).toInstant(ZoneOffset.of("+08:00")).toEpochMilli();
    }
}
