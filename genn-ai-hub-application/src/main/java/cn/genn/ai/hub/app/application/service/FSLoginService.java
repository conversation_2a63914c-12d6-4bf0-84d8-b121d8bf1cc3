package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.assembler.FSAssembler;
import cn.genn.ai.hub.app.application.command.FSRedirectURLCommand;
import cn.genn.ai.hub.app.application.dto.feishu.FSUserAccessTokenResp;
import cn.genn.ai.hub.app.application.dto.feishu.FSUserTokenRequest;
import cn.genn.ai.hub.app.application.dto.feishu.TenantInfo;
import cn.genn.ai.hub.app.infrastructure.config.FSLoginProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CommonCode;
import cn.genn.spring.boot.starter.upm.component.ISsoAuthClient;
import cn.genn.spring.boot.starter.upm.model.LoginUserAccountDTO;
import cn.genn.spring.boot.starter.upm.model.StatusEnum;
import cn.genn.spring.boot.starter.upm.model.UpmFSUserCommand;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.authen.v1.model.GetUserInfoResp;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 飞书用户查询相关
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FSLoginService {

    @Resource
    private FSAssembler fsaAssembler;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private GennAIHubProperties aiHubProperties;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ISsoAuthClient ssoAuthClient;


    public String getLoginUrl(FSRedirectURLCommand command) {
        String state = UUID.randomUUID().toString();
        stringRedisTemplate.opsForValue().set(CacheConstants.CACHE_PRE + CacheConstants.FS_LOGIN_STATE + state, "valid", 2, TimeUnit.MINUTES);
        FSLoginProperties fsLogin = aiHubProperties.getFSLoginConfig(command.getTenantId());
        if (StringUtils.isBlank(command.getRedirectUri())) {
            command.setRedirectUri(fsLogin.getRedirectUri());
        }
        return String.format(
//            "https://open.feishu.cn/open-apis/authen/v1/index?app_id=%s&redirect_uri=%s&state=%s", &scope=admin:app.info:readonly contact:user.base:readonly contact:user.phone:readonly
            "https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=%s&redirect_uri=%s&state=%s",
            fsLogin.getAppId(), URLEncoder.encode(command.getRedirectUri(), StandardCharsets.UTF_8), state
        );
    }

    public LoginUserAccountDTO handleCallback(String code, String state, Long tenantId, String systemCode, String redirectUri) {
        // 1. 校验State防止CSRF  安全优化
        String key = CacheConstants.CACHE_PRE + CacheConstants.FS_LOGIN_STATE + state;
        String storedValue = stringRedisTemplate.opsForValue().get(key);

        if (!storedValue.equals("valid")) {
            throw new BusinessException(CommonCode.SIGN_ERROR, "非法请求：State校验失败");
        }

        stringRedisTemplate.delete(key); // 使用后立即清除
        // 租户应用配置
        FSLoginProperties fsLogin = aiHubProperties.getFSLoginConfig(tenantId);
        // 2. 调用飞书API换取 access_token
        FSUserAccessTokenResp tokenResponse = fetchAccessToken(fsLogin, code, redirectUri);
        // 3. 获取用户信息
        GetUserInfoRespBody userInfo = fetchUserInfo(fsLogin, tokenResponse.getAccessToken());
        if (userInfo == null) {
            throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取用户基本信息】权限");
        }
        String mobile = userInfo.getMobile();
        if (StringUtils.isBlank(mobile)) {
            throw new BusinessException("未查询到飞书用户手机号: 请登录飞书开发者后台【https://open.feishu.cn】查看应用是否开通【获取用户手机号】的权限");
        }
        UpmFSUserCommand command = getFsUserCommand(fsLogin, systemCode, mobile, userInfo.getName(), userInfo.getOpenId());
        return ssoAuthClient.getLoginUserAccount(command);
    }

    private @NotNull UpmFSUserCommand getFsUserCommand(FSLoginProperties fsLogin, String systemCode, String mobile, String name, String openId) {
        String telephone = mobile.substring(mobile.length() - 11);
        UpmFSUserCommand command = new UpmFSUserCommand();
        command.setTenantId(fsLogin.getTenantId()); // 设置租户id
        command.setPassword(fsLogin.getPassword()); // 设置密码
        command.setRoleIdList(fsLogin.getRoleIdList()); // 设置关联角色id列表
        command.setUsername(telephone); // 设置账号
        command.setTelephone(telephone); // 设置联系电话
        command.setNick(name); // 设置昵称
        command.setSystemCode(systemCode); // 设置系统code
        command.setStatus(StatusEnum.ENABLE); // 设置状态
        command.setAppId(fsLogin.getAppId()); // 应用ID
        command.setOpenId(openId); // 飞书用户唯一标识
        return command;
    }

    private FSUserAccessTokenResp fetchAccessToken(FSLoginProperties fsLogin, String code, String redirectUri) {
        String url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=utf-8");
        FSUserTokenRequest body = new FSUserTokenRequest();
        body.setGrant_type("authorization_code");
        body.setCode(code);
        body.setClient_id(fsLogin.getAppId());
        body.setClient_secret(fsLogin.getAppSecret());
        if (StringUtils.isBlank(redirectUri)) {
            redirectUri = fsLogin.getRedirectUri();
        }
        body.setRedirect_uri(redirectUri);
        return restTemplate.postForObject(url, new HttpEntity<>(body, headers), FSUserAccessTokenResp.class);
    }


    private FSUserAccessTokenResp allowedAccessToken(FSLoginProperties fsLogin, String code) {
        String url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=utf-8");
        FSUserTokenRequest body = new FSUserTokenRequest();
        body.setGrant_type("authorization_code");
        body.setCode(code);
        body.setClient_id(fsLogin.getAppId());
        body.setClient_secret(fsLogin.getAppSecret());
        return restTemplate.postForObject(url, new HttpEntity<>(body, headers), FSUserAccessTokenResp.class);
    }

    private GetUserInfoRespBody fetchUserInfo(FSLoginProperties fsLogin, String accessToken) {
        // 构建client
        Client client = Client.newBuilder(fsLogin.getAppId(), fsLogin.getAppSecret()).build();
        // 创建请求对象
        // 发起请求
        GetUserInfoResp resp = null;
        try {
            resp = client.authen().v1().userInfo().get(RequestOptions.newBuilder()
                .userAccessToken(accessToken)
                .build());
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        // 处理服务端错误
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取用户基本信息】权限");
        }
        return resp.getData();
    }

    public List<TenantInfo> getTenantInfo(Long tenantId) {
        return aiHubProperties.getFsLogin().stream()
            .filter(a -> Objects.isNull(tenantId) || a.getTenantId().equals(tenantId))
            .map(ap ->
            TenantInfo.builder()
                .tenantId(ap.getTenantId())
                .name(ap.getName())
                .appId(ap.getAppId())
                .build()
        ).collect(Collectors.toList());
    }

    public LoginUserAccountDTO allowed(String code, Long tenantId, String systemCode) {
        // 租户应用配置
        FSLoginProperties fsLogin = aiHubProperties.getFSLoginConfig(tenantId);
        // 2. 调用飞书API换取 access_token
        FSUserAccessTokenResp tokenResponse = allowedAccessToken(fsLogin, code);
        // 3. 获取用户信息
        GetUserInfoRespBody userInfo = fetchUserInfo(fsLogin, tokenResponse.getAccessToken());
        if (userInfo == null) {
            throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取用户基本信息】权限");
        }
        String mobile = userInfo.getMobile();
        if (StringUtils.isBlank(mobile)) {
            throw new BusinessException("未查询到飞书用户手机号: 请登录飞书开发者后台【https://open.feishu.cn】查看应用是否开通【获取用户手机号】的权限");
        }
        UpmFSUserCommand command = getFsUserCommand(fsLogin, systemCode, mobile, userInfo.getName(), userInfo.getOpenId());
        return ssoAuthClient.getLoginUserAccount(command);
    }
}

