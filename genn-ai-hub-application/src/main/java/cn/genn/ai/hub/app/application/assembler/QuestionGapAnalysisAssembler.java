package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.QuestionGapAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.QuestionGapAnalysisDTO;
import cn.genn.ai.hub.app.application.dto.question.MissingPoints;
import cn.genn.ai.hub.app.application.query.QuestionGapAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionGapAnalysisPO;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.utils.jackson.JsonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuestionGapAnalysisAssembler extends QueryAssembler<QuestionGapAnalysisQuery, QuestionGapAnalysisPO, QuestionGapAnalysisDTO> {

    QuestionGapAnalysisAssembler INSTANCE = Mappers.getMapper(QuestionGapAnalysisAssembler.class);

    @Mapping(source = "po", target = "missingPoints", qualifiedByName = "toMissingPoints")
    QuestionGapAnalysisDTO PO2DTO(QuestionGapAnalysisPO po);

    @Named(value = "toMissingPoints")
    default List<MissingPoints> toMissingPoints(QuestionGapAnalysisPO po) {
        return JsonUtils.parseToList(po.getMissingPoints(), MissingPoints.class);
    }

    QuestionGapAnalysisPO convertPO(QuestionGapAnalysisCommand command);
}

