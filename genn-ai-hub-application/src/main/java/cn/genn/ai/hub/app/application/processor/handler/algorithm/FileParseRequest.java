package cn.genn.ai.hub.app.application.processor.handler.algorithm;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FileParseRequest {
    /**
     * {
     *   "collectionId": 1,
     *   "contentType": "text/plain",
     *   "deepParse": true,
     *   "externalFileId": "/uploads/108500.txt",
     *   "externalFileUrl": "https://genn-app.gennergy.com/uploads/108500.txt",
     *   "fileId": 1,
     *   "fileName": "108500.txt",
     *   "repoId": 1
     * }
     */
    private Long tenantId;
    private Long taskId;
    private Long fileId;
    private String fileName;
    private String externalFileId;
    private String externalFileUrl;
    private String contentType;
    private Boolean deepParse;
    private Boolean smartChunkSummary;
    private Long repoId;
    private Long collectionId;

}
