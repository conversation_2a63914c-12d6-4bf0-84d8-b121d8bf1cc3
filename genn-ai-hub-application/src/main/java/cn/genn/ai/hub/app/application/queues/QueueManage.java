package cn.genn.ai.hub.app.application.queues;

import java.util.List;

public interface QueueManage {

    // 任务进行中
    boolean isProcessing(String processQueueName, String value);

    // 进行队列长度
    Long getProcessQueueSize(String processQueueName);

    // 添加【process】队列 返回当前列表长度
    boolean addTaskToProcess(String processQueueName, String value);

    // 出队【process】队列
    void removeTaskFromProcess(String processQueueName, String value);

    // 添加【task】队列
    void rightPushTaskQueue(String taskQueueName, String value);
    void leftPushTaskQueue(String taskQueueName, String value);
    List<String>  peekCountFromTaskQueue(String taskQueueName, long count);
    // 弹出指定数量的值
    List<String> popCountFromTaskQueue(String taskQueueName, long count);
    String popFromTaskQueue(String taskQueueName);
    Long getTaskQueueSize(String processQueueName);
    // 出队【task】队列
    List<String> popFromTaskQueue(String taskQueueName, Long count);

}

