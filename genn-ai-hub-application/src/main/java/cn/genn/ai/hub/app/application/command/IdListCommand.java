package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.List;


/**
 * 多个 id 提交请求
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IdListCommand {

    @Schema(description = "ids")
    @NotNull(message = "ids 不能为空")
    private List<Long> ids;
}
