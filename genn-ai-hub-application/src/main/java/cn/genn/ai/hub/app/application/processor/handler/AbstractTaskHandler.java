package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.processor.AIHubInvokeService;
import cn.genn.ai.hub.app.application.processor.TaskHandler;
import cn.genn.ai.hub.app.application.queues.QueueManageImpl;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.TaskQueueProperties;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.core.exception.BusinessException;
import cn.genn.lock.base.LockTemplate;
import cn.genn.lock.base.properties.LockInfo;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public abstract class AbstractTaskHandler implements TaskHandler {

    @Resource
    protected AIHubInvokeService invokeService;
    @Resource
    protected LockTemplate lockTemplate;

    @Resource
    protected RepoTaskRepositoryImpl taskRepository;

    @Resource
    protected QueueManageImpl queueManage;
    @Resource
    protected GennAIHubProperties properties;


    @Override
    public String getProcessQueueName(String tenantId) {
        return CacheConstants.CACHE_PRE + ":processing_" + getType().getCode() + "_" + tenantId;
    }

    @Override
    public String getTaskQueueName(String tenantId) {
        return CacheConstants.CACHE_PRE + ":task_" + getType().getCode() + "_" + tenantId;
    }

    @Override
    public Map<String, Long> getQueueState(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            tenantId = "1";
        }
        Map<String, Long> map = Maps.newHashMap();
        String processQueueName = getProcessQueueName(tenantId);
        String taskQueueName = getTaskQueueName(tenantId);
        map.put(processQueueName, queueManage.getProcessQueueSize(processQueueName));
        map.put(taskQueueName, queueManage.getTaskQueueSize(taskQueueName));
        return map;
    }

    @Override
    public void removeTaskFromProcessQueue(KbRepoTaskDTO task) {
        String taskId = Long.toString(task.getId());
        String processQueueName = getProcessQueueName(task.getTenantId().toString());
        queueManage.removeTaskFromProcess(processQueueName, taskId);
    }

    @Override
    public void handleTask(String taskId, String tenantId) {
        log.info("Start handling task {}.", taskId);
        if (StringUtils.isBlank(taskId)) {
            log.warn("Failed to handle task {}.", taskId);
            return;
        }
        String processQueueName = getProcessQueueName(tenantId);
        String taskQueueName = getTaskQueueName(tenantId);
        // 检查任务是否已经在处理队列中
        if (queueManage.isProcessing(processQueueName, taskId)) {
            log.info("Task {} is already processing.", taskId);
            return; // 直接返回
        }

        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock(CacheConstants.TASK_PROCESS_LOCK_KEY + ":" + processQueueName,
                CacheConstants.LOCK_ORDER_STATUS_CHANGE_TIMEOUT,
                CacheConstants.LOCK_ORDER_STATUS_CHANGE_TIMEOUT);
            if (lockInfo == null) {
                log.warn("Failed to acquire lock for task {}.", taskId);
                return;
            }
            // 再次检查任务是否已经在处理队列中
            if (queueManage.isProcessing(processQueueName, taskId)) {
                log.info("Task {} is already processing after acquiring lock.", taskId);
                return;
            }
            // 检查任务队列是否已满
            if (queueManage.getTaskQueueSize(taskQueueName) >= getTaskLimit()) {
                log.warn("Task queue {} is full. Task {} cannot be added.", taskQueueName, taskId);
                throw new BusinessException(MessageCode.QUEUE_MAX);
            }
            // 将任务添加到任务队列
            queueManage.rightPushTaskQueue(taskQueueName, taskId);
            // 检查处理队列是否已满
            Long processQueueSize = queueManage.getProcessQueueSize(processQueueName);
            Long processLimit = getProcessLimit();
            if (processQueueSize >= processLimit) {
                log.info("Process queue {} is full. Task {} cannot be processed.", processQueueName, taskId);
                return;
            }
            // 队列里边取值是从 0 号元素开始
            long processSize = processLimit - processQueueSize - 1;
            // 取出任务队列中第一个任务  队列类型取差值（for）
            List<String> needProcessTaskId = queueManage.peekCountFromTaskQueue(taskQueueName, processSize);
            Set<Long> taskIds = needProcessTaskId
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toSet());
            Map<Long, KbRepoTaskDTO> taskMap = taskRepository.getInfoByIds(taskIds)
                .stream()
                .collect(Collectors.toMap(KbRepoTaskDTO::getId, Function.identity()));
            for (String proTaskId : needProcessTaskId) {
                // 将任务添加到处理队列
                KbRepoTaskDTO task = taskMap.get(Long.parseLong(proTaskId));
                if (task == null) {
                    log.warn("query task from data failed {}.", proTaskId);
                    continue;
                }
                if (TaskStatusEnum.isCompleted(task.getTaskStatus())) {
                    queueManage.popFromTaskQueue(taskQueueName);
                    queueManage.removeTaskFromProcess(processQueueName, proTaskId);
                    continue;
                }
                if (queueManage.addTaskToProcess(processQueueName, proTaskId)) {
                    queueManage.popFromTaskQueue(taskQueueName);
                    // 异步调用算法接口
                    invoke(task);
                    // 更新数据库数据和状态
                    updateTaskStatus(task.getId());
                }
            }

        } catch (Exception e) {
            log.error("TaskHandler  Exception", e);
            throw e;
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    protected void async(Runnable task) {
        Thread.ofVirtual()
            .name("thread_async_" + getType().getCode())
            .start(task);
    }

    protected Long getProcessLimit() {
        TaskQueueProperties queueLimit = properties.getKbQueueLimit();
        return queueLimit.getProcessLimit(getType());
    }

    protected Long getTaskLimit() {
        TaskQueueProperties queueLimit = properties.getKbQueueLimit();
        return queueLimit.getTaskLimit(getType());
    }

    protected void updateTaskStatus(Long taskId) {
        // 更新数据库数据和状态的实现
        taskRepository.updateTaskStatus(taskId, TaskStatusEnum.PROCESSING);
    }


}
