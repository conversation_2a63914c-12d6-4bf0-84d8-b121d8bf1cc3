package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.hub.app.infrastructure.config.voice.SpeechProperties;
import cn.genn.ai.hub.app.infrastructure.config.voice.VoiceProperties;
import cn.genn.core.model.KVStruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "genn.ai.hub.app")
@Data
public class GennAIHubProperties {

    @NestedConfigurationProperty
    private TaskQueueProperties kbQueueLimit = new TaskQueueProperties();

    @NestedConfigurationProperty
    private InvokeUrlProperties kbInvokeUrl = new InvokeUrlProperties();

    /**
     * 租户对应的存储配置参数
     * key: 租户ID
     * value: 存储类型(obs/tos)对应的配置参数
     */
    @NestedConfigurationProperty
    private Map<Long, KVStruct<String, GennAIFileStorageProperties>> tenantStorageConfigs = new HashMap<>();

    /**
     * 租户对应的公共存储配置参数
     * key: 租户ID
     * value: 存储类型(obs/tos)对应的配置参数
     */
    @NestedConfigurationProperty
    private Map<Long, KVStruct<String, GennAIFileStorageProperties>> tenantPublicStorageConfigs = new HashMap<>();

    /**
     * tos公有桶
     */
    @NestedConfigurationProperty
    private GennAIFileStorageProperties tosPublic = new GennAIFileStorageProperties();

    /**
     * 火山云对象存储 公共桶
     */
    @NestedConfigurationProperty
    private GennAIFileStorageProperties tos = new GennAIFileStorageProperties();

    /**
     * 华为云对象存储 公共桶
     */
    @NestedConfigurationProperty
    private GennAIFileStorageProperties obs = new GennAIFileStorageProperties();

    /**
     * 全局统一租户的情况下默认使用的对象存储平台 给无登录态上传接口使用等 公共桶
     */
    @NestedConfigurationProperty
    private String defaultStorage = "tos";

    @NestedConfigurationProperty
    private MilvusProperties milvus = new MilvusProperties();

    @NestedConfigurationProperty
    private AgentProperties agent = new AgentProperties();

    @NestedConfigurationProperty
    private AIGatewayProperties aiGateway = new AIGatewayProperties();

    /**
     * mcp配置
     */
    @NestedConfigurationProperty
    private McpProperties mcp = new McpProperties();

    /**
     * 首页配置
     */
    @NestedConfigurationProperty
    private HomeProperties home = new HomeProperties();

    /**
     * RTC配置
     */
    @NestedConfigurationProperty
    private RTCProperties rtc = new RTCProperties();

    /**
     * 实时音视频-智能体
     */
    @NestedConfigurationProperty
    private VoiceProperties voice = new VoiceProperties();
    /**
     * 实时音视频-智能体
     */
    @NestedConfigurationProperty
    private SpeechProperties speech = new SpeechProperties();

    @NestedConfigurationProperty
    private DailyAnalysisProperties dailyAnalysis = new DailyAnalysisProperties();

    /**
     * 默认的API Key,用于调用代理接口
     */
    private String defaultApiKey = "sk-bkL8MnqRQwubX2NSMWpeUHcfvW88QdzmmbvE";

    /**
     * 默认的模型Key,用于内部一些AI调用
     */
    private String defaultModelKey = "Doubao-1.5-pro-32k";


    @NestedConfigurationProperty
    private List<FSLoginProperties> fsLogin = new ArrayList<>();

    public FSLoginProperties getFSLoginConfig(Long tenantId) {
        if (tenantId == null) {
            tenantId = 1L;
        }
        for (FSLoginProperties config : this.fsLogin) {
            if (config.getTenantId().equals(tenantId)) {
                return config;
            }
        }
        return new FSLoginProperties();
    }
}
