package cn.genn.ai.hub.app.infrastructure.utils;

import cn.genn.ai.hub.app.application.dto.mcp.ApiMcpServerStatusDTO;
import cn.genn.ai.hub.app.application.dto.mcp.McpDTO;
import cn.genn.ai.hub.app.application.dto.mcp.McpGroupDTO;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.VirtualThreadUtils;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import java.util.List;

/**
 * MCP Gateway 服务调用工具类。
 * 封装了对 MCP Gateway 管理接口的 HTTP 调用。
 *
 * <AUTHOR> (原始作者)
 * <AUTHOR> AI (根据接口完成和增强)
 */
@Slf4j
public class McpGatewayInvokeUtils {

    // MCP Gateway Admin API 的基础路径, RestClient Bean 中应配置根URL
    private static final String BASE_PATH = "/admin/mcp";

    // --- MCP Server Management Endpoints ---
    private static final String ADD_MCP_SERVER_PATH = BASE_PATH + "/server";
    private static final String UPDATE_MCP_SERVER_PATH = BASE_PATH + "/server/{serverId}";
    private static final String DELETE_MCP_SERVER_PATH = BASE_PATH + "/server/{serverId}";
    private static final String RESTART_MCP_SERVER_PATH = BASE_PATH + "/server/{serverId}/restart";
    private static final String GET_MCP_SERVER_STATUS_PATH = BASE_PATH + "/server/{serverId}/status";
    private static final String GET_ALL_MCP_SERVERS_STATUS_PATH = BASE_PATH + "/servers/status";
    private static final String GET_INSTANCES_PATH = BASE_PATH + "/instances/list";
    private static final String DELETE_USER_DEFINED_SERVER_PATH = BASE_PATH + "/user-server/{workflowId}";

    // --- MCP Group Management Endpoints ---
    private static final String ADD_MCP_GROUP_PATH = BASE_PATH + "/group";
    private static final String UPDATE_MCP_GROUP_PATH = BASE_PATH + "/group/{groupKey}";
    private static final String DELETE_MCP_GROUP_PATH = BASE_PATH + "/group/{groupKey}";

    /**
     * 获取配置好的 RestClient 实例。
     * 该 RestClient Bean 应配置 MCP Gateway 的基础 URL。
     *
     * @return RestClient 实例
     */
    private static RestClient getLbRestClient() {
        return SpringUtil.getBean("mcpGatewayRestClient", RestClient.class);
    }

    // --- MCP Server Management Methods ---
    /**
     * 添加 MCP 服务。
     *
     * @param mcpDTO 包含 MCP 服务信息的 DTO 对象 (cn.genn.ai.hub.app.application.dto.mcp.McpDTO)。
     */
    public static void addMcpServer(McpDTO mcpDTO) {
        VirtualThreadUtils.runAsync(() -> {
            getLbRestClient().post()
                .uri(ADD_MCP_SERVER_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .body(mcpDTO)
                .retrieve()
                .body(String.class);
        });

    }

    /**
     * 更新指定的 MCP 服务器信息。
     *
     * @param serverId 要更新的服务器的唯一标识符。
     * @param mcpDTO   包含要更新的服务器信息的数据传输对象 (cn.genn.ai.hub.app.application.dto.mcp.McpDTO)。
     */
    public static void updateMcpServer(Long serverId, McpDTO mcpDTO) {
        VirtualThreadUtils.runAsync(() -> {
            getLbRestClient().put()
                .uri(UPDATE_MCP_SERVER_PATH, serverId)
                .contentType(MediaType.APPLICATION_JSON)
                .body(mcpDTO)
                .retrieve()
                .body(String.class);
        });
    }

    /**
     * 删除指定ID的MCP服务器。
     *
     * @param serverId 要删除的服务器的ID。
     */
    public static void deleteMcpServer(Long serverId) {
        VirtualThreadUtils.runAsync(() -> {
            getLbRestClient().delete()
                .uri(DELETE_MCP_SERVER_PATH, serverId)
                .retrieve()
                .body(String.class);
        });
    }

    /**
     * 重启指定ID的MCP服务器。
     *
     * @param serverId 要重启的服务器的ID。
     */
    public static void restartMcpServer(Long serverId) {
        try {
            unwrapRes(getLbRestClient().post()
                .uri(RESTART_MCP_SERVER_PATH, serverId)
                .contentType(MediaType.APPLICATION_JSON)
                .retrieve()
                .toEntity(new ParameterizedTypeReference<ResponseResult<Void>>() {})
                .getBody());
        } catch (Exception e) {
            log.error("重启 MCP 服务器失败: {}", e.getMessage());
            throw new BaseException(MessageCode.MCP_SERVER_RESTART_FAILED);
        }
    }

    /**
     * 获取当前配置环境中所有可用的实例名称列表。
     */
    public static List<String> getInstances() {
        return unwrapRes(getLbRestClient().get()
            .uri(GET_INSTANCES_PATH)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<List<String>>>() {})
            .getBody());
    }

    /**
     * 获取指定 MCP 服务器的状态信息。
     *
     * @param serverId MCP 服务器的唯一标识符。
     * @return ResponseResult 包含 ApiMcpServerStatusDTO；如果操作失败或未找到，ResponseResult内部将携带错误信息。
     */
    public static List<ApiMcpServerStatusDTO> getMcpServerStatus(Long serverId) {
        return unwrapRes(getLbRestClient().get()
            .uri(GET_MCP_SERVER_STATUS_PATH, serverId)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<List<ApiMcpServerStatusDTO>>>() {})
            .getBody());
    }

    /**
     * 获取所有 MCP 服务的状态列表。
     *
     * @return ResponseResult 包含 ApiMcpServerStatusDTO 列表；如果操作失败，ResponseResult内部将携带错误信息。
     */
    public static List<ApiMcpServerStatusDTO> getAllMcpServersStatus() {
        return unwrapRes(getLbRestClient().get()
            .uri(GET_ALL_MCP_SERVERS_STATUS_PATH)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<List<ApiMcpServerStatusDTO>>>() {})
            .getBody());
    }

    // --- MCP Group Management Methods ---

    /**
     * 添加一个 MCP 组。
     *
     * @param mcpGroupDTO 包含 MCP 组信息的 DTO 对象。
     */
    public static void addMcpGroup(McpGroupDTO mcpGroupDTO) {
        unwrapRes(getLbRestClient().post()
            .uri(ADD_MCP_GROUP_PATH)
            .contentType(MediaType.APPLICATION_JSON)
            .body(mcpGroupDTO)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<Void>>() {})
            .getBody());
    }

    /**
     * 更新 MCP 组信息。
     *
     * @param groupKey    要更新的组的唯一标识符。
     * @param mcpGroupDTO 包含更新组信息的 DTO。
     */
    public static void updateMcpGroup(String groupKey, McpGroupDTO mcpGroupDTO) {
        unwrapRes(getLbRestClient().put()
            .uri(UPDATE_MCP_GROUP_PATH, groupKey)
            .contentType(MediaType.APPLICATION_JSON)
            .body(mcpGroupDTO)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<McpGroupDTO>>() {})
            .getBody());
    }

    /**
     * 删除指定的 MCP 组。
     *
     * @param groupKey 要删除的 MCP 组的唯一标识符。
     */
    public static void deleteMcpGroup(String groupKey) {
        unwrapRes(getLbRestClient().delete()
            .uri(DELETE_MCP_GROUP_PATH, groupKey)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<Void>>() {})
            .getBody());
    }

    /**
     * 删除指定workflowId的用户定义服务。
     * 该方法用于删除通过工作流创建的用户自定义服务。
     *
     * @param workflowId 工作流ID，用于标识要删除的用户服务
     */
    public static void deleteUserDefinedServer(String workflowId) {
        if (workflowId == null || workflowId.trim().isEmpty()) {
            throw new BaseException(CommonCode.METHOD_ARGUMENT_NOT_VALID.buildCode(), "workflowId不能为空");
        }
        unwrapRes(getLbRestClient().delete()
            .uri(DELETE_USER_DEFINED_SERVER_PATH, workflowId)
            .retrieve()
            .toEntity(new ParameterizedTypeReference<ResponseResult<Void>>() {})
            .getBody());
    }

    private static <T> T unwrapRes(ResponseResult<T> body) {
        if (body == null) {
            throw new BaseException(CommonCode.CONNECT_ERROR);
        }
        if (!body.isSuccess()) {
            throw new BaseException(body.getCode(), body.getMsg());
        }
        return body.getData();
    }
}
