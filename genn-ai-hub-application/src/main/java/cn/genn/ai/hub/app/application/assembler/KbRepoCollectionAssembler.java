package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.FSSheetSyncCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoAfreshTrainCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FSSyncParams;
import cn.genn.ai.hub.app.application.dto.request.*;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoCollectionAssembler extends QueryAssembler<KbRepoCollectionQuery, KbRepoCollectionPO, KbRepoCollectionDTO> {

    KbRepoCollectionAssembler INSTANCE = Mappers.getMapper(KbRepoCollectionAssembler.class);

    KbRepoCollectionPO KbRepoCollectionTextOfFileCreateCommandToPO(KbRepoCollectionTextOfFileCreateCommand command);

    KbRepoCollectionPO KbRepoCollectionTextOfUrlCreateCommandToPO(KbRepoCollectionTextOfUrlCreateCommand command);

    KbRepoCollectionPO KbRepoCollectionTextOfTextCreateCommandToPO(KbRepoCollectionTextOfTextCreateCommand command);

    KbRepoCollectionPO convertAfreshTrain(KbRepoAfreshTrainCommand command);

    KbRepoCollectionPO DTO2PO(KbRepoCollectionDTO update);

    KbRepoCollectionPO sheetCommandConvertPO(KbRepoCollectionSheetCreateCommand command);

    @Mapping(target = "name", source = "title")
    KbRepoCollectionPO fsSheetConvertPO(FSSheetSyncCommand command);

    FSSyncParams syncParams(FSSheetSyncCommand command);
}

