package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.CodeRepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.CodeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 代码库保存命令
 * @date 2025-05-16
 */
@Data
public class CodeRepoSaveCommand {

    @Schema(description = "代码库id")
    private Long id;

    @Schema(description = "团队空间ID")
    private Long teamId;

    @Schema(description = "代码库名称")
    private String name;

    @Schema(description = "代码库类型 1：javascript 2：python")
    private CodeTypeEnum codeType;

    @Schema(description = "代码库类型 1：系统代码库 2：用户代码库")
    private CodeRepoTypeEnum codeRepoType;

    @Schema(description = "代码库描述")
    private String description;

    @Schema(description = "代码库内容")
    private String content;
}
