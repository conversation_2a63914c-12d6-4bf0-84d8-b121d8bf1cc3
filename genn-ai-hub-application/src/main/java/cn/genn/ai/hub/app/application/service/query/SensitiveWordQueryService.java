package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.SensitiveWordAssembler;
import cn.genn.ai.hub.app.application.dto.SensitiveWordDTO;
import cn.genn.ai.hub.app.application.enums.SensitiveWordType;
import cn.genn.ai.hub.app.application.query.SensitiveWordQuery;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.SensitiveWordRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.SensitiveWordPO;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 敏感词查询服务
 * @date 2025-05-16
 */
@Service
@RequiredArgsConstructor
public class SensitiveWordQueryService {

    private final SensitiveWordRepositoryImpl sensitiveWordRepository;

    private final SensitiveWordAssembler sensitiveWordAssembler;

    @IgnoreTenant
    public PageResultDTO<SensitiveWordDTO> page(SensitiveWordQuery query) {
        Page<SensitiveWordPO> page = new Page<>(query.getPageNo(), query.getPageSize());
        LambdaQueryWrapper<SensitiveWordPO> wrapper = new LambdaQueryWrapper<>();

        // 通用查询条件：敏感词内容 (模糊匹配)
        wrapper.like(StrUtil.isNotBlank(query.getWords()), SensitiveWordPO::getWords, query.getWords());

        // 根据 wordsType 是否传入，构造不同的查询逻辑
        if (query.getWordsType() == null) {
            // 情况1: wordsType 未传入，查询系统敏感词 或 用户敏感词
            wrapper.and(mainOr -> mainOr
                // 条件A: 系统敏感词 (wordsType = SYSTEM)，不进行 teamId 或 createUserId 过滤
                .eq(SensitiveWordPO::getWordsType, SensitiveWordType.SYSTEM) // 假设 SensitiveWordPO 有 getWordsType, SensitiveWordType.SYSTEM 存在
                // 条件B: 用户敏感词 (wordsType = USER)，并应用 teamId 或 createUserId 过滤
                .or(userSensitiveWordConditions -> {
                    userSensitiveWordConditions.eq(SensitiveWordPO::getWordsType, SensitiveWordType.USER); // 假设 SensitiveWordType.USER 存在
                    userSensitiveWordConditions.eq(SensitiveWordPO::getTenantId, CurrentUserHolder.getTenantId());
                    if (query.getTeamId() != null) {
                        // 团队空间的敏感词
                        userSensitiveWordConditions.eq(SensitiveWordPO::getTeamId, query.getTeamId());
                    } else {
                        // 个人空间的敏感词 (teamId 为 null 且创建者为当前用户)
                        userSensitiveWordConditions.isNull(SensitiveWordPO::getTeamId);
                        userSensitiveWordConditions.eq(SensitiveWordPO::getCreateUserId, CurrentUserHolder.getUserId());
                    }
                })
            );
        } else {
            // 情况2: wordsType 已传入，按指定类型查询
            wrapper.eq(SensitiveWordPO::getWordsType, query.getWordsType());

            // 如果查询的是用户敏感词 (USER)，则应用 teamId 或 createUserId 过滤
            // 注意：这里的 SensitiveWordType.USER 需要与您的枚举定义匹配
            if (SensitiveWordType.USER.equals(query.getWordsType())) {
                wrapper.eq(SensitiveWordPO::getTenantId, CurrentUserHolder.getTenantId());
                if (query.getTeamId() != null) {
                    // 团队空间的敏感词
                    wrapper.eq(SensitiveWordPO::getTeamId, query.getTeamId());
                } else {
                    // 个人空间的敏感词
                    wrapper.isNull(SensitiveWordPO::getTeamId);
                    wrapper.eq(SensitiveWordPO::getCreateUserId, CurrentUserHolder.getUserId());
                }
            }
            // 如果查询的是系统敏感词 (SYSTEM)，则不添加额外的 teamId 或 createUserId 过滤
        }

        wrapper.orderByDesc(SensitiveWordPO::getCreateTime); // 统一按创建时间降序

        Page<SensitiveWordPO> sensitiveWordPOPage = sensitiveWordRepository.getBaseMapper().selectPage(page, wrapper);
        List<SensitiveWordDTO> sensitiveWordDTOS = sensitiveWordAssembler.toDTO(sensitiveWordPOPage.getRecords());
        return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), sensitiveWordPOPage.getTotal(), sensitiveWordDTOS);
    }
}
