package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.feishu.FSFileType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 插件查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书文档子列表查询参数")
public class FSSpaceQuery {

    @Schema(description = "知识库ID", required = true)
    @NotEmpty(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "应用ID", required = true)
    @NotEmpty(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "配置的token(wiki)", required = true)
    private String folderToken;

    @Schema(description = "节点ID-当前节点spaceId，获取子列表必传")
    private String spaceId;

    @Schema(description = "节点token-当前节点token，获取子列表必传")
    private String nodeToken;

    @Schema(description = "objType-指定文件类型", required = true)
    private FSFileType objType;

    @Schema(description = "分页大小")
    private Integer pageSize = 50;

    @Schema(description = "分页pageToken-当前列表所在层级的pageToken")
    private String pageToken;

}
