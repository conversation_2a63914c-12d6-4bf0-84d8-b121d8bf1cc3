package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.IdListCommand;
import cn.genn.ai.hub.app.application.command.KbRepoQaPairEditCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoQaPairMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbQATeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver{

    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;
    private final KbRepoQaPairMapper kbRepoQaPairMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long kbId = switch (uniqueId) {
            case "editQA" -> {
                Long qaId = ((KbRepoQaPairEditCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoQaPairMapper.selectById(qaId).getRepoId();
            }
            case "deleteQA" -> {
                Long qaId = ((IdCommand) interceptor.getParamsMap().get("command")).getId();
                yield kbRepoQaPairMapper.selectById(qaId).getRepoId();
            }
            case "batchDeleteQA" -> {
                Long qaId = ((IdListCommand) interceptor.getParamsMap().get("command")).getIds().getFirst();
                yield kbRepoQaPairMapper.selectById(qaId).getRepoId();
            }
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return kbRepoBaseInfoMapper.selectById(kbId).getTeamId();
    }
}
