package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 问答对excel导入
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoQaPairUploadCommand implements Serializable {


    private static final long serialVersionUID = 1L;


    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    @NotNull(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "文件id")
    @NotNull(message = "文件id不能为空")
    private Long fileId;


}

