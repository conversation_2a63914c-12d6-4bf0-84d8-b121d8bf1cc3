package cn.genn.ai.hub.app.application.processor.handler.msg;

import cn.genn.ai.hub.app.application.enums.feishu.FSMsgContentTypeEnum;
import cn.genn.ai.hub.app.infrastructure.config.FSLoginProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.plugin.impl.feishu.utils.FeishuClientUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.lark.oapi.Client;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendFSMsgHandler {

    private final FeishuClientUtils feishuClient;
    private final GennAIHubProperties aiHubProperties;


    public Object sendMsg(String msg, FSMsgContentTypeEnum contentType) {
        FSLoginProperties fsLogin = aiHubProperties.getFSLoginConfig(CurrentUserHolder.getTenantId());
        Client client = feishuClient.getClient(fsLogin.getAppId(), fsLogin.getAppSecret());
        // 创建请求对象
        CreateMessageReq req = CreateMessageReq.newBuilder()
            .receiveIdType("open_id")
            .createMessageReqBody(CreateMessageReqBody.newBuilder()
                .receiveId(CurrentUserHolder.getFSOpenId())
                .msgType(contentType.getCode())
                .content(msg)
                .build())
            .build();
        try {
            CreateMessageResp createMessageResp = client.im().v1().message().create(req);
            return createMessageResp.getData();
        } catch (Exception e) {
            throw new BusinessException("发送消息异常");
        }

    }

}
