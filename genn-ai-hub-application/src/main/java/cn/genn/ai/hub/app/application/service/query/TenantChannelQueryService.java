package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.TenantChannelAssembler;
import cn.genn.ai.hub.app.application.dto.channel.TenantChannelDTO;
import cn.genn.ai.hub.app.application.query.ChannelQuery;
import cn.genn.ai.hub.app.domain.channel.config.ChannelConfigHandler;
import cn.genn.ai.hub.app.domain.channel.config.ChannelConfigHandlerFactory;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ChannelBaseRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TenantChannelRefRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ChannelBasePO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TenantChannelRefPO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 租户渠道查询服务
 * @date 2025-04-27
 */
@Service
@RequiredArgsConstructor
public class TenantChannelQueryService {

    private final TenantChannelRefRepositoryImpl tenantChannelRefRepository;

    private final ChannelBaseRepositoryImpl channelBaseRepository;

    private final TenantChannelAssembler tenantChannelAssembler;

    private final ChannelConfigHandlerFactory channelConfigHandlerFactory;

    public List<TenantChannelDTO> page(ChannelQuery query) {
        Page<TenantChannelRefPO> page = new Page<>(query.getPageNo(), query.getPageSize());
        LambdaQueryWrapper<TenantChannelRefPO> wrapper = new LambdaQueryWrapper<TenantChannelRefPO>()
            .eq(query.getTeamId() != null, TenantChannelRefPO::getTeamId, query.getTeamId())
            .isNull(query.getTeamId() == null, TenantChannelRefPO::getTeamId)
            .eq(query.getTeamId() == null, TenantChannelRefPO::getCreateUserId, CurrentUserHolder.getUserId())
            .eq(query.getChannelType() != null, TenantChannelRefPO::getChannelType, query.getChannelType())
            .orderByDesc(TenantChannelRefPO::getCreateTime);
        Page<TenantChannelRefPO> resultPage = tenantChannelRefRepository.getBaseMapper().selectPage(page, wrapper);
        return resultPage.getRecords().stream().map(po -> getById(po.getId())).toList();
    }

    public TenantChannelDTO getById(Long id) {
        TenantChannelRefPO tenantChannelRefPO = tenantChannelRefRepository.getById(id);
        if (tenantChannelRefPO == null) {
            return null;
        }
        ChannelBasePO channelBasePO = channelBaseRepository.getById(tenantChannelRefPO.getChannelId());
        TenantChannelDTO dto = tenantChannelAssembler.convert2DTO(tenantChannelRefPO);
        dto.setChannelUniqueIdentifier(channelBasePO.getUniqueIdentifier());
        ChannelConfigHandler handler = channelConfigHandlerFactory.getHandler(tenantChannelRefPO.getChannelType());
        if (handler != null) {
            handler.handleQuery(dto, channelBasePO.getChannelConfig());
        }
        return dto;
    }
}
