package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.TagsAssembler;
import cn.genn.ai.hub.app.application.command.TagsCreateCommand;
import cn.genn.ai.hub.app.application.command.TagsUpdateCommand;
import cn.genn.ai.hub.app.application.dto.TagsDTO;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.service.query.TagsQueryService;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TagsRepositoryImpl;
import cn.genn.core.model.ddd.IdCommand;
import cn.hutool.core.map.MapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 标签操作服务,负责增删改的实现.事务在这一层控制
 * 逻辑比较简单,就不用领域实现了
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TagsActionService {

    private final TagsRepositoryImpl tagsRepository;
    private final TagsAssembler tagsAssembler;
    private final TagsQueryService tagsQueryService;

    /**
     * 创建标签
     *
     * @param command 创建标签命令
     * @return 标签ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(TagsCreateCommand command) {
        return tagsRepository.createTag(tagsAssembler.command2PO(command));
    }

    /**
     * 删除标签
     *
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(IdCommand command) {
        return tagsRepository.deleteTag(command.getId());
    }

    /**
     * 删除资源关联的所有标签
     * @param resourceKey 资源key
     * @param tagType 标签类型
     * @param teamId 团队ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTagRefByResource(String resourceKey, TagTypeEnum tagType, Long teamId) {
        Map<String, List<TagsDTO>> resourceTagMap = tagsQueryService.getTagByResource(List.of(resourceKey), tagType, teamId);
        if (MapUtil.isNotEmpty(resourceTagMap)) {
            List<Long> oldTagIds = resourceTagMap.get(resourceKey).stream().map(TagsDTO::getId).toList();
            if (!oldTagIds.isEmpty()) {
                tagsRepository.deleteTagRefByResource(resourceKey, oldTagIds);
            }
        }
    }

    /**
     * 更新资源关联的标签
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTagRefByResource(String resourceKey, TagTypeEnum tagType, Long teamId, List<Long> tagIds) {
        deleteTagRefByResource(resourceKey, tagType, teamId);
        tagsRepository.createTagRef(resourceKey, tagIds);
    }

    /**
     * 更新标签
     *
     * @param command 更新标签命令
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(TagsUpdateCommand command) {
        return tagsRepository.updateTag(tagsAssembler.updateCommand2PO(command));
    }
}
