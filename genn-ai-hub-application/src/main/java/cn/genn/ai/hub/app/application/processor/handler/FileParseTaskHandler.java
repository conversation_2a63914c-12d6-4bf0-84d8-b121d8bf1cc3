package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskOfFileParseBody;
import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.handler.algorithm.FileParseRequest;
import cn.genn.ai.hub.app.application.processor.handler.algorithm.FileParseResponse;
import cn.genn.ai.hub.app.application.service.action.KbRepoCollectionActionService;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import com.google.gson.reflect.TypeToken;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.FILE_PARSE;

@Slf4j
@Component
public class FileParseTaskHandler extends AbstractTaskHandler {

    @Resource
    private KbRepoCollectionActionService collectionActionService;

    @Override
    public TaskTypeEnum getType() {
        return FILE_PARSE;
    }

    @Override
    public void invoke(KbRepoTaskDTO task) {
        async(() -> {
            // 异步调用算法接口的实现
            KbRepoTaskOfFileParseBody fileParseBody = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfFileParseBody.class);
            // 构建请求实体
            FileParseRequest request = new FileParseRequest();
            request.setTenantId(task.getTenantId());
            request.setTaskId(task.getId());
            request.setFileId(fileParseBody.getFileId());
            request.setFileName(fileParseBody.getFileName());
            request.setExternalFileId(fileParseBody.getExternalFileId());
            request.setExternalFileUrl(fileParseBody.getExternalFileUrl());
            request.setContentType(fileParseBody.getContentType());
            request.setRepoId(fileParseBody.getRepoId());
            request.setCollectionId(fileParseBody.getCollectionId());
            request.setDeepParse(fileParseBody.isDeepParse());
//                String url = "http://your-api-server/api/algorithm/fileParse";
            try {
                String url = properties.getKbInvokeUrl().getRequestUrl(getType());
                TypeToken<FileParseResponse> typeToken = new TypeToken<FileParseResponse>() {
                };
                ResponseResult<FileParseResponse> result = invokeService.fetchPost(url, JsonUtils.toJson(request), typeToken);
                log.info("FileParseTaskHandler invoke result:{}", JsonUtils.toJson(result));
            } catch (Exception e) {
                log.error("FileParseTaskHandler invoke error:{}", e);
                throw new RuntimeException(e);
            }
            // 更新数据集状态为解析中
            collectionActionService.updateCollectionStatus(fileParseBody.getCollectionId(), CollectionStatusEnum.PARSING);
        });
    }

}
