package cn.genn.ai.hub.app.infrastructure.utils.chunk;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class TextSplitter {

    public static final String CUSTOM_SPLIT_SIGN = "-----CUSTOM_SPLIT_SIGN-----";
    public static final String splitMarker = "SPLIT_HERE_SPLIT_HERE";
    public static final String codeBlockMarker = "CODE_BLOCK_LINE_MARKER";

    public static class SplitProps {
        public String text;
        public int chunkLen;
        public double overlapRatio;
        public List<String> customReg;

        public SplitProps(String text, int chunkLen, double overlapRatio, List<String> customReg) {
            this.text = text;
            this.chunkLen = chunkLen;
            this.overlapRatio = overlapRatio;
            this.customReg = customReg;
        }
    }

    public static class SplitResponse {
        public List<String> chunks;
        public int chars;

        public SplitResponse(List<String> chunks, int chars) {
            this.chunks = chunks;
            this.chars = chars;
        }
    }

    private static boolean strIsMdTable(String str) {
        if (!str.contains("|")) {
            return false;
        }

        String[] lines = str.split("\n");

        if (lines.length < 2) {
            return false;
        }

        String headerLine = lines[0].trim();
        if (!headerLine.startsWith("|") || !headerLine.endsWith("|")) {
            return false;
        }

        String separatorLine = lines[1].trim();
        Pattern separatorRegex = Pattern.compile("^\\|([\\s:]*-+[\\s:]*)+\\|$");
        Matcher matcher = separatorRegex.matcher(separatorLine);
        if (!matcher.matches()) {
            return false;
        }

        for (int i = 2; i < lines.length; i++) {
            String dataLine = lines[i].trim();
            if (!dataLine.isEmpty() && (!dataLine.startsWith("|") || !dataLine.endsWith("|"))) {
                return false;
            }
        }

        return true;
    }

    private static SplitResponse markdownTableSplit(SplitProps props) {
        String text = props.text;
        int chunkLen = props.chunkLen;
        String[] splitText2Lines = text.split("\n");
        String header = splitText2Lines[0];
        int headerSize = header.split("\\|").length - 2;

        String mdSplitString = "| " + String.join(" | ", new String[headerSize > 0 ? headerSize : 1])
            .replace("\0", "---") + " |";

        List<String> chunks = new ArrayList<>();
        StringBuilder chunk = new StringBuilder(header + "\n" + mdSplitString + "\n");

        for (int i = 2; i < splitText2Lines.length; i++) {
            if (chunk.length() + splitText2Lines[i].length() > chunkLen * 1.2) {
                chunks.add(chunk.toString());
                chunk = new StringBuilder(header + "\n" + mdSplitString + "\n");
            }
            chunk.append(splitText2Lines[i]).append("\n");
        }

        if (chunk.length() > 0) {
            chunks.add(chunk.toString());
        }

        int chars = chunks.stream().mapToInt(String::length).sum();

        return new SplitResponse(chunks, chars);
    }
    private static SplitResponse commonSplit(SplitProps props) {
        String text = props.text;
        int chunkLen = props.chunkLen;
        double overlapRatio = props.overlapRatio;
        List<String> customReg = props.customReg;

        int overlapLen = (int) Math.round(chunkLen * overlapRatio);

        // 使用 Matcher 和 Pattern 来替换代码块中的换行符
        Pattern codeBlockPattern = Pattern.compile("(|~~~[\\s\\S]*?~~~)");
        Matcher codeBlockMatcher = codeBlockPattern.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (codeBlockMatcher.find()) {
            String codeBlock = codeBlockMatcher.group();
            String replacedCodeBlock = codeBlock.replaceAll("\n", codeBlockMarker);
            codeBlockMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacedCodeBlock));
        }
        codeBlockMatcher.appendTail(sb);
        text = sb.toString();

        text = text.replaceAll("(\\r?\\n|\\r){3,}", "\n\n\n");

        int markdownIndex = 4;
        int forbidOverlapIndex = 8;
        List<StepReg> stepReges = new ArrayList<>();
        for (String regText : customReg) {
            stepReges.add(new StepReg(Pattern.compile("(" + replaceRegChars(regText) + ")", Pattern.MULTILINE), chunkLen * 1.4));
        }
        stepReges.add(new StepReg(Pattern.compile("^(#\\s[^\n]+\\n)", Pattern.MULTILINE), chunkLen * 1.2));
        stepReges.add(new StepReg(Pattern.compile("^(##\\s[^\n]+\\n)", Pattern.MULTILINE), chunkLen * 1.4));
        stepReges.add(new StepReg(Pattern.compile("^(###\\s[^\n]+\\n)", Pattern.MULTILINE), chunkLen * 1.6));
        stepReges.add(new StepReg(Pattern.compile("^(####\\s[^\n]+\\n)", Pattern.MULTILINE), chunkLen * 1.8));
        stepReges.add(new StepReg(Pattern.compile("^(#####\\s[^\n]+\\n)", Pattern.MULTILINE), chunkLen * 1.8));
        stepReges.add(new StepReg(Pattern.compile("([\\n]([`~]))", Pattern.MULTILINE), chunkLen * 4));
        stepReges.add(new StepReg(Pattern.compile("([\\n](?=\\s*[0-9]+\\.))", Pattern.MULTILINE), chunkLen * 2));
        stepReges.add(new StepReg(Pattern.compile("([\\n]{2,})", Pattern.MULTILINE), chunkLen * 1.6));
        stepReges.add(new StepReg(Pattern.compile("([\\n])", Pattern.MULTILINE), chunkLen * 1.2));
        stepReges.add(new StepReg(Pattern.compile("([。]|([a-zA-Z])\\.)\\s", Pattern.MULTILINE), chunkLen * 1.2));
        stepReges.add(new StepReg(Pattern.compile("([！]|!\\s)", Pattern.MULTILINE), chunkLen * 1.2));
        stepReges.add(new StepReg(Pattern.compile("([？]|\\?\\s)", Pattern.MULTILINE), chunkLen * 1.4));
        stepReges.add(new StepReg(Pattern.compile("([；]|;\\s)", Pattern.MULTILINE), chunkLen * 1.6));
        stepReges.add(new StepReg(Pattern.compile("([，]|,\\s)", Pattern.MULTILINE), chunkLen * 2));

        List<String> chunks = new ArrayList<>();
        String lastText = "";
        String parentTitle = "";

        chunks.addAll(splitTextRecursively(text, 0, lastText, parentTitle, stepReges, overlapLen, chunkLen));

        for (int i = 0; i < chunks.size(); i++) {
            chunks.set(i, chunks.get(i).replaceAll(codeBlockMarker, "\n").trim());
        }

        int chars = chunks.stream().mapToInt(String::length).sum();

        return new SplitResponse(chunks, chars);
    }

    private static List<String> splitTextRecursively(String text, int step, String lastText, String parentTitle, List<StepReg> stepReges, int overlapLen, int chunkLen) {

        List<String> chunks = new ArrayList<>();
        if (step >= stepReges.size()) {
            if (text.length() < chunkLen * 3) {
                chunks.add(text);
                return chunks;
            }

            for (int i = 0; i < text.length(); i += chunkLen - overlapLen) {
                chunks.add(text.substring(i, Math.min(i + chunkLen, text.length())));
            }
            return chunks;
        }

        StepReg stepReg = stepReges.get(step);
        Pattern reg = stepReg.reg;
        double maxLen = stepReg.maxLen;
        String[] splitTexts = text.split(Pattern.quote(splitMarker));
        StringBuilder newText = new StringBuilder(lastText);
        for (String part : splitTexts) {
            if (part.trim().isEmpty()) {
                continue;
            }
            Matcher matcher = reg.matcher(part);
            StringBuilder currentText = new StringBuilder();
            int lastEnd = 0;
            while (matcher.find()) {
                currentText.append(part.substring(lastEnd, matcher.start()));
                if (step < stepReges.size() - 1) {
                    currentText.append(splitMarker);
                }
                lastEnd = matcher.end();
            }
            currentText.append(part.substring(lastEnd));

            String[] subParts = currentText.toString().split(Pattern.quote(splitMarker));
            for (String subPart : subParts) {
                if (subPart.trim().isEmpty()) {
                    continue;
                }
                newText.append(subPart);
                if (newText.length() > maxLen) {
                    if (lastText.length() > chunkLen * 0.7) {
                        chunks.add(lastText);
                        lastText = getOneTextOverlapText(lastText, step, stepReges, overlapLen, chunkLen);
                        newText = new StringBuilder(subPart);
                    } else {
                        List<String> innerChunks = splitTextRecursively(newText.toString(), step + 1, "", parentTitle, stepReges, overlapLen, chunkLen);
                        String lastChunk = innerChunks.get(innerChunks.size() - 1);
                        if (lastChunk.length() < chunkLen * 0.7) {
                            chunks.addAll(innerChunks.subList(0, innerChunks.size() - 1));
                            lastText = lastChunk;
                            newText = new StringBuilder();
                        } else {
                            chunks.addAll(innerChunks);
                            lastText = getOneTextOverlapText(lastChunk, step, stepReges, overlapLen, chunkLen);
                            newText = new StringBuilder();
                        }
                    }
                }
            }
        }

        if (newText.length() > 0) {
            if (chunks.size() > 0 && !chunks.get(chunks.size() - 1).endsWith(newText.toString())) {
                if (newText.length() < chunkLen * 0.4) {
                    chunks.set(chunks.size() - 1, chunks.get(chunks.size() - 1) + newText.toString());
                } else {
                    chunks.add(newText.toString());
                }
            } else if (chunks.size() == 0) {
                chunks.add(newText.toString());
            }
        }

        return chunks;
    }

    private static String getOneTextOverlapText(String text, int step, List<StepReg> stepReges, int overlapLen, int chunkLen) {
        if (step >= stepReges.size() || overlapLen == 0) {
            return "";
        }

        StepReg stepReg = stepReges.get(step);
        Pattern reg = stepReg.reg;
        String[] splitTexts = text.split(Pattern.quote(splitMarker));
        StringBuilder overlayText = new StringBuilder();
        for (int i = splitTexts.length - 1; i >= 0; i--) {
            String currentText = splitTexts[i];
            String newText = currentText + overlayText.toString();
            if (newText.length() > overlapLen) {
                if (newText.length() > chunkLen * 0.4) {
                    return getOneTextOverlapText(newText, step + 1, stepReges, overlapLen, chunkLen) + overlayText.toString();
                }
                return newText;
            }
            overlayText.insert(0, currentText);
        }
        return overlayText.toString();
    }

    private static String replaceRegChars(String text) {
        // Implement replaceRegChars function here
        return text;
    }

    public static SplitResponse splitText2Chunks(SplitProps props) {
        String text = props.text;
        long start = System.currentTimeMillis();
        String[] splitWithCustomSign = text.split(Pattern.quote(CUSTOM_SPLIT_SIGN));

        List<SplitResponse> splitResult = new ArrayList<>();
        for (String item : splitWithCustomSign) {
            if (strIsMdTable(item)) {
                splitResult.add(markdownTableSplit(props));
            } else {
                splitResult.add(commonSplit(props));
            }
        }

        List<String> chunks = new ArrayList<>();
        int chars = 0;
        for (SplitResponse response : splitResult) {
            chunks.addAll(response.chunks);
            chars += response.chars;
        }

        return new SplitResponse(chunks, chars);
    }

    private static class StepReg {
        public Pattern reg;
        public double maxLen;

        public StepReg(Pattern reg, double maxLen) {
            this.reg = reg;
            this.maxLen = maxLen;
        }
    }

    public static void main(String[] args) {
        SplitProps props = new SplitProps("阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德测试xxxxxxxx测试先擦山东发斯蒂芬萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德\n" +
            "阿达是的阿萨德阿萨德阿萨德as大声道阿萨德阿萨德阿萨德as大声道阿萨德", 50, 0.15, List.of());
        SplitResponse response = splitText2Chunks(props);
        System.out.println("Chunks: " + response.chunks);
        System.out.println("Chars: " + response.chars);
    }
}
