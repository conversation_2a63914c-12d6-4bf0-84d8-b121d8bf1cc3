package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.FSDocSyncCommand;
import cn.genn.ai.hub.app.application.command.FSSheetSyncCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FSDeepImport;
import cn.genn.ai.hub.app.application.dto.feishu.NodeData;
import cn.genn.ai.hub.app.application.dto.feishu.video.PostVoiceChatBean;
import cn.genn.ai.hub.app.application.dto.feishu.video.SnapshotConfig;
import cn.genn.ai.hub.app.application.dto.feishu.video.UserPrompt;
import cn.genn.ai.hub.app.application.enums.feishu.FSFileType;
import cn.genn.ai.hub.app.application.query.FSSheetContentQuery;
import cn.genn.ai.hub.app.application.query.FSSpaceQuery;
import cn.genn.ai.hub.app.infrastructure.config.voice.ASRConfig;
import cn.genn.ai.hub.app.infrastructure.config.voice.VoiceProperties;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 插件转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FSAssembler {

    FSAssembler INSTANCE = Mappers.getMapper(FSAssembler.class);

    FSSheetContentQuery convert(FSSheetSyncCommand command);

    @Mapping(target = "nodeData", ignore = true)
    FSDeepImport convertDI(FSDocSyncCommand command);

    default FSSpaceQuery convertFSQ(FSDeepImport deepImport, NodeData nodeData) {
        if (deepImport == null) {
            return null;
        }
        FSSpaceQuery fSSpaceQuery = new FSSpaceQuery();
        fSSpaceQuery.setRepoId(deepImport.getRepoId());
        fSSpaceQuery.setAppId(deepImport.getAppId());
        fSSpaceQuery.setNodeToken(nodeData.getNodeToken());
        fSSpaceQuery.setSpaceId(nodeData.getSpaceId());
        fSSpaceQuery.setObjType(FSFileType.DOCX);
        return fSSpaceQuery;
    }

    @Mapping(target = "nodeData", ignore = true)
    FSDocSyncCommand convertFSC(FSDeepImport deepImport);

    FSDocSyncCommand convertDSC(KbRepoCollectionDTO sourceCllDTO);


    @Mapping(target = "asrConfig", source = "asr")
    @Mapping(target = "ttsConfig", source = "tts")
    @Mapping(target = "llmConfig", source = "llm")
    @Mapping(target = "subtitleConfig", source = "subtitle")
    PostVoiceChatBean.Config convertVoiceConfig(VoiceProperties voice);

    List<PostVoiceChatBean.Config.LLMConfig.UserPrompts> convertUP(List<UserPrompt> userPrompts);

    PostVoiceChatBean.Config.LLMConfig.VisionConfig.SnapshotConfig convertSSC(SnapshotConfig snapshotConfig);
}
