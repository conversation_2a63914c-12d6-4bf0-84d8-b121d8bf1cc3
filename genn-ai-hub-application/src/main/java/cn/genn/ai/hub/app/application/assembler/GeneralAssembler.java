package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.QuestionGapAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.feishu.AvatarDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GeneralAssembler {

    GeneralAssembler INSTANCE = Mappers.getMapper(GeneralAssembler.class);

    AvatarDTO convertFSAvatar(AvatarInfo avatar);

    @Mapping(target = "questionId", source = "id")
    QuestionGapAnalysisCommand convertGapAnl(QuestionInfoDTO dto);
}

