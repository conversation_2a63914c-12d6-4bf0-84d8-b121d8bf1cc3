package cn.genn.ai.hub.app.application.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * QuestionAnalysis查询对象
 *
 * <AUTHOR>
 */
@Data
public class QuestionAnalysisQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流id")
    @NotNull(message = "工作流ID不能为空")
    private String appId;

    @Schema(description = "来源")
    private List<String> sources;

    @Schema(description = "top N", example = "10")
    private Integer top = 10;

}

