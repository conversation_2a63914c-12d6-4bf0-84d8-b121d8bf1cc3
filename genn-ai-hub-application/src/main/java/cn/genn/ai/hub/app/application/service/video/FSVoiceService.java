package cn.genn.ai.hub.app.application.service.video;

import cn.genn.ai.hub.app.application.assembler.FSAssembler;
import cn.genn.ai.hub.app.application.dto.feishu.video.*;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.voice.VoiceProperties;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 音视频聊天
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FSVoiceService {

    @Resource
    private FSAssembler fsaAssembler;

    @Resource
    private GennAIHubProperties aiHubProperties;


    public void StartVoiceChat(StartVoiceChatRequest command) {
        RtcVoiceServiceImpl voiceService = RtcVoiceServiceImpl.getInstance();
        VoiceProperties voice = aiHubProperties.getVoice();
        voiceService.setAccessKey(voice.getAccessKey());
        voiceService.setSecretKey(voice.getSecretKey());
        try {
            voiceService.startVoiceChat(getPostVoiceChatBean(command));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void UpdateVoiceChat(UpdateVoiceChatRequest command) {
        try {
            VoiceProperties voice = aiHubProperties.getVoice();
            PostVoiceChatBean postVoiceChatBean = new PostVoiceChatBean();
            postVoiceChatBean.setAppId(voice.getAppId());
            postVoiceChatBean.setRoomId(command.getRoomId());
            postVoiceChatBean.setTaskId(command.getTaskId());
            postVoiceChatBean.setCommand(command.getCommand());
            postVoiceChatBean.setMessage(command.getMessage());
            postVoiceChatBean.setInterruptMode(command.getInterruptMode());

            RtcVoiceService rtcVoiceService = RtcVoiceServiceImpl.getInstance();
            rtcVoiceService.setAccessKey(voice.getAccessKey());
            rtcVoiceService.setSecretKey(voice.getSecretKey());

            ResponseBean response = rtcVoiceService.updateVoiceChat(postVoiceChatBean);
        } catch (Exception e) {
            log.error("UpdateVoiceChat Exception", e);
        }
    }

    public void StopVoiceChat(BaseVoiceChatRequest command) {
        RtcVoiceServiceImpl voiceService = RtcVoiceServiceImpl.getInstance();
        VoiceProperties voice = aiHubProperties.getVoice();
        voiceService.setAccessKey(voice.getAccessKey());
        voiceService.setSecretKey(voice.getSecretKey());

        PostVoiceChatBean postVoiceChatBean = new PostVoiceChatBean();
        postVoiceChatBean.setAppId(voice.getAppId());

        postVoiceChatBean.setRoomId(command.getRoomId());
        postVoiceChatBean.setTaskId(command.getTaskId());

        try {
            voiceService.stopVoiceChat(postVoiceChatBean);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private PostVoiceChatBean getPostVoiceChatBean(StartVoiceChatRequest chatRequest) {
        PostVoiceChatBean postVoiceChatBean = new PostVoiceChatBean();
        VoiceProperties voice = aiHubProperties.getVoice();
        postVoiceChatBean.setAppId(voice.getAppId());
        postVoiceChatBean.setRoomId(chatRequest.getRoomId());
        postVoiceChatBean.setTaskId(chatRequest.getRoomId());

        PostVoiceChatBean.Config config = fsaAssembler.convertVoiceConfig(voice);
        // LLMConfig ==》 apiKey
        PostVoiceChatBean.Config.LLMConfig llmConfig = config.getLlmConfig();
        Map<String, String> apiKeyMap = voice.getLlm().getApiKeyMap();
        llmConfig.setAPIKey(apiKeyMap.get(chatRequest.getAgentId()));

        // 业务自定义参数  customConfig
        PostVoiceChatBean.Config.LLMConfig.CustomConfig customConfig = new PostVoiceChatBean.Config.LLMConfig.CustomConfig();
        customConfig.setAppId(chatRequest.getAgentId());
        customConfig.setChatId(chatRequest.getChatId());
        customConfig.setOnlyAnswer(true);
        customConfig.setGennAiToken(chatRequest.getGennAiToken());
        llmConfig.setCustom(JsonUtils.toJson(customConfig));

        List<PostVoiceChatBean.Config.LLMConfig.UserPrompts> userPrompts = Optional.ofNullable(fsaAssembler.convertUP(chatRequest.getUserPrompts()))
            .orElse(Lists.newArrayList());
        if (CollUtil.isEmpty(userPrompts)) {
            PostVoiceChatBean.Config.LLMConfig.UserPrompts userPrompts1 = new PostVoiceChatBean.Config.LLMConfig.UserPrompts();
            userPrompts1.setRole("user");
            userPrompts1.setContent("你好");
            userPrompts.add(userPrompts1);

            PostVoiceChatBean.Config.LLMConfig.UserPrompts userPrompts2 = new PostVoiceChatBean.Config.LLMConfig.UserPrompts();
            userPrompts2.setRole("assistant");
            userPrompts2.setContent("有什么可以帮到你的？");
            userPrompts.add(userPrompts2);
        }

        PostVoiceChatBean.Config.LLMConfig.UserPrompts[] userPromptsArray =
            userPrompts.toArray(new PostVoiceChatBean.Config.LLMConfig.UserPrompts[0]);
        llmConfig.setUserPrompts(userPromptsArray);
        llmConfig.setHistoryLength(0);

        // Vision Config
        PostVoiceChatBean.Config.LLMConfig.VisionConfig visionConfig = llmConfig.getVisionConfig();

        PostVoiceChatBean.Config.LLMConfig.VisionConfig.SnapshotConfig snapshotConfig =
            Optional.ofNullable(fsaAssembler.convertSSC(chatRequest.getSnapshotConfig())).orElse(
                new PostVoiceChatBean.Config.LLMConfig.VisionConfig.SnapshotConfig());
        visionConfig.setSnapshotConfig(snapshotConfig);

        postVoiceChatBean.setConfig(config);

        // Agent Config
        PostVoiceChatBean.AgentConfig agentConfig = new PostVoiceChatBean.AgentConfig();
        String[] userIds = new String[1]; // 真人用户 ID。需使用客户端 SDK 进房的真人用户的 UserId 一致
        userIds[0] = CurrentUserHolder.getUserId().toString();
        agentConfig.setTargetUserId(userIds);
        agentConfig.setUserId("BotName" + CurrentUserHolder.getUserId().toString());
        agentConfig.setEnableConversationStateCallback(true);
        postVoiceChatBean.setAgentConfig(agentConfig);

        return postVoiceChatBean;
    }

}

