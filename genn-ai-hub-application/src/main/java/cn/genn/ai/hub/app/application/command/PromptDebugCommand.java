package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 提示词对比命令
 * @date 2025-05-12
 */
@Data
public class PromptDebugCommand {

    @Schema(description = "模型key")
    private String modelKey;

    @Schema(description = "系统提示词")
    private String systemPrompt;

    @Schema(description = "用户提示词")
    private String userPrompt;

}
