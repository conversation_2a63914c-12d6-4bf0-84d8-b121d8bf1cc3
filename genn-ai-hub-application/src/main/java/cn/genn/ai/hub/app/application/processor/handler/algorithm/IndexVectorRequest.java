package cn.genn.ai.hub.app.application.processor.handler.algorithm;


import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexVectorRequest {

    private Long tenantId;

    private Long taskId;
    private Long userId;

    private Long repoId;

    private Long collectionId;

    private String dataKey;

    private String indexKey;

    private String rawText;

    private String vectorModelKey;
}
