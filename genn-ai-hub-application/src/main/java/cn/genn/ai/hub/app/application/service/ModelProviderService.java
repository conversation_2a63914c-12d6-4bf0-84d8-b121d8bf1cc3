package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.dto.request.ModelProviderQuery;
import cn.genn.ai.hub.app.application.dto.response.ModelProviderDTO;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ModelProviderRepositoryImpl;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelProviderService {

    @Resource
    private ModelProviderRepositoryImpl providerRepository;
    public PageResultDTO<ModelProviderDTO> page(ModelProviderQuery query) {
        return null;
    }

    public ModelProviderDTO getInfo(IdQuery query) {
        return providerRepository.getInfo(query.getId());
    }

    public List<ModelProviderDTO> getList() {
        return providerRepository.getActiveList();
    }
}

