package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.response.ModelLogDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelLogPO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModelLogAssembler {

    ModelLogAssembler INSTANCE = Mappers.getMapper(ModelLogAssembler.class);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<ModelLogDTO> toPageResult(IPage<ModelLogPO> poPage);

}

