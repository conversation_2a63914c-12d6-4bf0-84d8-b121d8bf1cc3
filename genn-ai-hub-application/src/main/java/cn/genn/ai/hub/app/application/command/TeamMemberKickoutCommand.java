package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 团队成员踢出命令
 *
 * <AUTHOR>
 */
@Data
public class TeamMemberKickoutCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队ID")
    @NotNull(message = "团队ID不能为空")
    private Long teamId;

    @Schema(description = "被踢出用户ID")
    @NotNull(message = "被踢出用户ID不能为空")
    private Long userId;

}
