package cn.genn.ai.hub.app.application.service.video;

import com.volcengine.helper.Const;
import com.volcengine.model.ApiInfo;
import com.volcengine.model.Credentials;
import com.volcengine.model.ServiceInfo;
import org.apache.http.Header;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.volcengine.helper.Const.Header;

public class RtcVoiceConfig {
    public static ServiceInfo serviceInfo = new ServiceInfo(
        new HashMap<String, Object>() {
            {
                put(Const.CONNECTION_TIMEOUT, 5000);
                put(Const.SOCKET_TIMEOUT, 5000);
                put(Const.Scheme, "https");
                put(Const.Host, "rtc.volcengineapi.com");
                put(Header, new ArrayList<Header>() {
                    {
                        add(new BasicHeader("Accept", "application/json"));
                    }
                });
                put(Const.Credentials, new Credentials("cn-beijing", "rtc"));
            }
        }
    );
    public static Map<String, ApiInfo> apiInfoList = new HashMap<String, ApiInfo>() {
        {
            put("StartVoiceChat", new ApiInfo(
                new HashMap<String, Object>() {
                    {
                        put(Const.Method, "post");
                        put(Const.Path, "/");
                        put(Const.Query, new ArrayList<NameValuePair>() {
                            {
                                add(new BasicNameValuePair("Action", "StartVoiceChat"));
                                add(new BasicNameValuePair("Version", "2024-12-01"));
                            }
                        });
                    }
                }
            ));


            put("StopVoiceChat", new ApiInfo(
                new HashMap<String, Object>() {
                    {
                        put(Const.Method, "post");
                        put(Const.Path, "/");
                        put(Const.Query, new ArrayList<NameValuePair>() {
                            {
                                add(new BasicNameValuePair("Action", "StopVoiceChat"));
                                add(new BasicNameValuePair("Version", "2024-12-01"));
                            }
                        });
                    }
                }
            ));

            put("UpdateVoiceChat", new ApiInfo(
                new HashMap<String, Object>() {
                    {
                        put(Const.Method, "post");
                        put(Const.Path, "/");
                        put(Const.Query, new ArrayList<NameValuePair>() {
                            {
                                add(new BasicNameValuePair("Action", "UpdateVoiceChat"));
                                add(new BasicNameValuePair("Version", "2024-12-01"));
                            }
                        });
                    }
                }
            ));
        }
    };
}
