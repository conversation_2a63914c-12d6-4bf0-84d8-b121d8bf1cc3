package cn.genn.ai.hub.app.application.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 微信消息推送命令
 * @date 2025-04-23
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WxCallbackCommand {

    private String signature; // 明文模式签名

    @JsonProperty("msg_signature")
    private String msgSignature; // 加密模式签名

    private String timestamp;

    private String nonce;

    @JsonProperty("encrypt_type")
    private String encryptType;
}
