package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class McpGroupPublishCommand {

    @Schema(description = "mcp分组id")
    private Long id;

    @Schema(description = "工具图标")
    private String toolAvatar;

    @Schema(description = "标签id集合")
    private List<Long> tagIds;
}
