package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.response.ModelProviderDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelProviderPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModelProviderAssembler {

    ModelProviderAssembler INSTANCE = Mappers.getMapper(ModelProviderAssembler.class);

    List<ModelProviderDTO> convert(List<ModelProviderPO> modelProviderPOS);

    ModelProviderDTO convert(ModelProviderPO modelProviderPO);
}

