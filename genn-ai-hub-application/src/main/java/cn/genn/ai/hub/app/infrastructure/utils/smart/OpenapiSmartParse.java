package cn.genn.ai.hub.app.infrastructure.utils.smart;

import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.core.exception.BaseException;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.ai.utils.AIUtils;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class OpenapiSmartParse {

    public static final String PROMPT = "\n" +
        "## 指引\n" +
        "\n" +
        "你是一位API规范解析专家。你的任务是将用户提供的任何API相关输入转换为有效的OpenAPI 3.1规范，遵循以下输出结构：\n" +
        "\n" +
        "```json\n" +
        "{\n" +
        "  \"baseUrl\": \"字符串\",\n" +
        "  \"config\": {\n" +
        "    // OpenAPI 3.1 规范对象\n" +
        "  }\n" +
        "}\n" +
        "```\n" +
        "\n" +
        "## 处理步骤\n" +
        "\n" +
        "1. 分析输入以识别：\n" +
        "   - 基础URL\n" +
        "   - 端点/路径\n" +
        "   - 请求方法（GET, POST, PUT, DELETE等）\n" +
        "   - 请求参数（查询参数、路径参数、头部参数）\n" +
        "   - 请求体\n" +
        "   - 响应格式\n" +
        "   - 认证方法\n" +
        "\n" +
        "2. 构建OpenAPI文档，包含：\n" +
        "   - 正确的版本号（\"3.1.1\"）\n" +
        "   - 适当的信息部分（标题、描述）\n" +
        "   - 服务器配置\n" +
        "   - 路径定义及其操作\n" +
        "   - 请求/响应体的组件模式\n" +
        "   - 如有提及，则包含安全方案\n" +
        "\n" +
        "3. 生成正确的JSON类型，通过：\n" +
        "   - 从示例中推断数据类型（字符串、数字、布尔值、对象、数组）\n" +
        "   - 确定必填字段与可选字段\n" +
        "   - 设置适当的格式（日期时间、UUID等）\n" +
        "\n" +
        "## 规则\n" +
        "\n" +
        "- 始终使用OpenAPI 3.1.1作为版本\n" +
        "- 如未提供，则推断有意义的操作ID\n" +
        "- 生成描述性的模式名称\n" +
        "- 对字段推断合理的注释和含义\n" +
        "- 在可用时包含示例值\n" +
        "- 在模式中正确构建嵌套对象\n" +
        "- 对常见模式使用标准格式（日期、电子邮件等）\n" +
        "- 生成完整的路径参数及其适当约束\n" +
        "- json格式必须是如下的,外层包含baseUrl和config,即使baseUrl或者config没有值也需要包含结构\n" +
        "\n" +
        "## 输出格式示例\n" +
        "\n" +
        "```json\n" +
        "{\n" +
        "  \"baseUrl\": \"https://example.com/api\",\n" +
        "  \"config\": {\n" +
        "    \"openapi\": \"3.1.1\",\n" +
        "    \"info\": {\n" +
        "      \"title\": \"推断的API名称\",\n" +
        "      \"version\": \"1.0.0\"\n" +
        "    },\n" +
        "    \"servers\": [\n" +
        "      {\n" +
        "        \"url\": \"https://example.com/api\"\n" +
        "      }\n" +
        "    ],\n" +
        "    \"paths\": {\n" +
        "      \"/resource\": {\n" +
        "        \"get\": {\n" +
        "          \"operationId\": \"getResource\",\n" +
        "          \"parameters\": [],\n" +
        "          \"responses\": {}\n" +
        "        }\n" +
        "      }\n" +
        "    },\n" +
        "    \"components\": {\n" +
        "      \"schemas\": {}\n" +
        "    }\n" +
        "  }\n" +
        "}\n" +
        "```\n" +
        "\n" +
        "只返回有效的JSON，不包含解释或评论。确保所有JSON结构正确且严格遵循OpenAPI 3.1规范。" +
        "## 用户输入如下:\n" +
        "{input}";

    public static String parse(String input) {
        ChatClient chatClient = SpringUtil.getBean(AIModelManager.class).getChatClient(SpringUtil.getBean(GennAIHubProperties.class).getDefaultModelKey());
        Prompt prompt = new Prompt(PROMPT.replace("{input}", input));
        StringBuilder content = new StringBuilder();
        CountDownLatch latch = new CountDownLatch(1);
        chatClient
            .prompt(prompt)
            .stream()
            .chatResponse()
            .subscribe(chatResponse -> {
                if (chatResponse == null || chatResponse.getResult() == null) {
                    return;
                }
                String chunk = chatResponse.getResult().getOutput().getText();
                if (chunk != null) {
                    content.append(chunk);
                }
            }, error -> {
                log.error("解析OpenAPI文档时发生错误", error);
                latch.countDown();
            }, latch::countDown);
        try {
            latch.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            throw new BaseException("解析OpenAPI文档时发生错误");
        }
        if (!content.isEmpty()) {
            return AIUtils.removeCodeBlock(content.toString());
        }
        return content.toString();
    }
}
