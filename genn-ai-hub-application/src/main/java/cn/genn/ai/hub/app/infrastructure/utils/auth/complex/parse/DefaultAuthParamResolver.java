package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse;

import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParamResolver;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DefaultAuthParamResolver implements AuthParamResolver {

    @Override
    public List<AuthParam> authParams(AuthInterceptorParam interceptor) {
        return null;
    }
}
