package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.IndexTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoDataIndex操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoDataIndexOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "知识库 ID，标识数据所属的知识库")
    private Long repoId;

    @Schema(description = "知识集合 ID，与 collection 表关联，表示数据所属的集合")
    private Long collectionId;

    @Schema(description = "数据条目唯一标识")
    private String dataKey;

    @Schema(description = "索引唯一标识")
    private String indexKey;

    @Schema(description = "索引类型, default:DEFAULT-默认, customer:CUSTOMER-自定义")
    private IndexTypeEnum indexType;

    @Schema(description = "索引优先级，越大优先级越高")
    private Integer sort;

    @Schema(description = "自定义索引内容")
    private String indexData;

    @Schema(description = "索引算法处理状态,0:WAIT-未处理, 1:PROCESSING-处理中,2:DONE-已处理")
    private HandleStatusEnum handleStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

