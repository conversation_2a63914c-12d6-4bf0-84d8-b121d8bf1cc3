package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.QuestionInfoCommand;
import cn.genn.ai.hub.app.application.dto.question.DepartmentUserDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionDetailDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import cn.genn.ai.hub.app.application.dto.question.UserInfoDTO;
import cn.genn.ai.hub.app.application.query.QuestionInfoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionInfoPO;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.spring.boot.starter.upm.model.FsDepartmentDTO;
import cn.genn.spring.boot.starter.upm.model.FsUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * QuestionInfo转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuestionInfoAssembler extends QueryAssembler<QuestionInfoQuery, QuestionInfoPO, QuestionInfoDTO> {

    QuestionInfoAssembler INSTANCE = Mappers.getMapper(QuestionInfoAssembler.class);

    QuestionInfoPO convertPO(QuestionInfoCommand command);

    /**
     * PO转QuestionDetailDTO
     */
    List<QuestionDetailDTO> PO2DetailDTO(List<QuestionInfoPO> pos);

    /**
     * PO转QuestionDetailDTO
     */
    QuestionDetailDTO PO2DetailDTO(QuestionInfoPO po);

    /**
     * UpmUserDTO转UserInfoDTO
     */
    default UserInfoDTO upmUser2UserInfo(UpmUserDTO upmUser){
        if(ObjUtil.isNull(upmUser)){
            return null;
        }
        UserInfoDTO result  = new UserInfoDTO();
        result.setUserId(upmUser.getId());
        result.setName(upmUser.getNick());
        result.setAvatar(upmUser.getAvatar());
        result.setTelephone(upmUser.getTelephone());
        if(ObjUtil.isNotNull(upmUser.getFsUser())){
            FsUserDTO fsUser = upmUser.getFsUser();
            List<String> departments = new ArrayList<>();
            if(CollUtil.isNotEmpty(fsUser.getFsDepartments())){
                departments = fsUser.getFsDepartments().stream().map(FsDepartmentDTO::getName).toList();
            }
            result.setOpenId(fsUser.getFsOpenId());
            result.setDepartments(departments);
            result.setAvatar(fsUser.getFsAvatarUrl());
            result.setName(fsUser.getFsName());
        }
        return result;

    }

    /**
     * PO转DepartmentUserDTO
     */
    DepartmentUserDTO PO2DepartmentUserDTO(QuestionInfoPO po);

    /**
     * PO转DepartmentUserDTO列表
     */
    List<DepartmentUserDTO> PO2DepartmentUserDTO(List<QuestionInfoPO> pos);
}

