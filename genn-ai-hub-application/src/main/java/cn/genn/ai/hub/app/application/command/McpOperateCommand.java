package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.ExposeMcpTypeEnum;
import cn.genn.ai.hub.app.application.enums.McpTypeEnum;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.McpConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * Mcp操作对象
 *
 * <AUTHOR>
 */
@Data
public class McpOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "mcp名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "mcp类型，stdio:STDIO-标准输入输出,sse:SSE-服务器发送事件,streamablehttp:STREAMABLEHTTP-可流式HTTP,openapi:OPENAPI-开放API")
    private McpTypeEnum mcpType;

    @Schema(description = "mcp配置")
    private McpConfig mcpConfig;

}

