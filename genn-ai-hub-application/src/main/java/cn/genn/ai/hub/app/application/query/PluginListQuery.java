package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.core.plugin.PluginType;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 插件列表查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "插件列表查询参数")
public class PluginListQuery extends PageSortQuery {

    /**
     * 目录ID
     */
    @Schema(description = "目录ID")
    private Long catId;

    /**
     * 插件名称，模糊查询
     */
    @Schema(description = "插件名称，模糊查询")
    private String pluginName;

    /**
     * 插件类型
     */
    @Schema(description = "插件类型")
    private PluginType pluginType;
}
