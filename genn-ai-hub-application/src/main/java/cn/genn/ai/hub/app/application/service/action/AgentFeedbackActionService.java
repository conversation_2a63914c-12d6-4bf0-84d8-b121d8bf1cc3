package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.AgentFeedbackAssembler;
import cn.genn.ai.hub.app.application.command.AgentFeedbackCreateCommand;
import cn.genn.ai.hub.app.application.enums.FeedbackTypeEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentFeedbackMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentFeedbackPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体反馈应用服务
 * @date 2025-07-02
 */
@Service
@RequiredArgsConstructor
public class AgentFeedbackActionService {

    private final AgentFeedbackMapper agentFeedbackMapper;

    private final AgentFeedbackAssembler agentFeedbackAssembler;

    public Long create(AgentFeedbackCreateCommand command) {
        if (List.of(FeedbackTypeEnum.LIKE, FeedbackTypeEnum.DISLIKE).contains(command.getFeedbackType())) {
            // 查询是否已存在
            AgentFeedbackPO exist = agentFeedbackMapper.selectOne(Wrappers.lambdaQuery(AgentFeedbackPO.class)
                .eq(AgentFeedbackPO::getWorkflowId, command.getWorkflowId())
                .eq(AgentFeedbackPO::getChatId, command.getChatId())
                .eq(AgentFeedbackPO::getTaskId, command.getTaskId())
                .eq(AgentFeedbackPO::getSourceSystem, command.getSourceSystem())
                .in(AgentFeedbackPO::getFeedbackType, List.of(FeedbackTypeEnum.LIKE, FeedbackTypeEnum.DISLIKE))
                .eq(AgentFeedbackPO::getCreateUserId, command.getCreateUserId()));
            if (exist != null) {
                agentFeedbackMapper.deleteById(exist.getId());
            }
        }
        AgentFeedbackPO agentFeedbackPO = agentFeedbackAssembler.toPO(command);
        agentFeedbackMapper.insert(agentFeedbackPO);
        return agentFeedbackPO.getId();
    }

    @IgnoreTenant
    public void cancel(Long id) {
        agentFeedbackMapper.deleteById(id);
    }
}
