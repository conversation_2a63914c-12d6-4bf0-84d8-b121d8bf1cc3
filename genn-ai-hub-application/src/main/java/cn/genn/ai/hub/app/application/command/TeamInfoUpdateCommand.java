package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TeamInfo更新命令
 *
 * <AUTHOR>
 */
@Data
public class TeamInfoUpdateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @NotNull(message = "团队ID不能为空")
    private Long id;

    @Schema(description = "团队名称")
    @NotBlank(message = "团队名称不能为空")
    private String name;

}
