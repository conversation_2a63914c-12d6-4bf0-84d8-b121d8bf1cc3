package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.ResourceInviteCommand;
import cn.genn.ai.hub.app.application.command.ResourceRemoveInviteCommand;
import cn.genn.ai.hub.app.application.command.TagsAssignCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoEditCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver{

    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long kbId = switch (uniqueId) {
            case "editKbRepoDetail" -> ((KbRepoBaseInfoEditCommand) interceptor.getParamsMap().get("command")).getId();
            case "deleteKbRepo" -> ((IdCommand) interceptor.getParamsMap().get("command")).getId();
            case "assignTags" -> ((TagsAssignCommand) interceptor.getParamsMap().get("command")).getId();
            case "inviteCollaborator" -> ((ResourceInviteCommand) interceptor.getParamsMap().get("command")).getId();
            case "removeCollaborator" -> ((ResourceRemoveInviteCommand) interceptor.getParamsMap().get("command")).getId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return kbRepoBaseInfoMapper.selectById(kbId).getTeamId();
    }
}
