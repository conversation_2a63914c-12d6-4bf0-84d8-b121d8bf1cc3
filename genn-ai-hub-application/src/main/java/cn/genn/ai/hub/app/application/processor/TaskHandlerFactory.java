package cn.genn.ai.hub.app.application.processor;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TaskHandlerFactory {
    private final Map<TaskTypeEnum, TaskHandler> handlers = new HashMap<>();

    @Autowired
    public TaskHandlerFactory(List<TaskHandler> handlerList) {
        // 通过自定义注解或命名约定建立映射
        for (TaskHandler handler : handlerList) {
            handlers.put(handler.getType(), handler);

        }
    }

    public TaskHandler getHandler(TaskTypeEnum taskType) {
        return handlers.get(taskType);
    }

    public void removeProcessTask(KbRepoTaskDTO task) {
        if (task == null) {
            return;
        }
        TaskHandler taskHandler = handlers.get(task.getTaskType());
        taskHandler.removeTaskFromProcessQueue(task);
    }

}
