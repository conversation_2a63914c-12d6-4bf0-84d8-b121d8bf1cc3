package cn.genn.ai.hub.app.infrastructure.utils;

import cn.genn.core.utils.thread.ContextAwareThreadPoolExecutor;
import cn.genn.core.utils.thread.decorator.MDCContextAwareDecorator;
import cn.hutool.core.thread.NamedThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class AsyncExecutor {

    public static final ExecutorService PLUGIN_STREAM_EXECUTOR_SERVICE = new ContextAwareThreadPoolExecutor(10,
        10,
        0L,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(Integer.MAX_VALUE),
        new NamedThreadFactory("plugin-stream-executor", false),
        new MDCContextAwareDecorator()
    );

    public static final ExecutorService GOVOW_OPERATION_SERVICE = new ContextAwareThreadPoolExecutor(5,
        5,
        60L,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(Integer.MAX_VALUE),
        new NamedThreadFactory("send_govow_operation", false),
        new MDCContextAwareDecorator()
    );
    public static final ExecutorService RESOURCE_SERVICE = new ContextAwareThreadPoolExecutor(10,
        10,
        60L,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(Integer.MAX_VALUE),
        new NamedThreadFactory("feishu_resorce_query", false),
        new MDCContextAwareDecorator()
    );


}
