package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 插件查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书文档子列表查询参数")
public class FSPluginMultiSheetQuery extends FSPluginQuery {

    @Schema(description = "sheetId", required = true)
    @NotEmpty(message = "sheetId 不能为空")
    private String tableId;

    @Schema(description = "viewId")
    private String viewId;

    @Schema(description = "表格token-获取表格内容用", required = true)
    private String objToken;

}
