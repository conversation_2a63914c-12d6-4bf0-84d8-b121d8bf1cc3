package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.ModelManageDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelManagePO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GatewayModelAssembler {

    GatewayModelAssembler INSTANCE = Mappers.getMapper(GatewayModelAssembler.class);

    @Mapping(target = "commonConfig", expression = "java(cn.hutool.json.JSONUtil.toJsonStr(model.getCommonConfig()))")
    ModelManageDTO convert(ModelManagePO model);

    List<ModelManageDTO> convert(List<ModelManagePO> models);
}

