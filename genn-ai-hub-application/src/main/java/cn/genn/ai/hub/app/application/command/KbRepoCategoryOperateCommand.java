package cn.genn.ai.hub.app.application.command;

import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoCategory操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoCategoryOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "目录名称")
    private String catName;

    @Schema(description = "父目录id")
    private Long pid;

    @Schema(description = "级别")
    private Integer level;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

