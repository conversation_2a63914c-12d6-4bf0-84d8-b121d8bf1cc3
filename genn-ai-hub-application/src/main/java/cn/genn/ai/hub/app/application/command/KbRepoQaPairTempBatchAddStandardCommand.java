package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 临时问答对批量添加到标准问答库
 *
 * @Date: 2025/4/10
 * @Author: kang<PERSON>an
 */
@Data
public class KbRepoQaPairTempBatchAddStandardCommand {

    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private List<Long> idList;

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    @NotNull(message = "知识库ID不能为空")
    private Long repoId;


}
