package cn.genn.ai.hub.app.application.service.video;

import cn.genn.ai.hub.app.application.dto.feishu.video.PostVoiceChatBean;
import cn.genn.ai.hub.app.application.dto.feishu.video.ResponseBean;
import com.volcengine.service.IBaseService;



public interface RtcVoiceService extends IBaseService {

    ResponseBean startVoiceChat(PostVoiceChatBean postVoiceChatBean) throws Exception;

    ResponseBean stopVoiceChat(PostVoiceChatBean postVoiceChatBean) throws Exception;

    ResponseBean updateVoiceChat(PostVoiceChatBean postVoiceChatBean) throws Exception;
}
