package cn.genn.ai.hub.app.infrastructure.config;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体应用配置
 * @date 2025-04-09
 */
@Data
public class AgentProperties {

    /**
     * 智能体调用域名
     */
    private String invokeDomain = "https://cerebro-sit.genn.cn";

    /**
     * 内部鉴权
     */
    private String authorization = "Bearer gennai-internal-api-key-4vQYhr7VPLTQyV9X4GRPQgmRrDg4Kb7qsdGP";



    private Integer maxRetries = 3;

    /**
     * 高频问题统计
     */
    private String highFreApiKey = "gennai-uIePn9MMUaptejVxEXrWVlnsPtyMdyIRYR1xqA5bxiVVDDxyHBRntYyt";

    /**
     * 知识缺失分析
     */
    private String gapAnlApiKey = "gennai-xqS9TnK5uy6EvXBcp7wXinUBQYVupJwtMvXHKJvjmYDz2bvaa9VLNX";

    /**
     * 集团日报,格物运营引流
     */
    private String govowTableApiKey = "gennai-sUPYZMlx4PXAt5PkGtlrxAq2m7xwHwRK3GOU56UD4O12MudOGDR2ZlaZDaxZ71";

    /**
     *  过滤appId
     */
    private List<String> filter = Lists.newArrayList("687a031486a252e25e42a2ac", "687a039686a252e25e42a3f0");
}
