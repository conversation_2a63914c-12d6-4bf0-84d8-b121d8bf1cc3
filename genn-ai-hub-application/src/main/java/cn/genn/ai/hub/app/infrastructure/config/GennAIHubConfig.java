package cn.genn.ai.hub.app.infrastructure.config;

import cn.genn.ai.hub.app.infrastructure.config.auth.AuthSqlInterceptor;
import cn.genn.ai.hub.app.infrastructure.config.auth.UserManagerInterceptor;
import cn.genn.web.http.HttpClientProperties;
import cn.genn.web.spring.component.request.HttpClientFactoryCreator;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.web.client.RestClient;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class GennAIHubConfig implements WebMvcConfigurer {

    @Bean
    @Order(-1)
    public InnerInterceptor authSqlInterceptor() {
        return new AuthSqlInterceptor();
    }

    @Bean
    public MilvusClientV2 milvusClient(GennAIHubProperties properties) {
        return new MilvusClientV2(ConnectConfig.builder()
            .uri(properties.getMilvus().getUrl())
            .dbName(properties.getMilvus().getDatabaseName())
            .build());
    }

    @Bean
    public RestClient htmlConverterRestClient(GennAIHubProperties properties) {
        HttpClientProperties httpClientProperties = new HttpClientProperties();
        httpClientProperties.setConnectTimeout(100_000);
        httpClientProperties.setReadTimeout(100_000);
        return RestClient.builder()
            .baseUrl(properties.getKbInvokeUrl().getBaseUrl())
            .requestFactory(HttpClientFactoryCreator.clientHttpRequestFactory(httpClientProperties)).build();
    }

    /**
     * 注入一个默认的 RestClient.Builder,防止覆盖底层的 RestClient.Builder
     * @param configurer
     * @return
     */
    @Bean
    @Primary
    RestClient.Builder restClientBuilder(RestClientBuilderConfigurer configurer) {
        return configurer.configure(RestClient.builder());
    }

    @Bean("lbRestClientBuilder")
    @LoadBalanced
    RestClient.Builder lbRestClientBuilder(RestClientBuilderConfigurer configurer) {
        return configurer.configure(RestClient.builder());
    }

    @Bean
    @LoadBalanced
    public RestClient mcpGatewayRestClient(@Qualifier("lbRestClientBuilder") RestClient.Builder lbBuilder, GennAIHubProperties properties) {
        return lbBuilder.baseUrl(properties.getMcp().getMcpGatewayInnerUrl())
            .build();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new UserManagerInterceptor())
            .addPathPatterns("/user/save", "/user/update", "/user/change/status", "/user/batch/delete")
            .order(1);
    }
}
