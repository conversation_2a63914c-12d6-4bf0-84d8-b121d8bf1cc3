package cn.genn.ai.hub.app.infrastructure.utils.auth;

import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AuthUtils {

    public static AuthCommon buildUserAgentAuth(Long agentId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.AGENT, String.valueOf(agentId), actionType);
    }

    public static AuthCommon buildUserKbRepoAuth(Long kbId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.KB_REPO, String.valueOf(kbId), actionType);
    }

    public static AuthCommon buildUserTeamAuth(Long teamId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.TEAM, String.valueOf(teamId), actionType);
    }

    public static AuthCommon buildUserChannelAuth(Long channelId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.CHANNEL, String.valueOf(channelId), actionType);
    }

    public static AuthCommon buildUserPromptAuth(Long promptId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.PROMPT, String.valueOf(promptId), actionType);
    }

    public static AuthCommon buildUserSensitiveWordAuth(Long sensitiveWordId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.SENSITIVE_WORD, String.valueOf(sensitiveWordId), actionType);
    }

    public static AuthCommon buildUserCodeRepoAuth(Long codeRepoId, Long userId, ActionType actionType) {
        return AuthCommon.of(SubjectType.USER, String.valueOf(userId), ResourceType.CODE_REPO, String.valueOf(codeRepoId), actionType);
    }

    public static Map<AuthUniqueCriteriaEnum, Object> buildUserTeamUniqueCriteria(Long teamId, Long userId) {
        return Map.of(AuthUniqueCriteriaEnum.SUBJECT_TYPE, SubjectType.USER,
                AuthUniqueCriteriaEnum.SUBJECT_KEY, String.valueOf(userId),
                AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.TEAM,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(teamId));
    }

}
