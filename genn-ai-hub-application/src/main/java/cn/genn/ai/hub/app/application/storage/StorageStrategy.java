package cn.genn.ai.hub.app.application.storage;

import cn.genn.ai.hub.core.api.tool.FileStorageConfig;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

public interface StorageStrategy {

    boolean saveFile(MultipartFile file, String objectKey);

    boolean saveContentTOFile(String content, String objectKey);

    InputStream getObject(String objectKey);

    String preSignedUrlGetObject(String objectKey, String originFileName);

    String getCachedPreSignedUrl(String objectKey, String originFileName);

    boolean saveFile(MultipartFile file, String objectKey, FileStorageConfig config);

    String preSignedUrlGetObject(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config);

    default String preSignedUrlGetObjectByCustomDomain(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
        throw new UnsupportedOperationException();
    }

    boolean saveOutPutStream(ByteArrayOutputStream content, String objectKey);

    boolean saveByteArray(byte[] byteArray, String objectKey);
}
