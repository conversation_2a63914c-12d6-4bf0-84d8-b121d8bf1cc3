package cn.genn.ai.hub.app.domain.question.service;

import cn.genn.ai.hub.app.application.assembler.GeneralAssembler;
import cn.genn.ai.hub.app.application.assembler.QuestionInfoAssembler;
import cn.genn.ai.hub.app.application.command.QuestionInfoCommand;
import cn.genn.ai.hub.app.application.dto.feishu.AvatarDTO;
import cn.genn.ai.hub.app.application.dto.question.*;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.QuestionInfoQuery;
import cn.genn.ai.hub.app.application.query.UserQuestionCountQuery;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.config.FSLoginProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionAnalysisRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionRepositoryImpl;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.contact.v3.model.BatchUserReq;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.BatchUserRespBody;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QuestionInfoService {

    private final QuestionRepositoryImpl questionRepository;
    private final QuestionAnalysisRepositoryImpl analysisRepository;
    private final QuestionInfoRepositoryImpl questionInfoRepository;
    private final QuestionInfoAssembler questionInfoAssembler;
    private final GeneralAssembler generalAssembler;
    private final IUpmUserService upmUserService;
    private final FeishuClientService feishuClientService;
    private final GennAIHubProperties gennAIHubProperties;





    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return QuestionInfoDTO分页对象
     */
    public List<QuestionInfoDTO> infos(QuestionInfoQuery query) {
        List<QuestionInfoDTO> infos = questionRepository.infos(query);
        processInfos(infos);
        return infos;
    }

    private void processInfos(List<QuestionInfoDTO> infos) {
        List<QuestionInfoDTO> fillUser = Lists.newArrayList();
        List<Long> userIds = Lists.newArrayList();
        Map<Long, List<QuestionInfoDTO>> groupedByTenant = new HashMap<>();

        for (QuestionInfoDTO info : infos) {
            if (info.getUserId() != null && info.getUserId() > 0L) {
                userIds.add(info.getUserId());
                fillUser.add(info);
            }
            if (StringUtils.isNotBlank(info.getOpenId())) {
                groupedByTenant.computeIfAbsent(info.getTenantId(), k -> new ArrayList<>()).add(info);
            }
        }
        if (CollUtil.isNotEmpty(userIds)) {
            UpmUserQuery query = new UpmUserQuery();
            query.setUserIdList(userIds);
            List<UpmUserDTO> upmUser = upmUserService.conditionList(query);
            if (CollUtil.isNotEmpty(upmUser)) {
                Map<Long, UpmUserDTO> userDTOMap = upmUser.stream().collect(Collectors.toMap(UpmUserDTO::getId, Function.identity()));
                for (QuestionInfoDTO info : fillUser) {
                    UpmUserDTO upmUserDTO = userDTOMap.get(info.getUserId());
                    if (upmUserDTO != null) {
                        info.setUserInfo(upmUserDTO);
                        info.setAvatar(AvatarDTO.builder().avatarOrigin(upmUserDTO.getAvatar()).build());
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(groupedByTenant)) {
            groupedByTenant.forEach((tenantId, infoDTOS) -> {
                FSLoginProperties fsLoginConfig = gennAIHubProperties.getFSLoginConfig(tenantId);
                Client client = feishuClientService.getClient(fsLoginConfig.getAppId());
                Set<String> openIds = infoDTOS.stream().map(QuestionInfoDTO::getOpenId).collect(Collectors.toSet());
                BatchUserReq userReq = BatchUserReq.newBuilder().userIds(openIds.toArray(String[]::new)).userIdType("open_id").departmentIdType("open_department_id").build();
                try {
                    BatchUserResp userResp = client.contact().user().batch(userReq);
                    BatchUserRespBody respData = userResp.getData();
                    Map<String, User> userMap = Arrays.stream(respData.getItems()).collect(Collectors.toMap(User::getOpenId, Function.identity()));
                    for (QuestionInfoDTO dto : infoDTOS) {
                        User user = userMap.get(dto.getOpenId());
                        if (user != null) {
                            dto.setAvatar(generalAssembler.convertFSAvatar(user.getAvatar()));
                        }
                    }
                } catch (Exception ignore) {
                }
            });
        }
    }


    @Transactional
    public Long add(QuestionInfoCommand command) {
        return questionRepository.add(command);
    }

    public HighFreDTO getListByAppIdAndSource(String appId, String source) {
        List<String> labels = analysisRepository.queryTypes(appId);
        List<HighFreAnalysisInfo> anlList = questionRepository.getListByAppIdAndSource(appId, source);
        if (CollUtil.isEmpty(anlList)) {
            return new HighFreDTO();
        }
        List<QuestionAnalysisDTO> oldAnlDTO = analysisRepository.queryByAppId(appId, source);
        List<Long> oldRelIds = Lists.newArrayList();
        for (QuestionAnalysisDTO dto : oldAnlDTO) {
            oldRelIds.addAll(dto.getRelInfoId());
        }
        List<HighFreAnalysisInfo> needProcess = anlList.stream().filter(anl -> !oldRelIds.contains(anl.getId())).toList();
        if (CollUtil.isEmpty(needProcess)) {
            return new HighFreDTO();
        }
        return HighFreDTO
            .builder()
            .questions(needProcess)
            .labels(labels)
            .build();
    }

    /**
     * 获取用户统计和问题详情
     */
    public List<UserQuestionCountDTO> getTopUsersWithDetails(UserQuestionCountQuery query) {
        if (query.getSources() != null && query.getSources().isEmpty()) {
            return Lists.newArrayList();
        }
        // 1. 参数验证
        validateQuery(query);

        // 2. 获取用户统计和问题详情
        List<UserQuestionCountDTO> result = questionInfoRepository.getTopUsersWithDetails(query);

        // 3. 补充用户详细信息
        enrichUserInfo(result);

        return result;
    }

    /**
     * 获取部门top10统计和人员详情
     */
    public List<DepartmentQuestionCountDTO> getDepartmentWithDetails(DepartmentQuestionCountQuery query) {
        if (query.getSources() != null && query.getSources().isEmpty()) {
            return Lists.newArrayList();
        }
        // 1. 参数验证
        validateQuery(query);

        // 2. 获取部门统计和人员详情
        List<DepartmentQuestionCountDTO> result = questionInfoRepository.getDepartmentWithDetails(query);

        // 3. 补充用户详细信息
        enrichDepartmentUserInfo(result);

        return result;
    }

    /**
     * 验证查询参数
     */
    private void validateQuery(UserQuestionCountQuery query) {
        if (ObjUtil.isNotNull( query.getStartTime()) && ObjUtil.isNotNull(query.getEndTime()) && query.getStartTime().isAfter(query.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }

    /**
     * 补充用户详细信息
     */
    private void enrichUserInfo(List<UserQuestionCountDTO> questionCountDTOS) {
        if (CollUtil.isEmpty(questionCountDTOS)) {
            return;
        }

        // 获取所有用户ID
        List<Long> userIds = questionCountDTOS.stream()
            .map(UserQuestionCountDTO::getUserId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        // 批量查询用户信息
        UpmUserQuery upmUserQuery = new UpmUserQuery();
        upmUserQuery.setUserIdList(userIds);
        List<UpmUserDTO> upmUsers = upmUserService.conditionList(upmUserQuery);

        if (CollUtil.isNotEmpty(upmUsers)) {
            Map<Long, UpmUserDTO> userMap = upmUsers.stream()
                .collect(Collectors.toMap(UpmUserDTO::getId, Function.identity()));

            // 补充用户信息
            for (UserQuestionCountDTO dto : questionCountDTOS) {
                UpmUserDTO upmUser = userMap.get(dto.getUserId());
                UserInfoDTO userInfoDTO = questionInfoAssembler.upmUser2UserInfo(upmUser);
                if(ObjUtil.isNotNull(userInfoDTO)){
                    dto.setUserInfo(userInfoDTO);
                    dto.getQuestionDetails().forEach(detail -> detail.setUserInfo(userInfoDTO));
                }
            }
        }
    }

    /**
     * 验证查询参数
     */
    private void validateQuery(DepartmentQuestionCountQuery query) {
        if (ObjUtil.isNotNull( query.getStartTime()) && ObjUtil.isNotNull(query.getEndTime()) && query.getStartTime().isAfter(query.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }

    /**
     * 补充用户详细信息
     */
    private void enrichDepartmentUserInfo(List<DepartmentQuestionCountDTO> departments) {
        if (CollUtil.isEmpty(departments)) {
            return;
        }

        // 获取所有用户ID
        List<Long> userIds = departments.stream()
            .flatMap(dept -> dept.getUsers().stream())
            .map(DepartmentUserDTO::getUserId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        // 批量查询用户信息
        UpmUserQuery upmUserQuery = new UpmUserQuery();
        upmUserQuery.setUserIdList(userIds);
        List<UpmUserDTO> upmUsers = upmUserService.conditionList(upmUserQuery);

        if (CollUtil.isNotEmpty(upmUsers)) {
            Map<Long, UpmUserDTO> userMap = upmUsers.stream()
                .collect(Collectors.toMap(UpmUserDTO::getId, Function.identity()));

            // 补充用户信息
            for (DepartmentQuestionCountDTO dept : departments) {
                for (DepartmentUserDTO user : dept.getUsers()) {
                    UpmUserDTO upmUser = userMap.get(user.getUserId());
                    UserInfoDTO userInfoDTO = questionInfoAssembler.upmUser2UserInfo(upmUser);
                    if(ObjUtil.isNotNull(userInfoDTO)){
                        user.setUserInfo(userInfoDTO);
                    }
                }
            }
        }
    }
}

