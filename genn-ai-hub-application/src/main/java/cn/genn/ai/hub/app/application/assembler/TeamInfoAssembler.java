package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.TeamInfoCreateCommand;
import cn.genn.ai.hub.app.application.command.TeamInfoUpdateCommand;
import cn.genn.ai.hub.app.application.dto.TeamInfoDTO;
import cn.genn.ai.hub.app.application.query.TeamInfoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamInfoPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamInfoAssembler extends QueryAssembler<TeamInfoQuery, TeamInfoPO, TeamInfoDTO>{

    TeamInfoAssembler INSTANCE = Mappers.getMapper(TeamInfoAssembler.class);

    /**
     * 创建命令转PO
     *
     * @param command 创建命令
     * @return TeamInfoPO
     */
    TeamInfoPO command2PO(TeamInfoCreateCommand command);

    /**
     * 更新命令转PO
     *
     * @param command 更新命令
     * @return TeamInfoPO
     */
    TeamInfoPO command2PO(TeamInfoUpdateCommand command);

}

