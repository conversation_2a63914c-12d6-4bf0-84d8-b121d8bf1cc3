package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum QaPairStatusEnum {

    NOT_CHECK(0, "未校验"),
    NOT_ACCEPT(1, "未采纳"),
    ACCEPTED(2, "已采纳"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;

    QaPairStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

}

