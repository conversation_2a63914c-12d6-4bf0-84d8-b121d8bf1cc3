package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * 更新智能体命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentInfoUpdateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "智能体ID")
    @NotNull(message = "智能体ID不能为空")
    private Long id;

    @Schema(description = "团队id，如果为空，代表是个人空间创建")
    private Long teamId;

    @Schema(description = "mongo对应的工作流id")
    private String workflowId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "智能体类型，chat：CHAT-对话智能体, workflow：WORKFLOW-工作流智能体, tool_workflow：TOOL_WORKFLOW-工作流工具, tool_http：TOOL_HTTP-HTTP工具")
    private AgentTypeEnum agentType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "额外配置")
    private Map<String, Object> extraConfig;

    @Schema(description = "版本号")
    private String version;
}
