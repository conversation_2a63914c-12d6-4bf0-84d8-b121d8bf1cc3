package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.agent;

import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.application.query.WorkflowAuthCommonQuery;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentChannelMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParamResolver;
import cn.genn.core.exception.BaseException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AgentGetByWorkflowIdResourceAuthParamResolver implements AuthParamResolver {

    private final AgentInfoRepositoryImpl agentInfoRepository;
    private final AgentChannelMapper agentChannelMapper;

    @Override
    public List<AuthParam> authParams(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        WorkflowAuthCommonQuery authCommonQuery = (WorkflowAuthCommonQuery) interceptor.getParamsMap().get("query");
        String workflowId = authCommonQuery.getWorkflowId();
        AgentInfoDTO agentInfo = agentInfoRepository.getAgentByWorkflowId(workflowId);
        switch (uniqueId){
            case "user":
                return List.of(AuthParam.builder()
                    .subjectType(SubjectType.USER)
                    .resourceType(ResourceType.AGENT)
                    .actionType(new ActionType[]{ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT})
                    .resourceKey(agentInfo.getId().toString())
                    .build());
            case "team":
                Long teamId = agentInfo.getTeamId();
                if (teamId == null) {
                    return List.of();
                }
                return List.of(AuthParam.builder()
                    .subjectType(SubjectType.USER)
                    .resourceType(ResourceType.TEAM)
                    .actionType(new ActionType[]{ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT})
                    .resourceKey(teamId.toString())
                    .build());
            case "tenant":
                AgentChannelPO agentChannelPO = agentChannelMapper.selectOne(Wrappers.<AgentChannelPO>lambdaQuery()
                    .eq(AgentChannelPO::getWorkflowId, workflowId));
                if (agentChannelPO != null) {
                    return List.of();
                }
                throw new BaseException(MessageCode.RESOURCE_NOT_EXIST);
            default: throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        }

    }
}
