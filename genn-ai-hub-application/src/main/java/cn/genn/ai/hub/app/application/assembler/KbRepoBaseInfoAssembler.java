package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoBaseInfoDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCatDetailDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoEditCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoBaseInfoAssembler extends QueryAssembler<KbRepoBaseInfoQuery, KbRepoBaseInfoPO, KbRepoBaseInfoDTO>{

    KbRepoBaseInfoAssembler INSTANCE = Mappers.getMapper(KbRepoBaseInfoAssembler.class);

    @Mapping(target = "repoId", source = "id")
    KbRepoCatDetailDTO KbRepoBaseInfoDTO2KbRepoCatDetailDTO(KbRepoBaseInfoDTO baseInfoDTO);

    KbRepoBaseInfoPO convertPO(KbRepoBaseInfoCreateCommand command);

    KbRepoBaseInfoPO convertPO(KbRepoBaseInfoEditCommand command);
}

