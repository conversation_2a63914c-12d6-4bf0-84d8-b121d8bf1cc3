package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.dto.KbRepoDataIndexDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataIndexQuery;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataIndexRepositoryImpl;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.ai.hub.app.application.assembler.KbRepoDataIndexAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoDataIndexMapper;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoDataIndexQueryService {

    private final KbRepoDataIndexMapper mapper;
    private final KbRepoDataIndexAssembler assembler;
    private final KbRepDataIndexRepositoryImpl repository;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoDataIndexDTO分页对象
     */
    public PageResultDTO<KbRepoDataIndexDTO> page(KbRepoDataIndexQuery query) {
        KbRepoDataIndexPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoDataIndexDTO
     */
    public KbRepoDataIndexDTO get(IdQuery query) {
        return assembler.PO2DTO(mapper.selectById(query.getId()));
    }

    /**
     * 根据分块key查询索引列表
     *
     * @param query
     * @return
     */
    public List<KbRepoDataIndexDTO> getByDataKey(KbRepoDataIndexQuery query) {
        List<KbRepoDataIndexPO> poList = repository.selectDataIndexOfDataKey(query.getRepoId(), query.getCollectionId(), query.getDataKey());
        return assembler.PO2DTO(poList);
    }

    /**
     * 获取数据集下的所有索引
     *
     * @param repoId
     * @param collectionId
     * @return
     */
    public List<KbRepoDataIndexDTO> getByCollectionId(Long repoId, Long collectionId) {
        QueryWrapper<KbRepoDataIndexPO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
            .eq(KbRepoDataIndexPO::getRepoId, repoId)
            .eq(KbRepoDataIndexPO::getCollectionId, collectionId)
            .eq(KbRepoDataIndexPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return assembler.PO2DTO(mapper.selectList(wrapper));
    }
}

