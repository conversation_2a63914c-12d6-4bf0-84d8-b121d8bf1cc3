package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 创建标签关联命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagsRefCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "标签id")
    @NotNull(message = "标签id不能为空")
    private Long tagId;

    @Schema(description = "资源key")
    @NotBlank(message = "资源key不能为空")
    private String resourceKey;
}
