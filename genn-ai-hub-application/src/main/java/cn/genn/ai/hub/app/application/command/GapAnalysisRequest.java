package cn.genn.ai.hub.app.application.command;


import cn.genn.ai.hub.app.application.dto.question.MissingPoints;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * KnowledgeGapAnalysis操作对象
 *
 * <AUTHOR>
 */
@Data
public class GapAnalysisRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分析结果")
    private String result;

    @Schema(description = "问题id")
    private String questionId;

    @Data
    public static class GapAnl {
        /**
         * 缺失内容
         */
        private List<MissingPoints> missingPoints;

        /**
         * 分析结果
         */
        private String summary;

    }

}

