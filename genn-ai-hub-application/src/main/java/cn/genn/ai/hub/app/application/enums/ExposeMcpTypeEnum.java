package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ExposeMcpTypeEnum {

    SSE("sse", "服务器发送事件"),
    STREAMABLE_HTTP("streamableHttp", "可流式HTTP"),
    ALL("all", "所有"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    ExposeMcpTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}

