package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.CollaboratorDTO;
import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import cn.genn.ai.hub.app.application.query.AgentInfoQuery;
import cn.genn.ai.hub.app.application.query.McpAgentQuery;
import cn.genn.ai.hub.app.application.query.TagsQuery;
import cn.genn.ai.hub.app.domain.agent.model.valobj.AgentExtraConfig;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.OpenApiMcpConfig;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentInfoPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.GennUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AgentInfoQueryService {

    private final AgentInfoRepositoryImpl agentInfoRepository;
    private final AgentInfoMapper agentInfoMapper;
    private final TagsQueryService tagsQueryService;
    private final TeamMemberQueryService teamMemberQueryService;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return AgentInfoDTO分页对象
     */
    public PageResultDTO<AgentInfoDTO> page(AgentInfoQuery query) {
        // 处理标签相关的查询
        Set<Long> agentIds = null;

        // 如果有标签相关的查询条件
        if (CollUtil.isNotEmpty(query.getTagIds()) || CharSequenceUtil.isNotEmpty(query.getTagName())) {
            // 将AgentInfoQuery转换为TagsQuery
            TagsQuery tagsQuery = convertToTagsQuery(query);

            // 使用标签查询服务的方法获取资源key列表
            Set<String> resourceKeys = tagsQueryService.getResourceKeysByTagConditions(tagsQuery);

            if (resourceKeys.isEmpty()) {
                // 如果没有找到任何资源key，返回空结果
                return PageResultDTO.empty();
            }

            agentIds = resourceKeys.stream()
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        }

        PageResultDTO<AgentInfoDTO> pageResultDTO = agentInfoRepository.pageByIds(query, agentIds);
        if (GennUtils.pageIsEmpty(pageResultDTO)) {
            return pageResultDTO;
        }
        // 填充标签信息
        fillTagsInfo(pageResultDTO.getList());
        // 填充协作者信息
        fillCollaborators(pageResultDTO.getList());
        return pageResultDTO;
    }


    /**
     * 根据id查询
     *
     * @return AgentInfoDTO
     */
    public AgentInfoDTO get(Long id) {
        AgentInfoDTO agent = agentInfoRepository.getAgent(id);
        fillTagsInfo(Collections.singletonList(agent));
        fillCollaborators(Collections.singletonList(agent));
        return agent;
    }


    @IgnoreTenant
    public OpenApiMcpConfig loadByWorkflowId(McpAgentQuery mcpAgentQuery) {
        AgentInfoPO agentInfoPO = agentInfoMapper.selectOne(Wrappers.lambdaQuery(AgentInfoPO.class)
            .eq(AgentInfoPO::getWorkflowId, mcpAgentQuery.getMcpWorkflowId())
            .eq(AgentInfoPO::getAgentType, AgentTypeEnum.TOOL_MCQ)
            .eq(AgentInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED));
        if (agentInfoPO == null) {
            return null;
        }
        Map<String, Object> extraConfig = agentInfoPO.getExtraConfig();
        if (extraConfig == null) {
            return null;
        }
        AgentExtraConfig agentExtraConfig = JsonUtils.parse(JsonUtils.toJson(extraConfig), AgentExtraConfig.class);
        if (agentExtraConfig == null || agentExtraConfig.isNotOpenapi()) {
            return null;
        }
        return agentExtraConfig.getOpenapi();
    }

    /**
     * 根据workflowId查询
     */
    public AgentInfoDTO getByWorkflowId(String workflowId) {
        AgentInfoDTO agent = agentInfoRepository.getAgentByWorkflowId(workflowId);
        fillTagsInfo(Collections.singletonList(agent));
        fillCollaborators(Collections.singletonList(agent));
        return agent;
    }

    /**
     * 列出智能体所有协作者
     */
    public List<CollaboratorDTO> listCollaborator(IdQuery query) {
        AgentInfoDTO agentInfoDTO = agentInfoRepository.getAgent(query.getId());
        return teamMemberQueryService.getCollaborators(
            agentInfoDTO.getId(),
            agentInfoDTO.getTeamId(),
            agentInfoDTO.getCreateUserId(),
            ResourceType.AGENT
        );
    }

    /**
     * 将AgentInfoQuery转换为TagsQuery
     *
     * @param query AgentInfoQuery查询条件
     * @return TagsQuery查询条件
     */
    private TagsQuery convertToTagsQuery(AgentInfoQuery query) {
        TagsQuery tagsQuery = new TagsQuery();
        tagsQuery.setTagIds(query.getTagIds());
        tagsQuery.setName(query.getTagName());
        tagsQuery.setTagType(AgentTypeEnum.convertToTagType(query.getAgentType()));
        tagsQuery.setTeamId(query.getTeamId());
        return tagsQuery;
    }


    /**
     * 填充标签信息
     *
     * @param agentInfoDTOList 智能体DTO列表
     */
    private void fillTagsInfo(List<AgentInfoDTO> agentInfoDTOList) {
        if (CollUtil.isEmpty(agentInfoDTOList)) {
            return;
        }
        AgentInfoDTO peekAgent = agentInfoDTOList.getFirst();
        tagsQueryService.fillTagsInfo(
            agentInfoDTOList,
            AgentTypeEnum.convertToTagType(peekAgent.getAgentType()),
            peekAgent.getTeamId(),
            AgentInfoDTO::getId,
            AgentInfoDTO::setTagList
        );
    }

    /**
     * 填充协作者信息
     *
     * @param agentInfoDTOList 智能体DTO列表
     */
    private void fillCollaborators(List<AgentInfoDTO> agentInfoDTOList) {
        teamMemberQueryService.fillCollaborators(
            agentInfoDTOList,
            AgentInfoDTO::getId,
            AgentInfoDTO::getTeamId,
            AgentInfoDTO::getCreateUserId,
            ResourceType.AGENT,
            AgentInfoDTO::setCollaborators
        );
    }

}

