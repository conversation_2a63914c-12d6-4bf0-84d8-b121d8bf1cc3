package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 资源移除邀请协作邀请命令
 *
 * <AUTHOR>
 */
@Data
public class ResourceRemoveInviteCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "移除用户ID")
    @NotNull(message = "移除ID不能为空")
    private Long userId;

}
