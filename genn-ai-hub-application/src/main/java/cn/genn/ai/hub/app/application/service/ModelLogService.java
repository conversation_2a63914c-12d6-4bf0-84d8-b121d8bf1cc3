package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.dto.request.ModelLogQuery;
import cn.genn.ai.hub.app.application.dto.response.ModelLogDTO;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ModelLogRepositoryImpl;
import cn.genn.core.model.page.PageResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模型日志
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelLogService {

    @Resource
    private ModelLogRepositoryImpl modelLogRepository;

    public PageResultDTO<ModelLogDTO> logPage(ModelLogQuery query) {

        return modelLogRepository.logPage(query);
    }
}

