package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.PromptAssembler;
import cn.genn.ai.hub.app.application.dto.PromptDTO;
import cn.genn.ai.hub.app.application.dto.PromptVersionDTO;
import cn.genn.ai.hub.app.application.query.PromptQuery;
import cn.genn.ai.hub.app.domain.prompt.model.entity.Prompt;
import cn.genn.ai.hub.app.domain.prompt.model.entity.PromptVersion;
import cn.genn.ai.hub.app.domain.prompt.repository.PromptRepository;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptVersionPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 提示词查询服务
 * @date 2025-05-15
 */
@Service
@RequiredArgsConstructor
public class PromptQueryService {

    private final PromptRepository promptRepository;

    private final PromptAssembler promptAssembler;

    /**
     * 获取指定提示词的最新已发布版本。
     */
    public PromptDTO getLatestPublishedVersion(Long promptId) {
        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new BusinessException("未找到ID为 " + promptId + " 的提示词"));
        Optional<PromptVersion> publishedVersion = prompt.getPublishedVersion();
        return publishedVersion.map(promptVersion -> promptAssembler.toPromptDTOWithVersion(prompt, promptVersion)).orElse(null);
    }

    /**
     * 获取指定提示词的草稿版本。
     */
    public PromptDTO getDraftPromptVersion(Long promptId) {
        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new BusinessException("未找到ID为 " + promptId + " 的提示词"));
        Optional<PromptVersion> draftVersion = prompt.getDraftVersion();
        return draftVersion.map(promptVersion -> promptAssembler.toPromptDTOWithVersion(prompt, promptVersion)).orElse(null);
    }

    @IgnoreTenant
    public PageResultDTO<PromptDTO> queryPromptPage(PromptQuery query) {
        Page<PromptPO> resultPage = promptRepository.findPage(query);
        List<PromptDTO> promptDTO = promptAssembler.toPromptDTO(resultPage.getRecords());
        promptDTO.forEach(prompt -> {
            // 处理提示词的版本信息
            PromptVersionPO promptVersionPO = promptRepository.findVersionByPromptId(prompt.getId(), prompt.getLatestVersion());
            PromptVersionDTO promptVersionDTO = promptAssembler.toPromptVersionDTO(promptVersionPO);
            promptVersionDTO.setTitle(prompt.getTitle());
            prompt.setPromptVersion(promptVersionDTO);
        });
        return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), resultPage.getTotal(), promptDTO);
    }

    public List<PromptVersionDTO> getAllVersion(Long id) {
        Prompt prompt = promptRepository.findById(id)
            .orElseThrow(() -> new BusinessException("未找到ID为 " + id + " 的提示词"));
        List<PromptVersion> allVersions = prompt.getAllVersions();
        return allVersions.stream()
            .map(version -> promptAssembler.toPromptVersionDTO(prompt, version))
            .sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())) // 降序排序
            .toList();
    }

    @IgnoreTenant
    public List<PromptDTO> querySystemPromptList() {
        List<PromptPO> systemPrompts = promptRepository.findSystemPrompts();
        List<PromptDTO> promptDTO = promptAssembler.toPromptDTO(systemPrompts);
        promptDTO.forEach(prompt -> {
            // 处理提示词的版本信息
            PromptVersionPO promptVersionPO = promptRepository.findVersionByPromptId(prompt.getId(), prompt.getLatestVersion());
            PromptVersionDTO promptVersionDTO = promptAssembler.toPromptVersionDTO(promptVersionPO);
            promptVersionDTO.setTitle(prompt.getTitle());
            prompt.setPromptVersion(promptVersionDTO);
        });
        return promptDTO;
    }
}
