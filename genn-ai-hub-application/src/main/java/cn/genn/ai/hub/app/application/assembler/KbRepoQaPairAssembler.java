package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.KbRepoQaPairEditCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoQaPairDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoQaPairExcelImportDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoQaPairQuery;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoQaPairAssembler extends QueryAssembler<KbRepoQaPairQuery, KbRepoQaPairPO, KbRepoQaPairDTO>{

    KbRepoQaPairAssembler INSTANCE = Mappers.getMapper(KbRepoQaPairAssembler.class);

    KbRepoQaPairPO kbRepoQaPairEditCommand2KbRepoQaPairPO(KbRepoQaPairEditCommand command);

    List<KbRepoQaPairPO> kbRepoQaPairExcelImportDTO2PO(List<KbRepoQaPairExcelImportDTO> dataList);

    @Mapping(target = "similarQuestions", source = "similarQuestions", qualifiedByName = "strToList")
    KbRepoQaPairPO kbRepoQaPairExcelImportDTO2PO(KbRepoQaPairExcelImportDTO dto);

    /**
     * 将换行分隔的字符串转换为List<String>
     * @param str 换行分隔的字符串
     * @return List<String>
     */
    @Named("strToList")
    default List<String> strToList(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        return Arrays.stream(str.split("\\n"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
}

