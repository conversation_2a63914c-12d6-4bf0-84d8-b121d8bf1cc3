package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoDataDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoDataIndexDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataAndIndexUpdateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataChunkRequestCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataIndexUpdateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataQuery;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoDataAssembler extends QueryAssembler<KbRepoDataQuery, KbRepoDataPO, KbRepoDataDTO>{

    KbRepoDataAssembler INSTANCE = Mappers.getMapper(KbRepoDataAssembler.class);

    KbRepoDataDTO kbRepoDataChunkRequestCommand2DTO(KbRepoDataChunkRequestCommand command);

    KbRepoDataDTO kbRepoDataAndIndexUpdateCommand2DTO(KbRepoDataAndIndexUpdateCommand command);

    KbRepoDataPO DTO2PO(KbRepoDataDTO dto);

    List<KbRepoDataIndexDTO> kbRepoDataIndexUpdateCommand2DTO(List<KbRepoDataIndexUpdateCommand> indexInfoList);
}

