package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.PluginInvokeCommand;
import cn.genn.ai.hub.app.application.dto.PluginDTO;
import cn.genn.ai.hub.core.plugin.PluginRequest;
import cn.genn.ai.hub.plugin.common.repository.po.PluginCategoryPO;
import cn.genn.ai.hub.plugin.common.repository.po.PluginDefinitionPO;
import cn.genn.ai.hub.plugin.common.repository.po.PluginVersionDefinitionPO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Map;

/**
 * 插件转换器
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PluginAssembler {

    PluginAssembler INSTANCE = Mappers.getMapper(PluginAssembler.class);

    /**
     * 将插件调用命令转换为插件请求
     * @param command 插件调用命令
     * @return 插件请求
     */
    @Mapping(target = "requestId", expression = "java(cn.hutool.core.util.IdUtil.simpleUUID())")
    PluginRequest toPluginRequest(PluginInvokeCommand command);

    /**
     * 将插件定义PO转换为插件DTO
     * @param po 插件定义PO
     * @return 插件DTO
     */
    PluginDTO toPluginDTO(PluginDefinitionPO po);

    /**
     * 将插件定义PO和版本PO转换为插件DTO
     * @param po 插件定义PO
     * @param versionPO 版本PO
     * @return 插件DTO
     */
    @Mapping(target = "id", source = "po.id")
    @Mapping(target = "pluginKey", source = "po.pluginKey")
    @Mapping(target = "pluginName", source = "po.pluginName")
    @Mapping(target = "description", source = "po.description")
    @Mapping(target = "pluginType", source = "po.pluginType")
    @Mapping(target = "latestVersion", source = "po.latestVersion")
    @Mapping(target = "status", source = "po.status")
    @Mapping(target = "callMethod", source = "versionPO.callMethod")
    @Mapping(target = "pluginConfig", source = "versionPO.pluginConfig")
    @Mapping(target = "localCallConfig", source = "versionPO.localCallConfig")
    @Mapping(target = "remoteCallConfig", source = "versionPO.remoteCallConfig")
    PluginDTO toPluginDTO(PluginDefinitionPO po, PluginVersionDefinitionPO versionPO);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<PluginDTO> toPageResult(IPage<PluginDefinitionPO> poPage);

    /**
     * 将分页结果和版本信息转换为分页DTO
     * @param poPage 分页结果
     * @param versionMap 版本信息映射
     * @return 分页DTO
     */
    default PageResultDTO<PluginDTO> toPageResult(IPage<PluginDefinitionPO> poPage, Map<String, PluginVersionDefinitionPO> versionMap) {
        PageResultDTO<PluginDTO> pageResult = new PageResultDTO<>();
        pageResult.setPageNo((int) poPage.getCurrent());
        pageResult.setPageSize((int) poPage.getSize());
        pageResult.setTotal(poPage.getTotal());
        pageResult.setTotalPages((int) poPage.getPages());
        pageResult.setList(poPage.getRecords().stream()
            .map(po -> {
                PluginVersionDefinitionPO versionPO = versionMap.get(po.getPluginKey() + ":" + po.getLatestVersion());
                return toPluginDTO(po, versionPO);
            })
            .toList());
        return pageResult;
    }

    /**
     * 获取目录名称
     */
    default String getCategoryName(Long catId, Map<Long, PluginCategoryPO> categoryMap) {
        if (catId == null || catId == 0L || categoryMap == null) {
            return null;
        }
        PluginCategoryPO category = categoryMap.get(catId);
        return category != null ? category.getCatName() : null;
    }
}
