package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.AgentInfoUpdateCommand;
import cn.genn.ai.hub.app.application.command.ResourceInviteCommand;
import cn.genn.ai.hub.app.application.command.ResourceRemoveInviteCommand;
import cn.genn.ai.hub.app.application.command.TagsAssignCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentInfoMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AgentTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver{

    private final AgentInfoMapper agentInfoMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long agentId = switch (uniqueId) {
            case "update" -> ((AgentInfoUpdateCommand) interceptor.getParamsMap().get("command")).getId();
            case "lock", "delete" -> ((IdCommand) interceptor.getParamsMap().get("command")).getId();
            case "assignTags" -> ((TagsAssignCommand) interceptor.getParamsMap().get("command")).getId();
            case "inviteCollaborator" -> ((ResourceInviteCommand) interceptor.getParamsMap().get("command")).getId();
            case "removeCollaborator" ->
                ((ResourceRemoveInviteCommand) interceptor.getParamsMap().get("command")).getId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return agentInfoMapper.selectById(agentId).getTeamId();
    }
}
