package cn.genn.ai.hub.app.infrastructure.utils.auth.complex;

import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 鉴权结果封装
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AuthResult implements Serializable {

    private static final long serialVersionUID = -1429842319621029783L;

    private boolean pass;

    private SubjectType subjectType;

    private ResourceType resourceType;

    private ActionType[] actionType;

    private String resourceKey;

    private String resourceName;

    public AuthResult(SubjectType subjectType, ResourceType resourceType, ActionType[] actionType, String resourceKey) {
        this.subjectType = subjectType;
        this.resourceType = resourceType;
        this.actionType = actionType;
        this.resourceKey = resourceKey;
    }

    public static AuthResult ofFail(SubjectType subjectType, ResourceType resourceType, ActionType[] actionType, String resourceId) {
        AuthResult authResult = new AuthResult(subjectType, resourceType, actionType, resourceId);
        authResult.setPass(false);
        return authResult;
    }

    public static List<AuthResult> ofFailColl(SubjectType subjectType, ResourceType resourceType, ActionType[] actionType, String resourceId) {
        return CollUtil.newArrayList(AuthResult.ofFail(subjectType, resourceType, actionType, resourceId));
    }

    public static AuthResult ofPass(SubjectType subjectType, ResourceType resourceType, ActionType[] actionType, String resourceId) {
        AuthResult authResult = new AuthResult(subjectType, resourceType, actionType, resourceId);
        authResult.setPass(true);
        return authResult;
    }

    public static List<AuthResult> ofPassColl(SubjectType subjectType, ResourceType resourceType, ActionType[] actionType, String resourceId) {
        return CollUtil.newArrayList(AuthResult.ofPass(subjectType, resourceType, actionType, resourceId));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AuthResult)) return false;
        AuthResult that = (AuthResult) o;
        return subjectType == that.subjectType && resourceType == that.resourceType && actionType == that.actionType && Objects.equals(resourceKey, that.resourceKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(resourceType, actionType, resourceKey);
    }
}
