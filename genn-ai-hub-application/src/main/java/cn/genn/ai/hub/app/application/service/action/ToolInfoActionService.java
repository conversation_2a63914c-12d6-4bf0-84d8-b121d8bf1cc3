package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.enums.ToolSourceEnum;
import cn.genn.ai.hub.app.infrastructure.constant.Constants;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ToolInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ToolInfoPO;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ToolInfoActionService {

    private final ToolInfoMapper toolInfoMapper;
    private final TagsActionService tagsActionService;

    /**
     * 发布 MCP 分组。
     * @param groupKey 分组的唯一标识。
     * @param name 分组的名称。
     * @return 如果发布成功则返回 true，否则返回 false。
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishMcpGroup(String groupKey, String name, String description, String avatar, List<Long> tagIds) {
        String toolKey = Constants.MCP_COMMON_TOOL_PREFIX + groupKey;
        ToolInfoPO toolInfoPO = toolInfoMapper.selectOne(Wrappers.<ToolInfoPO>lambdaQuery()
            .eq(ToolInfoPO::getToolKey, toolKey));
        if (toolInfoPO == null) {
           toolInfoPO = new ToolInfoPO();
            toolInfoPO.setToolKey(toolKey);
            toolInfoPO.setToolSource(ToolSourceEnum.MCP);
            toolInfoPO.setAvatar(avatar);
            toolInfoPO.setName(name);
            toolInfoPO.setIntro(description);
            toolInfoMapper.insert(toolInfoPO);
            if (CollUtil.isNotEmpty(tagIds)) {
                tagsActionService.updateTagRefByResource(String.valueOf(toolInfoPO.getId()), TagTypeEnum.GENERAL_TOOL, null, tagIds);
            }
        }else {
            toolInfoPO.setDeleted(DeletedTypeEnum.NOT_DELETED);
            toolInfoPO.setAvatar(avatar);
            toolInfoPO.setName(name);
            toolInfoPO.setIntro(description);
            toolInfoMapper.updateById(toolInfoPO);
            if (CollUtil.isNotEmpty(tagIds)) {
                tagsActionService.updateTagRefByResource(String.valueOf(toolInfoPO.getId()), TagTypeEnum.GENERAL_TOOL, null, tagIds);
            }else {
                tagsActionService.deleteTagRefByResource(String.valueOf(toolInfoPO.getId()), TagTypeEnum.GENERAL_TOOL, null);
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean unpublishMcpGroup(String groupKey) {
        String toolKey = Constants.MCP_COMMON_TOOL_PREFIX + groupKey;
        ToolInfoPO toolInfoPO = toolInfoMapper.selectOne(Wrappers.<ToolInfoPO>lambdaQuery()
            .eq(ToolInfoPO::getToolKey, toolKey));
        if (toolInfoPO == null) {
            return true;
        }
        toolInfoPO.setDeleted(DeletedTypeEnum.DELETED);
        return toolInfoMapper.updateById(toolInfoPO) > 0;
    }
}
