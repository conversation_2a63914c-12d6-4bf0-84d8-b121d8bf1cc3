package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.core.model.KVStruct;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 团队转让命令
 *
 * <AUTHOR>
 */
@Data
public class ResourceTransferCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "资源id")
    @NotNull(message = "转移资源id不能为空")
    private List<KVStruct<String, ResourceType>> resources;

    @Schema(description = "团队ID")
    @NotNull(message = "团队ID不能为空")
    private Long teamId;

}
