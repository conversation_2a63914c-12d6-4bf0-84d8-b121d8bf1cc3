package cn.genn.ai.hub.app.infrastructure.config.auth;


import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.expression.AuthExpressionResolver;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.expression.DefaultAuthExpressionResolver;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuthExpression {

    /**
     * 是否需要鉴权
     */
    boolean enabled() default true;

    /**
     * 过滤数据的数据库字段名
     */
    String dbFieldName() default "id";

    /**
     * 特殊的表达式生成实现类
     */
    Class<? extends AuthExpressionResolver> authExpressionResolver() default DefaultAuthExpressionResolver.class;

    /**
     * 主体类型
     */
    SubjectType subjectType() default SubjectType.USER;

    /**
     * 操作类型
     */
    ActionType[] actionType() default {};

    /**
     * 资源类型
     */
    ResourceType resourceType() default ResourceType.EMPTY;

    String uniqueId() default "";
}
