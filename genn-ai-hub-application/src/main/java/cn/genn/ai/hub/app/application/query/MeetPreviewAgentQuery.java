package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * AgentInfo查询对象
 *
 * <AUTHOR>
 */
@Data
public class MeetPreviewAgentQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库ID")
    private List<Long> repoIds;

    @Schema(description = "倒推天数")
    private Integer minusDays;

}

