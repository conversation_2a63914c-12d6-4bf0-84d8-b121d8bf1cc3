package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.PromptTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 提示词查询
 * @date 2025-05-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PromptQuery extends PageSortQuery {

    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "提示词标题")
    private String title;

    @Schema(description = "提示词类型: 1-系统提示词, 2-用户提示词")
    private PromptTypeEnum promptType;
}
