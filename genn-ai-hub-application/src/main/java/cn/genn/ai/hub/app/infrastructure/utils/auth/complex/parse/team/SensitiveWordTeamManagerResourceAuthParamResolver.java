package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.PromptPublishCommand;
import cn.genn.ai.hub.app.application.command.PromptSaveCommand;
import cn.genn.ai.hub.app.application.command.SensitiveWordSaveCommand;
import cn.genn.ai.hub.app.application.command.TagsAssignCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.SensitiveWordMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 敏感词团队管理资源鉴权
 * @date 2025-05-19
 */
@Component
@RequiredArgsConstructor
public class SensitiveWordTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver {

    private final SensitiveWordMapper sensitiveWordMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long promptId = switch (uniqueId) {
            case "updateSensitiveWord" -> ((SensitiveWordSaveCommand) interceptor.getParamsMap().get("command")).getId();
            case "deleteSensitiveWord" -> ((IdCommand) interceptor.getParamsMap().get("command")).getId();
            case "assignTags" -> ((TagsAssignCommand) interceptor.getParamsMap().get("command")).getId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return sensitiveWordMapper.selectById(promptId).getTeamId();
    }
}
