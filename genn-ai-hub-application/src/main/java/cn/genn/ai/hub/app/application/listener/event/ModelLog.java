package cn.genn.ai.hub.app.application.listener.event;

import cn.genn.core.model.enums.BooleanTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelLog {

    /**
     * 请求唯一标识
     */
    private String traceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 应用唯一标识
     */
    private String appId;

    /**
     * 会话唯一标识
     */
    private String chatId;

    /**
     * 数据唯一标识
     */
    private String dataId;

    /**
     * 响应节点唯一标识
     */
    private String respId;

    /**
     * 渠道唯一ID
     */
    private String channelId;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 真是请求的模型名称
     */
    private String model;

    /**
     * 模型标识
     */
    private String modelKey;

    /**
     * 请求来源：流程实例、插件、业务系统
     */
    private String requestSource;

    /**
     * 请求key,根据来源分为流程id，插件requestId，业务系统标识
     */
    private String requestKey;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 是否成功
     */
    private BooleanTypeEnum success;

    /**
     * 请求时间
     */
    private LocalDateTime requestTime;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 响应体
     */
    @Builder.Default
    private String responseBody = "";

    /**
     * 请求token
     */
    private Integer requestTokenSize = 0;

    /**
     * 响应token
     */
    private Integer responseTokenSize = 0;

    /**
     * 耗时(毫秒)
     */
    private Long costTime;

    /**
     * 异常
     */
    private Throwable throwable;

    /**
     * 错误信息
     */
    private String errorMsg;

}
