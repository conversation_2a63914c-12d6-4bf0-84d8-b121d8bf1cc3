package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoTermMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTermPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbRepoTermAuthParamResolver extends AbstractRepoAuthParamResolver {

    private final KbRepoTermMapper kbRepoTermMapper;
    @Override
    protected Long getRepoIdById(Long id) {
        KbRepoTermPO kbRepoQaPairPO = kbRepoTermMapper.selectById(id);
        return kbRepoQaPairPO.getRepoId();
    }
}
