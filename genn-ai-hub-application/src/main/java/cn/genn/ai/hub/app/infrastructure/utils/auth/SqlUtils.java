package cn.genn.ai.hub.app.infrastructure.utils.auth;

import cn.genn.ai.hub.app.domain.auth.model.valobj.FieldWhereExpression;
import cn.genn.ai.hub.app.domain.auth.model.valobj.OperatorEnum;
import cn.genn.core.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.NullValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.ParenthesedSelect;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectItem;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class SqlUtils {


    public static Statement parse(String sql) {
        try {
            return CCJSqlParserUtil.parse(sql);
        } catch (JSQLParserException e) {
            log.error("sql parse error, sql: {}", sql, e);
            throw new CheckException("sql解析错误");
        }
    }

    public static boolean isSelectStatement(Statement statement) {
        return statement instanceof Select;
    }


    /**
     * 给sql语句添加where条件
     */
    public static String addWhereExpression(String originSql, Expression expression) {
        if (expression == null) {
            return originSql;
        }
        Statement statement = parse(originSql);
        if (!isSelectStatement(statement)) {
            return originSql;
        }
        Select select = (Select) statement;
        PlainSelect plainSelect = select.getPlainSelect();

        Expression oldWhere = plainSelect.getWhere();
        if (oldWhere == null) {
            plainSelect.setWhere(expression);
        } else {
            plainSelect.setWhere(new AndExpression(oldWhere, expression));
        }
        return select.toString();
    }


    public static Expression toWhereExpression(FieldWhereExpression expression) {
        if (expression == null) {
            return null;
        }

        try {
            return parseExpression(expression);
        } catch (Exception e) {
            throw new RuntimeException("转换WHERE表达式失败", e);
        }
    }

    /**
     * 将FieldWhereExpression转换为SQL WHERE子句
     *
     * @param expression FieldWhereExpression对象
     * @return SQL WHERE子句（不包含"WHERE"关键字）
     */
    public static String toWhereSql(FieldWhereExpression expression) {
        if (expression == null) {
            return "";
        }

        try {
            Expression sqlExpression = parseExpression(expression);
            return sqlExpression != null ? sqlExpression.toString() : "";
        } catch (Exception e) {
            throw new RuntimeException("转换WHERE表达式失败", e);
        }
    }

    /**
     * 解析FieldWhereExpression为JSqlParser的Expression
     */
    private static Expression parseExpression(FieldWhereExpression expression) {
        if (expression == null) {
            return null;
        }
        //处理子查询
        OperatorEnum operator = expression.getOperator();
        if (operator == OperatorEnum.SUB_QUERY_IN) {
            return parseSubQueryInExpression(expression);
        }
        // 处理嵌套条件
        if (expression.getContents() != null && !expression.getContents().isEmpty()) {
            return parseNestedExpression(expression);
        }
        // 处理基本条件
        return parseBasicExpression(expression);
    }

    /**
     * 解析子查询表达式
     * 例如 id in (select id from table where ...)
     */
    private static Expression parseSubQueryInExpression(FieldWhereExpression expression) {
        String field = expression.getField();
        FieldWhereExpression subExpression = expression.getContents().getFirst();
        String subQueryTable = subExpression.getSubQueryTable();
        String selectField = subExpression.getField();
        if (field == null || subQueryTable == null || selectField == null) {
            return null;
        }

        PlainSelect plainSelect = new PlainSelect();

        SelectItem selectItem = new SelectItem();
        selectItem.setExpression(new Column(selectField));
        plainSelect.addSelectItems(selectItem);

        Table table = new Table(subQueryTable);
        plainSelect.setFromItem(table);

        Expression whereExpression = parseExpression(subExpression);
        plainSelect.setWhere(whereExpression);

        ParenthesedSelect parenthesedSelect = new ParenthesedSelect();
        parenthesedSelect.setSelect(plainSelect);
        // 构建IN表达式
        InExpression inExpression = new InExpression();
        inExpression.setLeftExpression(new Column(field));
        inExpression.setRightExpression(parenthesedSelect);

        return inExpression;
    }




    /**
     * 解析嵌套表达式 - 使用显式括号
     */
    private static Expression parseNestedExpression(FieldWhereExpression expression) {
        List<FieldWhereExpression> contents = expression.getContents();
        if (contents == null || contents.isEmpty()) {
            return null;
        }
        Expression result = null;
        OperatorEnum operator = expression.getOperator();
        // 如果只有一个子表达式，直接返回
        if (contents.size() == 1) {
            return parseExpression(contents.getFirst());
        }
        // 处理多个子表达式
        for (FieldWhereExpression item : contents) {
            Expression itemExpression = parseExpression(item);
            if (itemExpression == null) {
                continue;
            }
            // 对复杂的子表达式添加括号
            if (itemExpression instanceof AndExpression ||
                itemExpression instanceof OrExpression) {
                itemExpression = new ParenthesedExpressionList(itemExpression);
            }
            if (result == null) {
                result = itemExpression;
            } else {
                if (OperatorEnum.AND.equals(operator)) {
                    result = new AndExpression(result, itemExpression);
                } else if (OperatorEnum.OR.equals(operator)) {
                    result = new OrExpression(result, itemExpression);
                }
            }
        }

        // 对最终结果添加括号
        if (expression.getContents().size() > 1) {
            return new ParenthesedExpressionList(result);
        }

        return result;
    }

    /**
     * 解析基本表达式
     */
    private static Expression parseBasicExpression(FieldWhereExpression expression) {
        String field = expression.getField();
        OperatorEnum operator = expression.getOperator();
        List<Object> values = expression.getValues();

        if (field == null || operator == null) {
            return null;
        }

        Column column = new Column(field);

        switch (operator) {
            case EQ:
                return new EqualsTo(column, convertToExpression(values.getFirst()));
            case NE:
                return new NotEqualsTo(column, convertToExpression(values.getFirst()));
            case GT:
                return new GreaterThan(column, convertToExpression(values.getFirst()));
            case LT:
                return new MinorThan(column, convertToExpression(values.getFirst()));
            case GE:
                return new GreaterThanEquals(column, convertToExpression(values.getFirst()));
            case LE:
                return new MinorThanEquals(column, convertToExpression(values.getFirst()));
            case LIKE:
                LikeExpression likeExpr = new LikeExpression();
                likeExpr.setLeftExpression(column);
                likeExpr.setRightExpression(convertToExpression(values.getFirst()));
                return likeExpr;
            case IN:
                return new InExpression(column, createExpressionList(values, true));
            case NOT_IN:
                InExpression notInExpr = new InExpression(column, createExpressionList(values, true));
                notInExpr.setNot(true);
                return notInExpr;
            case IS_NULL:
                IsNullExpression isNullExpr = new IsNullExpression();
                isNullExpr.setLeftExpression(column);
                return isNullExpr;
            case NOT_NULL:
                IsNullExpression isNotNullExpr = new IsNullExpression();
                isNotNullExpr.setLeftExpression(column);
                isNotNullExpr.setNot(true);
                return isNotNullExpr;
            case BETWEEN:
                if (values.size() >= 2) {
                    Between between = new Between();
                    between.setLeftExpression(column);
                    between.setBetweenExpressionStart(convertToExpression(values.get(0)));
                    between.setBetweenExpressionEnd(convertToExpression(values.get(1)));
                    return between;
                }
                break;
            default:
                throw new IllegalArgumentException("不支持的操作符: " + operator);
        }

        return null;
    }

    /**
     * 将值列表转换为ExpressionList
     */
    /**
     * 将值列表转换为ExpressionList，支持JSqlParser 5.1版本
     * @param values 值列表
     * @param withParenthesis 是否添加括号
     * @return 表达式列表或带括号的表达式列表
     */
    private static Expression createExpressionList(List<Object> values, boolean withParenthesis) {
        if (values == null || values.isEmpty()) {
            return new ExpressionList<>();
        }

        List<Expression> expressions = new ArrayList<>();
        for (Object value : values) {
            expressions.add(convertToExpression(value));
        }

        // 创建表达式列表
        ExpressionList<Expression> expressionList = new ExpressionList<>(expressions);

        // 根据需要添加括号
        if (withParenthesis) {
            return new ParenthesedExpressionList<>(expressionList);
        } else {
            return expressionList;
        }
    }


    /**
     * 将Java对象转换为JSqlParser表达式
     */
    private static Expression convertToExpression(Object value) {
        if (value == null) {
            return new NullValue();
        } else if (value instanceof Number) {
            return new LongValue(((Number) value).longValue());
        } else {
            return new StringValue(value.toString());
        }
    }

}
