package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.KbRepoDataIndexAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoDataIndexDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoDeleteIndexVectorBody;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.IndexTypeEnum;
import cn.genn.ai.hub.app.application.processor.AIHubInvokeService;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataIndexRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.lock.base.LockException;
import cn.genn.lock.base.LockTemplate;
import cn.genn.lock.base.properties.LockInfo;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据条目索引维护
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoDataIndexActionService {

    private final KbRepDataIndexRepositoryImpl dataIndexRepository;
    private final KbRepDataRepositoryImpl dataRepository;
    private final KbRepoDataIndexAssembler assembler;
    private final LockTemplate lockTemplate;
    private final GennAIHubProperties properties;
    private final AIHubInvokeService invokeService;

    /**
     * 根据index索引返回需要向量化的原始文本
     *
     * @return
     */
    public String handleRawText(String dataKey, String indexKey) {
        // 按dataKey查询
        KbRepoDataPO dataPO = dataRepository.queryByDataKey(dataKey);
        if (Objects.isNull(dataPO)) {
            throw new RuntimeException("dataKey is not exist");
        }
        KbRepoDataIndexPO indexPO = dataIndexRepository.queryByIndexKey(indexKey);
        if (Objects.isNull(indexPO)) {
            throw new RuntimeException("indexKey is not exist");
        }
        // 判断是默认索引 还是 用户自定义索引
        if (indexPO.getIndexType().equals(IndexTypeEnum.DEFAULT)) {
            return StrUtil.join(" ", dataPO.getQuestion(), Optional.ofNullable(dataPO.getAnswer()).orElse(""));
        } else {
            return indexPO.getIndexData();
        }
    }

    /**
     * 创建数据条目索引 为某个数据条目增加一条默认索引
     */
    @Transactional(rollbackFor = Exception.class)
    public void createDataIndexOfDefault(Long repoId, Long collectionId, List<KbRepoDataIndexDTO> dtoList) {
        // 加分布式锁
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock("createDataIndexOfDefault_repo_" + repoId + "_collection_" + collectionId);
            if (lockInfo != null) {
                // 先查询数据条目索引是否存在
                List<KbRepoDataIndexPO> indexList = dataIndexRepository.selectDefaultIndexFromCollection(repoId, collectionId);
                Map<String, KbRepoDataIndexPO> existDefaultIndexMap = indexList.stream().collect(Collectors.toMap(KbRepoDataIndexPO::getDataKey, Function.identity()));
                // 如果存在，则不需要做什么，不存在则新增默认索引
                List<KbRepoDataIndexPO> needCreateIndexList = new ArrayList<>();
                dtoList.stream().filter(o -> o.getIndexType().equals(IndexTypeEnum.DEFAULT)).forEach(dto -> {
                    if (!existDefaultIndexMap.containsKey(dto.getDataKey())) {
                        KbRepoDataIndexPO indexPO = assembler.DTO2PO(dto);
                        indexPO.setIndexType(IndexTypeEnum.DEFAULT);
                        indexPO.setIndexKey(UUID.randomUUID().toString());
                        //                        indexPO.setIndexData(dto.getDataKey());
                        indexPO.setIndexSort(0);
                        indexPO.setHandleStatus(HandleStatusEnum.WAIT);
                        needCreateIndexList.add(indexPO);
                    }
                });
                if (!needCreateIndexList.isEmpty()) {
                    dataIndexRepository.saveBatch(needCreateIndexList);
                }
            } else {
                throw new LockException();
            }
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    /**
     * 每次都是传入全量的用户自定义索引
     *
     * @param repoId
     * @param collectionId
     * @param dtoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void createDataIndexOfCustomer(Long repoId, Long collectionId, String dataKey, List<KbRepoDataIndexDTO> dtoList) {
        // 加分布式锁
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock("createDataIndexOfCustomer_repo_" + repoId + "_collection_" + collectionId);
            if (lockInfo != null) {
                // 先查询数据条目索引是否存在
                List<KbRepoDataIndexPO> indexList = new ArrayList<>();
                if (Objects.nonNull(dataKey)) {
                    indexList = dataIndexRepository.selectCustomerIndexFromCollection(repoId, collectionId, dataKey);
                }
                Map<String, KbRepoDataIndexPO> existDefaultIndexMap = indexList.stream().collect(Collectors.toMap(KbRepoDataIndexPO::getIndexKey, Function.identity()));
                // 如果存在，则不需要做什么，不存在则新增默认索引
                List<KbRepoDataIndexPO> needCreateIndexList = new ArrayList<>();
                List<Long> needDeleteIndexIdList = new ArrayList<>();
                dtoList.forEach(dto -> {
                    if (StrUtil.isBlank(dto.getIndexKey())) {
                        KbRepoDataIndexPO indexPO = assembler.DTO2PO(dto);
                        indexPO.setIndexType(IndexTypeEnum.CUSTOMER);
                        indexPO.setIndexKey(UUID.randomUUID().toString());
                        indexPO.setHandleStatus(HandleStatusEnum.WAIT);
                        indexPO.setIndexSort(1);
                        indexPO.setId(null);
                        needCreateIndexList.add(indexPO);
                    } else if (!existDefaultIndexMap.containsKey(dto.getIndexKey())) {
                        KbRepoDataIndexPO indexPO = assembler.DTO2PO(dto);
                        indexPO.setIndexType(IndexTypeEnum.CUSTOMER);
                        indexPO.setIndexKey(dto.getIndexKey());
                        indexPO.setHandleStatus(HandleStatusEnum.WAIT);
                        indexPO.setIndexSort(1);
                        indexPO.setId(null);
                        needCreateIndexList.add(indexPO);
                    }
                });
                if (!needCreateIndexList.isEmpty()) {
                    dataIndexRepository.saveBatch(needCreateIndexList);
                }
                // 如果已经存在在表里的索引 但是传入的索引数据为空，则删除该索引
                List<Long> indexIdList = dtoList.stream().map(KbRepoDataIndexDTO::getId).filter(Objects::nonNull).toList();
                existDefaultIndexMap.forEach((id, indexPO) -> {
                    if (!indexIdList.contains(id)) {
                        needDeleteIndexIdList.add(indexPO.getId());
                    }
                });
                if (!needDeleteIndexIdList.isEmpty()) {
                    dataIndexRepository.removeByIds(needDeleteIndexIdList);
                }
            } else {
                throw new LockException();
            }
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    public void updateHandleStatusOfDataKeyList(Long repoId, Long collectionId, List<String> dataKeyList, HandleStatusEnum handleStatusEnum) {
        dataIndexRepository.updateHandleStatusOfDataKey(repoId, collectionId, dataKeyList, handleStatusEnum);
    }

    public boolean deleteDataIndexVectorOfIndex(Long repoId, Long collectionId, String dataKey, String indexKey) {
        KbRepoDeleteIndexVectorBody body = KbRepoDeleteIndexVectorBody.builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .repoId(repoId)
            .collectionId(collectionId)
            .dataKey(dataKey)
            .indexKey(indexKey)
            .build();
        try {
            String url = properties.getKbInvokeUrl().getDeleteDataIndexVectorOfIndexUrl();
            TypeToken<Boolean> typeToken = new TypeToken<Boolean>() {
            };
            ResponseResult<Boolean> result = invokeService.fetchPost(url, JsonUtils.toJson(body), typeToken);
            log.info("deleteDataIndexVectorOfIndex invoke result:{}", result);
            return Boolean.valueOf(result.getData());
        } catch (Exception e) {
            log.error("deleteDataIndexVectorOfIndex invoke error:{}", e);
            throw new RuntimeException(e);
        }
    }

    public boolean deleteDataIndexVectorOfRepo(Long repoId) {
        KbRepoDeleteIndexVectorBody body = KbRepoDeleteIndexVectorBody.builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .repoId(repoId)
            .build();
        try {
            String url = properties.getKbInvokeUrl().getDeleteDataIndexVectorOfRepoUrl();
            TypeToken<Boolean> typeToken = new TypeToken<Boolean>() {
            };
            ResponseResult<Boolean> result = invokeService.fetchPost(url, JsonUtils.toJson(body), typeToken);
            log.info("deleteDataIndexVectorOfRepo invoke result:{}", result);
            return Boolean.valueOf(result.getData());
        } catch (Exception e) {
            log.error("deleteDataIndexVectorOfRepo invoke error:{}", e);
            throw new RuntimeException(e);
        }
    }

    public boolean deleteDataIndexVectorOfCollection(Long repoId, Long collectionId, Long tenantId) {
        KbRepoDeleteIndexVectorBody body = KbRepoDeleteIndexVectorBody.builder()
            .tenantId(tenantId)
            .repoId(repoId)
            .collectionId(collectionId)
            .build();
        try {
            String url = properties.getKbInvokeUrl().getDeleteDataIndexVectorOfCollectionUrl();
            TypeToken<Boolean> typeToken = new TypeToken<Boolean>() {
            };
            ResponseResult<Boolean> result = invokeService.fetchPost(url, JsonUtils.toJson(body), typeToken);
            log.info("deleteDataIndexVectorOfCollection invoke result:{}", result);
            return Boolean.valueOf(result.getData());
        } catch (Exception e) {
            log.error("deleteDataIndexVectorOfCollection invoke error ", e);
            throw new RuntimeException(e);
        }
    }

    public boolean deleteDataIndexVectorOfData(Long repoId, Long collectionId, String dataKey, Long tenantId) {
        KbRepoDeleteIndexVectorBody body = KbRepoDeleteIndexVectorBody.builder()
            .tenantId(tenantId)
            .repoId(repoId)
            .collectionId(collectionId)
            .dataKey(dataKey)
            .build();
        try {
            String url = properties.getKbInvokeUrl().getDeleteDataIndexVectorOfDataUrl();
            TypeToken<Boolean> typeToken = new TypeToken<Boolean>() {
            };
            ResponseResult<Boolean> result = invokeService.fetchPost(url, JsonUtils.toJson(body), typeToken);
            log.info("deleteDataIndexVectorOfData invoke result:{}", result);
            return Boolean.valueOf(result.getData());
        } catch (Exception e) {
            log.error("deleteDataIndexVectorOfData invoke error:{}", e);
            throw new RuntimeException(e);
        }
    }

}

