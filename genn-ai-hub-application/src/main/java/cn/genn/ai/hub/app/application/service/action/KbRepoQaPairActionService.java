package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.KbRepoQaPairAssembler;
import cn.genn.ai.hub.app.application.command.IdListCommand;
import cn.genn.ai.hub.app.application.command.KbRepoQaPairCreateCommand;
import cn.genn.ai.hub.app.application.command.KbRepoQaPairEditCommand;
import cn.genn.ai.hub.app.application.command.KbRepoQaPairUploadCommand;
import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.processor.BaseExcelListener;
import cn.genn.ai.hub.app.application.service.query.KbRepoBaseInfoQueryService;
import cn.genn.ai.hub.app.application.service.query.KbRepoFileQueryService;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoQaPairRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairPO;
import cn.genn.ai.hub.app.infrastructure.utils.ExcelHandleUtil;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.QaPairVectorUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.lang.UUID;
import cn.hutool.extra.spring.SpringUtil;
import cn.idev.excel.FastExcel;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 知识库问答对业务逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoQaPairActionService {

    private final KbRepoQaPairAssembler qaPairAssembler;
    private final KbRepoQaPairRepositoryImpl qaPairRepository;
    private final FileStorageOperation fileStorageOperation;
    private final KbRepoFileQueryService fileQueryService;
    private final KbRepoBaseInfoQueryService repoQueryService;
    private final QaPairVectorUtil qaPairVectorUtil;

    @Transactional(rollbackFor = Exception.class)
    public Long create(KbRepoQaPairCreateCommand command) {
        try {
            KbRepoQaPairPO qaPairPO = KbRepoQaPairPO.builder()
                .tenantId(CurrentUserHolder.getTenantId())
                .repoId(command.getRepoId())
                .qaPairKey(UUID.fastUUID().toString())
                .similarQuestions(command.getSimilarQuestions())
                .question(command.getQuestion())
                .answer(command.getAnswer())
                .build();
            qaPairRepository.insert(qaPairPO);
            // 获取知识库的向量模型
            KbRepoBaseInfoDTO repoInfo = repoQueryService.get(IdQuery.builder().id(command.getRepoId()).build());
            // 录入一条问答对向量化任务
            KbRepoTaskOfQaPairVectorBody taskBody = KbRepoTaskOfQaPairVectorBody.builder()
                .tenantId(CurrentUserHolder.getTenantId())
                .repoId(command.getRepoId())
                .qaPairKey(qaPairPO.getQaPairKey())
                .vectorModelKey(repoInfo.getVectorModelKey())
                .build();
            SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfQaPairVector(taskBody);
            return qaPairPO.getId();
        } catch (Exception e) {
            log.error("create qa pair error, command: {}", command, e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(KbRepoQaPairEditCommand command) {
        try {
            KbRepoQaPairPO po = qaPairAssembler.kbRepoQaPairEditCommand2KbRepoQaPairPO(command);
            KbRepoQaPairPO data = qaPairRepository.getById(command.getId());
            if (data == null) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
            }
            qaPairRepository.editQaPair(po);
            // 获取知识库的向量模型
            KbRepoBaseInfoDTO repoInfo = repoQueryService.get(IdQuery.builder().id(command.getRepoId()).build());
            // 录入一条问答对向量化任务
            KbRepoTaskOfQaPairVectorBody taskBody = KbRepoTaskOfQaPairVectorBody.builder()
                .tenantId(CurrentUserHolder.getTenantId())
                .repoId(command.getRepoId())
                .qaPairKey(data.getQaPairKey())
                .vectorModelKey(repoInfo.getVectorModelKey())
                .build();
            SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfQaPairVector(taskBody);
            return true;
        } catch (Exception e) {
            log.error("edit qa pair error, command: {}", command, e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(IdCommand command) {
        try {
            KbRepoQaPairPO data = qaPairRepository.getQaPairById(command.getId());
            if (data == null) {
                return true;
            }
            KbRepoBaseInfoDTO repo = repoQueryService.get(IdQuery.builder().id(data.getRepoId()).build());
            if (repo == null) {
                return true;
            }
            qaPairRepository.removeById(command.getId());
            // 删除向量
            qaPairVectorUtil.deleteQaPairVectorByQaPairKey(repo.getVectorModelKey(), data.getQaPairKey());
            return true;
        } catch (Exception e) {
            log.error("delete qa pair error, command: {}", command, e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDelete(IdListCommand command) {
        try {
            if (command.getIds() == null || command.getIds().isEmpty()) {
                return true;
            }
            List<KbRepoQaPairPO> qaPairPOList = qaPairRepository.getQaPairListByIds(command.getIds());
            if (qaPairPOList == null || qaPairPOList.isEmpty()) {
                return true;
            }
            KbRepoBaseInfoDTO repo = repoQueryService.get(IdQuery.builder().id(qaPairPOList.getFirst().getRepoId()).build());
            if (repo == null) {
                return true;
            }
            List<Long> ids = qaPairPOList.stream().map(KbRepoQaPairPO::getId).toList();
            qaPairRepository.batchDelete(ids);
            // 删除向量
            List<String> qaPairKeyList = qaPairPOList.stream().map(KbRepoQaPairPO::getQaPairKey).toList();
            qaPairVectorUtil.deleteQaPairVectorByQaPairKeyList(repo.getVectorModelKey(), qaPairKeyList);
            return true;
        } catch (Exception e) {
            log.error("batch delete qa pairs error, command: {}", command, e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean importExcel(KbRepoQaPairUploadCommand command) {
        try {
            KbRepoFileDTO fileInfo = fileQueryService.get(IdQuery.builder().id(command.getFileId()).build());
            if (fileInfo == null) {
                log.error("导入Excel文件为空");
                return false;
            }
            InputStream inputStream = fileStorageOperation.getFile(fileInfo.getExternalFileId());
            // 检查文件类型
            String originalFilename = fileInfo.getFileName();
            if (originalFilename == null || !(originalFilename.endsWith(".xlsx") || originalFilename.endsWith(".xls"))) {
                log.error("文件格式不正确，只支持.xlsx或.xls格式");
                throw new BusinessException(MessageCode.KB_REPO_FILE_CHECK_EXCEL_FAIL);
            }
            ExcelHandleUtil.ValidateResult validateResult = ExcelHandleUtil.validateFirstRow(inputStream, KbRepoQaPairExcelImportDTO.class);
            if (!validateResult.isValid()) {
                throw new BusinessException(MessageCode.IMPORT_EXCEL_HEAD_NOT_MATCH);
            }
            inputStream = validateResult.getResetInputStream();
            BaseExcelListener<KbRepoQaPairExcelImportDTO> baseExcelListener = new BaseExcelListener<>();
            FastExcel.read(inputStream, KbRepoQaPairExcelImportDTO.class, baseExcelListener).sheet().doRead();
            // 得到读取数据
            List<KbRepoQaPairExcelImportDTO> dataList = baseExcelListener.getDataList();
            // 过滤掉问题或者答案为空的数据
            dataList.removeIf(item -> item.getQuestion() == null || item.getQuestion().trim().isEmpty() || item.getAnswer() == null || item.getAnswer().trim().isEmpty());
            // 生成qaPairKey
            List<KbRepoQaPairPO> poList = qaPairAssembler.kbRepoQaPairExcelImportDTO2PO(dataList);
            poList.forEach(item -> {
                item.setTenantId(CurrentUserHolder.getTenantId());
                item.setRepoId(command.getRepoId());
                item.setQaPairKey(UUID.fastUUID().toString());
            });
            qaPairRepository.batchInsert(poList);
            KbRepoBaseInfoDTO repoInfo = repoQueryService.get(IdQuery.builder().id(command.getRepoId()).build());
            // 录入一批问答对向量化任务
            poList.forEach(item -> {
                // 录入一条问答对向量化任务
                KbRepoTaskOfQaPairVectorBody taskBody = KbRepoTaskOfQaPairVectorBody.builder()
                    .tenantId(CurrentUserHolder.getTenantId())
                    .repoId(item.getRepoId())
                    .qaPairKey(item.getQaPairKey())
                    .vectorModelKey(repoInfo.getVectorModelKey())
                    .build();
                SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfQaPairVector(taskBody);
            });
            return true;
        } catch (Exception e) {
            log.error("import excel error", e);
            throw e;
        }
    }

    public void updateHandleStatusOfQaPairKey(String qaPairKey, HandleStatusEnum handleStatusEnum) {
        qaPairRepository.updateHandleStatusOfQaPairKey(qaPairKey, handleStatusEnum);
    }
}

