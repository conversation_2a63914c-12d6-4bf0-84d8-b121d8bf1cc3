package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;

/**
 * 插件查询参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "飞书文档子列表查询参数")
public class FSSheetContentQuery {

    @Schema(description = "知识库ID", required = true)
    @NotEmpty(message = "知识库ID")
    private Long repoId;

    @Schema(description = "sheetId", required = true)
    @NotEmpty(message = "sheetId 不能为空")
    private String sheetId;

    @Schema(description = "表格token", required = true)
    private String objToken;

    @Schema(description = "查询范围 <开始位置>:<结束位置>", example = "A1:C8", required = true)
    private String ranges;

    /**
     *
     * 指定单元格数据的格式。可选值如下所示。当参数缺省时，默认不进行公式计算，返回公式本身，且单元格为数值格式。
     *
     * ToString：返回纯文本的值（数值类型除外）
     * Formula：单元格中含有公式时，返回公式本身
     * FormattedValue：计算并格式化单元格
     * UnformattedValue：计算但不对单元格进行格式化
     */
    @Schema(description = "指定单元格数据的格式")
    private String valueRenderOption;

}
