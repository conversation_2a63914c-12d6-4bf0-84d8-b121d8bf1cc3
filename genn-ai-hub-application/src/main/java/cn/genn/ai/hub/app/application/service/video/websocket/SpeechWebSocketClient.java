package cn.genn.ai.hub.app.application.service.video.websocket;

import cn.genn.ai.hub.app.application.service.video.protocol.EventType;
import cn.genn.ai.hub.app.application.service.video.protocol.Message;
import cn.genn.ai.hub.app.application.service.video.protocol.MessageFlag;
import cn.genn.ai.hub.app.application.service.video.protocol.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
public class SpeechWebSocketClient extends WebSocketClient {
    private final BlockingQueue<Message> messageQueue = new LinkedBlockingQueue<>();

    public SpeechWebSocketClient(URI serverUri, Map<String, String> headers) {
        super(serverUri, headers);
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        log.info("WebSocket连接已建立：");
        // 打印所有请求头信息
        for (Iterator<String> it = handshakedata.iterateHttpFields(); it.hasNext(); ) {
            String headerName = it.next();
            log.info("  {}: {}", headerName, handshakedata.getFieldValue(headerName));
        }
    }

    @Override
    public void onMessage(String message) {
        log.warn("收到未预期的文本消息: {}", message);
    }

    @Override
    public void onMessage(ByteBuffer bytes) {
        try {
            Message message = Message.unmarshal(bytes.array());
            messageQueue.put(message);
            log.debug("收到消息: type={}, event={}", message.getType(), message.getEvent());
        } catch (Exception e) {
            log.error("解析消息失败", e);
        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("WebSocket连接已关闭: code={}, reason={}, remote={}", code, reason, remote);
    }

    @Override
    public void onError(Exception ex) {
        log.error("WebSocket错误", ex);
    }

    public void sendStartConnection() throws Exception {
        Message message = new Message(MessageType.MSG_TYPE_FULL_CLIENT, MessageFlag.MSG_FLAG_WITH_EVENT);
        message.setEvent(EventType.TYPE_START_CONNECTION);
        message.setPayload("{}".getBytes());
        send(message.marshal());
    }

    public void sendFinishConnection() throws Exception {
        Message message = new Message(MessageType.MSG_TYPE_FULL_CLIENT, MessageFlag.MSG_FLAG_WITH_EVENT);
        message.setEvent(EventType.TYPE_FINISH_CONNECTION);
        send(message.marshal());
    }

    public void sendStartSession(byte[] payload, String sessionId) throws Exception {
        Message message = new Message(MessageType.MSG_TYPE_FULL_CLIENT, MessageFlag.MSG_FLAG_WITH_EVENT);
        message.setEvent(EventType.TYPE_START_SESSION);
        message.setSessionId(sessionId);
        message.setPayload(payload);
        send(message.marshal());
    }

    public void sendFinishSession(String sessionId) throws Exception {
        Message message = new Message(MessageType.MSG_TYPE_FULL_CLIENT, MessageFlag.MSG_FLAG_WITH_EVENT);
        message.setEvent(EventType.TYPE_FINISH_SESSION);
        message.setSessionId(sessionId);
        message.setPayload("{}".getBytes());
        send(message.marshal());
    }

    public void sendTaskRequest(byte[] payload, String sessionId) throws Exception {
        Message message = new Message(MessageType.MSG_TYPE_FULL_CLIENT, MessageFlag.MSG_FLAG_WITH_EVENT);
        message.setEvent(EventType.TYPE_TASK_REQUEST);
        message.setSessionId(sessionId);
        message.setPayload(payload);
        send(message.marshal());
    }

    public void sendFullClientMessage(byte[] payload) throws Exception {
        Message message = new Message(MessageType.MSG_TYPE_FULL_CLIENT, MessageFlag.MSG_FLAG_NO_SEQ);
        message.setPayload(payload);
        send(message.marshal());
    }

    public Message receiveMessage() throws InterruptedException {
        return messageQueue.take();
    }
}
