package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 */
@Data
public class VoiceProperties {

    private String appId = "685b509374f0a9018b337e7b";

    private String appKey = "6e6e98a1f6114ea48542b3e96574dadb";

    private String accessKey = "AKLTZWUwYWUxM2UyZjIzNDk0NjhkYjA1ZWI4MWE5ZjFmN2E";
    private String secretKey = "WlRreFl6ZG1aR1U1WWpVeU5HUTBaR0l6WVRsbE9XRmhZakk0WW1GaE1qWQ==";

    @NestedConfigurationProperty
    private ASRConfig asr = new ASRConfig();

    @NestedConfigurationProperty
    private TTSConfig tts = new TTSConfig();

    @NestedConfigurationProperty
    private LLMConfig llm = new LLMConfig();

    @NestedConfigurationProperty
    private SubtitleConfig subtitle = new SubtitleConfig();

}
