package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.api.UserAbilityImpl;
import cn.genn.ai.hub.app.application.dto.CollaboratorDTO;
import cn.genn.ai.hub.app.application.dto.TeamMemberDTO;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.application.query.TeamMemberQuery;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommonSearch;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.auth.service.AuthCommonService;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TeamInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TeamUserRefRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamUserRefPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.GennUtils;
import cn.genn.core.utils.query.MemoryQueryUtils;
import cn.genn.core.utils.query.MemoryQueryWrapper;
import cn.genn.core.utils.query.model.MemoryOrderType;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 团队成员查询服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TeamMemberQueryService {

    private final TeamInfoRepositoryImpl teamInfoRepository;
    private final TeamUserRefRepositoryImpl teamUserRefRepository;
    private final UserAbilityImpl userAbility;
    private final AuthCommonService authCommonService;


    /**
     * 获取团队下所有成员
     */
    public List<TeamMemberDTO> listUserByTeam(Long teamId) {
        TeamMemberQuery query = new TeamMemberQuery();
        query.setPageNo(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setTeamId(teamId);
        PageResultDTO<TeamMemberDTO> pageResultDTO = page(query);
        if (GennUtils.pageIsEmpty(pageResultDTO)) {
            return Collections.emptyList();
        }
        return pageResultDTO.getList();
    }

    /**
     * 分页查询团队成员
     *
     * @param query 查询条件
     * @return 团队成员分页对象
     */
    public PageResultDTO<TeamMemberDTO> page(TeamMemberQuery query) {
        // 检查团队是否存在
        TeamInfoPO teamInfo = teamInfoRepository.getTeamById(query.getTeamId());
        if (teamInfo == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }

        // 获取团队成员
        List<TeamUserRefPO> teamUserRefs = teamUserRefRepository.getTeamUserRefByTeamId(query.getTeamId());
        if (teamUserRefs.isEmpty()) {
            return PageResultDTO.empty();
        }

        Map<Long, TeamUserRefPO> teamUserRefMap = teamUserRefs.stream().collect(Collectors.toMap(TeamUserRefPO::getUserId, Function.identity()));

        // 获取用户ID列表
        Set<Long> userIds = teamUserRefs.stream()
            .map(TeamUserRefPO::getUserId)
            .collect(Collectors.toSet());

        // 查询用户信息
        List<UpmUserDTO> upmUserDTOS = userAbility.listByIds(new ArrayList<>(userIds));
        if (CollUtil.isEmpty(upmUserDTOS)) {
            return PageResultDTO.empty();
        }

        List<TeamMemberDTO> teamMembers = upmUserDTOS.stream()
            .map(user -> {
                TeamMemberDTO memberDTO = new TeamMemberDTO();
                memberDTO.setUserId(user.getId());
                memberDTO.setUsername(user.getUsername());
                memberDTO.setNick(user.getNick());
                memberDTO.setTelephone(user.getTelephone());
                memberDTO.setAvatar(user.getAvatar());
                memberDTO.setIsOwner(user.getId().equals(teamInfo.getOwnerUserId()));
                memberDTO.setJoinTime(teamUserRefMap.getOrDefault(user.getId(), new TeamUserRefPO()).getCreateTime());
                return memberDTO;
            })
            .collect(Collectors.toList());

        MemoryQueryWrapper<TeamMemberDTO> queryWrapper = new MemoryQueryWrapper<>();
        queryWrapper
            .page(query.getPageNo(), query.getPageSize())
            .orderBy("joinTime", MemoryOrderType.DESC)
            .like(CharSequenceUtil.isNotEmpty(query.getUserName()), "username", query.getUserName());
        return MemoryQueryUtils.selectPage(teamMembers, queryWrapper);
    }

    /**
     * 统一的填充协作者信息方法
     *
     * @param <T>                 实体类型，可以是KbRepoBaseInfoDTO或AgentInfoDTO
     * @param dtoList             需要填充协作者的实体列表
     * @param idGetter            获取实体ID的函数
     * @param teamIdGetter        获取团队ID的函数
     * @param createUserIdGetter  获取创建用户ID的函数
     * @param resourceType        资源类型
     * @param collaboratorsSetter 设置协作者的函数
     */
    public <T> void fillCollaborators(
        List<T> dtoList,
        Function<T, Long> idGetter,
        Function<T, Long> teamIdGetter,
        Function<T, Long> createUserIdGetter,
        ResourceType resourceType,
        BiConsumer<T, List<CollaboratorDTO>> collaboratorsSetter) {

        // 空集合检查
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }

        T first = dtoList.getFirst();
        Long teamId = teamIdGetter.apply(first);

        if (teamId == null) {
            // 个人空间创建的资源，填充创建人信息
            List<UpmUserDTO> upmUserDTOList = userAbility.listByIds(
                Collections.singletonList(createUserIdGetter.apply(first)));

            dtoList.forEach(dto -> {
                List<CollaboratorDTO> collaboratorDTOS = CollaboratorDTO.from(upmUserDTOList);
                collaboratorDTOS.forEach(collaboratorDTO -> {
                    collaboratorDTO.setOwner(true);
                    collaboratorDTO.setCurrentUser(true);
                });
                collaboratorsSetter.accept(dto, collaboratorDTOS);
            });
            return;
        }

        // 获取权限信息
        List<AuthCommon> authCommonList = authCommonService.list(AuthCommonSearch
            .builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .subjectType(SubjectType.USER)
            .resourceType(Collections.singletonList(resourceType))
            .resourceKey(dtoList.stream()
                .map(idGetter)
                .map(String::valueOf)
                .collect(Collectors.toList()))
            .actionType(List.of(ActionType.MANAGER, ActionType.EDIT))
            .build());

        authCommonList.addAll(authCommonService.list(AuthCommonSearch
            .builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .subjectType(SubjectType.USER)
            .resourceType(Collections.singletonList(ResourceType.TEAM))
            .resourceKey(Collections.singletonList(String.valueOf(teamId)))
            .actionType(List.of(ActionType.MANAGER))
            .build()));

        Map<String, List<AuthCommon>> resourceAuthMap = authCommonList.stream()
            .filter(authCommon -> authCommon.getResourceType() == resourceType)
            .collect(Collectors.groupingBy(AuthCommon::getResourceKey));

        List<Long> userIds = authCommonList.stream()
            .map(AuthCommon::getSubjectKey)
            .map(Long::valueOf)
            .distinct()
            .collect(Collectors.toList());

        Long adminUser = Long.parseLong(authCommonList.stream().filter(authCommon -> {
            return authCommon.getResourceType() == ResourceType.TEAM;
        }).toList().getFirst().getSubjectKey());

        //移除不在当前团队的成员
        Set<Long> memberUserIds = teamUserRefRepository.getTeamUserRefByTeamId(teamId).stream().
            map(TeamUserRefPO::getUserId).collect(Collectors.toSet());

        userIds.removeIf(userId -> !memberUserIds.contains(userId));

        List<UpmUserDTO> upmUserDTOList = userAbility.listByIds(userIds);

        Map<Long, UpmUserDTO> userMap = upmUserDTOList.stream()
            .collect(Collectors.toMap(UpmUserDTO::getId, user -> user));

        dtoList.forEach(dto -> {
            String id = String.valueOf(idGetter.apply(dto));
            List<AuthCommon> commonList = resourceAuthMap.get(id);
            List<CollaboratorDTO> collaboratorDTOS = new ArrayList<>();
            if (commonList != null) {
                collaboratorDTOS = commonList.stream()
                    .map(authCommon -> {
                        UpmUserDTO user = userMap.get(Long.valueOf(authCommon.getSubjectKey()));
                        if (user == null) {
                            return null;
                        }
                        return CollaboratorDTO.builder()
                            .userId(user.getId())
                            .username(user.getUsername())
                            .avatar(user.getAvatar())
                            .nick(user.getNick())
                            .owner(user.getId().equals(createUserIdGetter.apply(first)))
                            .currentUser((user.getId().equals(CurrentUserHolder.getUserId())))
                            .build();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            }
            // 添加管理员信息
            if (collaboratorDTOS.stream().noneMatch(collaboratorDTO -> collaboratorDTO.getUserId().equals(adminUser))) {
                UpmUserDTO admin = userMap.get(adminUser);
                if (admin != null) {
                    CollaboratorDTO adminCollaborator = CollaboratorDTO.builder()
                        .userId(admin.getId())
                        .username(admin.getUsername())
                        .avatar(admin.getAvatar())
                        .nick(admin.getNick())
                        .owner(admin.getId().equals(createUserIdGetter.apply(first)))
                        .currentUser((admin.getId().equals(CurrentUserHolder.getUserId())))
                        .build();
                    collaboratorDTOS.add(adminCollaborator);
                }
            }

            collaboratorsSetter.accept(dto, collaboratorDTOS);
        });
    }

    public List<CollaboratorDTO> getCollaborators(Long id, Long teamId, Long createUserId, ResourceType resourceType) {
        if (teamId == null) {
            return Collections.emptyList();
        }

        // 获取权限列表
        List<AuthCommon> authCommonList = authCommonService.list(AuthCommonSearch
            .builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .subjectType(SubjectType.USER)
            .resourceType(Collections.singletonList(resourceType))
            .resourceKey(Collections.singletonList(id.toString()))
            .actionType(List.of(ActionType.EDIT, ActionType.MANAGER))
            .build());

        authCommonList.addAll(authCommonService.list(AuthCommonSearch
            .builder()
            .tenantId(CurrentUserHolder.getTenantId())
            .subjectType(SubjectType.USER)
            .resourceType(Collections.singletonList(ResourceType.TEAM))
            .resourceKey(Collections.singletonList(teamId.toString()))
            .actionType(List.of(ActionType.MANAGER))
            .build()));

        if (authCommonList.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取用户ID并筛选
        List<Long> userIds = authCommonList
            .stream()
            .map(authCommon -> Long.parseLong(authCommon.getSubjectKey()))
            .distinct()
            .collect(Collectors.toList());

        Set<Long> memberUserIds = teamUserRefRepository.getTeamUserRefByTeamId(teamId).stream()
            .map(TeamUserRefPO::getUserId).collect(Collectors.toSet());

        userIds.removeIf(userId -> !memberUserIds.contains(userId));

        // 转换为 CollaboratorDTO 列表
        List<CollaboratorDTO> collaboratorDTOS = CollaboratorDTO.from(userAbility.listByIds(userIds));

        collaboratorDTOS.forEach(collaboratorDTO -> {
            collaboratorDTO.setCurrentUser(collaboratorDTO.getUserId().equals(CurrentUserHolder.getUserId()));
            collaboratorDTO.setOwner(collaboratorDTO.getUserId().equals(createUserId));
        });

        return collaboratorDTOS;
    }

}
