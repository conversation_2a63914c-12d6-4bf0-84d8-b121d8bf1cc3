package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 资源邀请协作邀请命令
 *
 * <AUTHOR>
 */
@Data
public class ResourceInviteCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "被邀请用户ID列表")
    @NotNull(message = "被邀请用户ID列表不能为空")
    private List<Long> userIds;

}
