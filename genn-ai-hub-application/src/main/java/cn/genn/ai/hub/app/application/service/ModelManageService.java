package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.assembler.GatewayModelAssembler;
import cn.genn.ai.hub.app.application.assembler.ModelManageAssembler;
import cn.genn.ai.hub.app.application.dto.ModelManageDTO;
import cn.genn.ai.hub.app.application.dto.request.ModelManageQuery;
import cn.genn.ai.hub.app.application.dto.request.SetDefaultModel;
import cn.genn.ai.hub.app.application.enums.ModelTypeEnum;
import cn.genn.ai.hub.app.application.enums.OpType;
import cn.genn.ai.hub.app.application.listener.event.ModelManageEvent;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ModelManageRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelManagePO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.event.redis.component.RedisEventPublish;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelManageService {

    @Resource
    private ModelManageRepositoryImpl manageRepository;
    @Resource
    private ModelManageAssembler mangleAssembler;
    @Resource
    private RedisEventPublish eventPublish;
    @Resource
    private GatewayModelAssembler gatewayModelAssembler;

    @Value("${genn.event.redis.channel-model:model-update}")
    private String channelModel;


    public PageResultDTO<ModelManageDTO> configPage(ModelManageQuery query) {
        return manageRepository.getPage(query);
    }

    public PageResultDTO<ModelManageDTO> activePage(ModelManageQuery query) {
        query.setIsActive(true);
        return manageRepository.getPage(query);
    }

    public Map<ModelTypeEnum, List<ModelManageDTO>> getDefaultModel() {
        return manageRepository.getActiveModel()
            .stream()
            .collect(Collectors.groupingBy(ModelManageDTO::getType));
    }

    public ModelManageDTO getInfo(IdQuery query) {
        return manageRepository.getInfo(query.getId());
    }

    @Transactional
    public void setDefaultModel(SetDefaultModel command) {
        manageRepository.updateModelDefault(command.getOldIds(), false);
        manageRepository.updateModelDefault(command.getNewIds(), true);
    }

    public Long create(ModelManageDTO create) {
        ModelManageDTO model = manageRepository.findByModelAndProviderId(create.getModel(), create.getProviderId());
        if (model != null) {
            throw new BusinessException(MessageCode.MODEL_EXIST);
        }
        Long modelId = manageRepository.create(create);
        eventPublish.publish(channelModel,
            ModelManageEvent.builder()
                .type(OpType.UPDATE)
                .modelId(modelId)
                .build(), false, true);
        return modelId;
    }

    public Boolean updateConfig(ModelManageDTO update) {
        ModelManagePO sourcePO = manageRepository.getById(update.getId());
        if (sourcePO == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        boolean success = manageRepository.updateConfig(update);
        if (success && sourcePO.getIsActive()) {
            eventPublish.publish(channelModel,
                ModelManageEvent.builder()
                    .type(OpType.UPDATE)
                    .modelId(update.getId())
                    .build(), false, true);
        }
        return success;
    }

    public Boolean active(IdQuery query) {
        Boolean success = manageRepository.active(query.getId());
        if (success) {
            manageRepository.setDefaultModel(query.getId());
            eventPublish.publish(channelModel,
                ModelManageEvent.builder()
                    .type(OpType.UPDATE)
                    .modelId(query.getId())
                    .build(), false, true);
        }
        return success;
    }

    public Boolean stop(IdQuery query) {
        Boolean success = manageRepository.stop(query.getId());
        if (success) {
            manageRepository.setDefaultModel(query.getId());
            eventPublish.publish(channelModel,
                ModelManageEvent.builder()
                    .type(OpType.DELETE)
                    .modelId(query.getId())
                    .build(), false, true);
        }
        return success;
    }

    public Boolean delete(IdQuery query) {
        return manageRepository.delete(query.getId());
    }

    public Boolean ping(IdQuery query) {
        ModelManageDTO modelInfo = manageRepository.getInfo(query.getId());
        ModelTypeEnum type = modelInfo.getType();
        return switch (type) {
            case LLM -> pingChat(modelInfo);
            case INDEX -> pingEmbedding(modelInfo);
            case VOICE_COMP, ASR, RERANK -> true;
        };
    }

    private Boolean pingEmbedding(ModelManageDTO modelInfo) {
        try {
            OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(modelInfo.getBaseUrl())
                .apiKey(modelInfo.getApiKey())
                .embeddingsPath(modelInfo.getApiPath())
                .build();
            OpenAiEmbeddingModel embeddingModel = new OpenAiEmbeddingModel(
                openAiApi,
                MetadataMode.EMBED,
                OpenAiEmbeddingOptions.builder()
                    .model(modelInfo.getModel())
                    .user("hello")
                    .build(),
                RetryUtils.DEFAULT_RETRY_TEMPLATE);
            embeddingModel.embedForResponse(List.of("Hello World", "World is big and salvation is near"));
        } catch (Exception e) {
            log.error("ping embedding model error, modelId: [{}]", modelInfo.getId(), e);
            return false;
        }
        return true;
    }

    private Boolean pingChat(ModelManageDTO modelInfo) {
        try {
            OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(modelInfo.getBaseUrl())
                .apiKey(modelInfo.getApiKey())
                .completionsPath(modelInfo.getApiPath())
                .build();
            ChatModel chatModel = OpenAiChatModel
                .builder()
                .defaultOptions(OpenAiChatOptions.builder()
                    .model(modelInfo.getModel())
                    .build())
                .openAiApi(openAiApi)
                .build();
            ChatClient.Builder builder = ChatClient
                .builder(chatModel);
            ChatClient chatClient = builder.build();
            chatClient.prompt("hello").call().content();
            return true;
        } catch (Exception e) {
            log.error("ping chat model error, modelId: [{}]", modelInfo.getId(), e);
            return false;
        }
    }


    public List<ModelManageDTO> gatewayUse() {
        return gatewayModelAssembler.convert(manageRepository.getActives());
    }
}

