package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoFileQuery;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoFileAssembler extends QueryAssembler<KbRepoFileQuery, KbRepoFilePO, KbRepoFileDTO>{

    KbRepoFileAssembler INSTANCE = Mappers.getMapper(KbRepoFileAssembler.class);

    KbRepoFilePO DTO2PO(KbRepoFileDTO kbRepoFileDTO);
}

