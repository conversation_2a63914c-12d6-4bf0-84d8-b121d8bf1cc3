package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 插件查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书表格Sheet列表查询参数")
public class FSSheetsQuery {


    @Schema(description = "应用ID", required = true)
    @NotEmpty(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "表格文件token", required = true)
    private String objToken;


}
