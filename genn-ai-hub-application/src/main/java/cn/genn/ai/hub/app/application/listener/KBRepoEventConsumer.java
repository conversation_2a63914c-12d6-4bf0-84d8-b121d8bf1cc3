package cn.genn.ai.hub.app.application.listener;

import cn.genn.ai.hub.app.application.listener.event.KBRepoEvent;
import cn.genn.ai.hub.app.application.processor.TaskHandler;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.event.rocketmq.component.BaseConsumerListener;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = "${genn.event.rocketmq.naming.topic.genn-ai-hub-app-task-analysis}",
    maxReconsumeTimes = 3,
    consumeMode = ConsumeMode.ORDERLY,
    consumerGroup = "${genn.event.rocketmq.naming.consumer.genn-ai-hub-app-task-analysis}")
public class KBRepoEventConsumer extends BaseConsumerListener<KBRepoEvent> {


    @Resource
    private TaskHandlerFactory taskHandlerFactory;

    @Override
    protected void process(KBRepoEvent event) {
        log.info("KBRepoEventConsumer process event:{}", JsonUtils.toJson(event));
        TaskHandler handler = taskHandlerFactory.getHandler(event.getType());
        if (handler != null) {
            String tenantId = StringUtils.isNotBlank(event.getTenantId()) ? event.getTenantId() : "1";
            GennRequestContext.initTenantId(Long.parseLong(tenantId));
            handler.handleTask(event.getTaskId(), tenantId);
            GennRequestContext.clear();
        }
    }
}
