package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.core.api.tool.FileStorageConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @description 文件存储转换器
 * @date 2025-04-17
 */
@Mapper(componentModel = "spring")
public interface FileStorageAssembler {

    @Mapping(target = "fileStorageType", ignore = true)
    FileStorageConfig toFileStorageConfig(GennAIFileStorageProperties properties);
}
