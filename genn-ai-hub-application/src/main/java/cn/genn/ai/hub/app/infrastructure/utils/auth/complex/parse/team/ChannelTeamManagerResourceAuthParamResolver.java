package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.ChannelSaveCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.TenantChannelRefMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ChannelTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver{

    private final TenantChannelRefMapper tenantChannelRefMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long channelId = switch (uniqueId) {
            case "updateChannel" -> ((ChannelSaveCommand) interceptor.getParamsMap().get("command")).getId();
            case "deleteChannel" -> ((IdCommand) interceptor.getParamsMap().get("command")).getId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return tenantChannelRefMapper.selectById(channelId).getTeamId();
    }
}
