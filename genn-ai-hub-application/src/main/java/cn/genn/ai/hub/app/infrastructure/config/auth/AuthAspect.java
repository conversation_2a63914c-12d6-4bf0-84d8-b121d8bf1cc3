package cn.genn.ai.hub.app.infrastructure.config.auth;

import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.FieldWhereExpression;
import cn.genn.ai.hub.app.domain.auth.model.valobj.OperatorEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.auth.service.AuthCommonService;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.*;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.expression.DefaultAuthExpressionResolver;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.DefaultAuthParamResolver;
import cn.genn.core.exception.CheckException;
import cn.genn.core.utils.SpelUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * 权限切面,控制AuthCommon适配的数据权限
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class AuthAspect {

    @Resource
    private AuthCommonService authService;
    public static ThreadLocal<FieldWhereExpression> FIELD_WHERE_EXPRESSION_THREAD_LOCAL = new ThreadLocal<>();


    @Around("@annotation(batchAndAuth)")
    public Object auth(ProceedingJoinPoint joinPoint, BatchAndAuth batchAndAuth) throws Throwable {
        try {
            Auth[] auths = batchAndAuth.value();

            return Arrays.stream(auths)
                .allMatch(auth -> singleAuth(joinPoint, auth).stream().allMatch(AuthResult::isPass));
        } finally {
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.remove();
        }
    }


    @Around("@annotation(batchAndAuth)")
    public Object authExpression(ProceedingJoinPoint joinPoint, BatchAndAuthExpression batchAndAuth) throws Throwable {
        try {
            AuthExpression[] auths = batchAndAuth.value();
            FieldWhereExpression combineExpression = new FieldWhereExpression();
            combineExpression.setOperator(OperatorEnum.AND);
            combineAuthExpression(joinPoint, auths, combineExpression);
            return joinPoint.proceed();
        } finally {
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.remove();
        }
    }


    @Around("@annotation(batchOrAuth)")
    public Object authExpression(ProceedingJoinPoint joinPoint, BatchOrAuthExpression batchOrAuth) throws Throwable {
        try {
            AuthExpression[] auths = batchOrAuth.value();
            FieldWhereExpression combineExpression = new FieldWhereExpression();
            combineExpression.setOperator(OperatorEnum.OR);
            combineAuthExpression(joinPoint, auths, combineExpression);
            return joinPoint.proceed();
        } finally {
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.remove();
        }
    }


    @Around("@annotation(batchOrAuth)")
    public Object auth(ProceedingJoinPoint joinPoint, BatchOrAuth batchOrAuth) throws Throwable {
        try {
            Auth[] auths = batchOrAuth.value();
            boolean pass = Arrays.stream(auths)
                .anyMatch(auth -> singleAuth(joinPoint, auth).stream().allMatch(AuthResult::isPass));
            if (!pass) {
                throw new CheckException(MessageCode.AUTH_API_AUTH_FAILED);
            }
            return joinPoint.proceed();
        } finally {
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.remove();
        }
    }


    @Around("@annotation(auth)")
    public Object authExpression(ProceedingJoinPoint joinPoint, AuthExpression auth) throws Throwable {
        try {
            FieldWhereExpression fieldWhereExpression = fillFieldWhereExpression(joinPoint, auth);
            if (fieldWhereExpression != null) {
                FIELD_WHERE_EXPRESSION_THREAD_LOCAL.set(fieldWhereExpression);
            }
            return joinPoint.proceed();
        } finally {
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.remove();
        }
    }


    @Around("@annotation(auth)")
    public Object auth(ProceedingJoinPoint joinPoint, Auth auth) throws Throwable {
        try {
            return doAuth(joinPoint, () -> singleAuth(joinPoint, auth));
        } finally {
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.remove();
        }
    }


    private void combineAuthExpression(ProceedingJoinPoint joinPoint, AuthExpression[] auths, FieldWhereExpression combineExpression) {
        List<FieldWhereExpression> validExpressions = Arrays.stream(auths)
            .map(auth -> fillFieldWhereExpression(joinPoint, auth))
            .filter(Objects::nonNull)
            .toList();

        if (!validExpressions.isEmpty()) {
            combineExpression.setContents(new ArrayList<>(validExpressions));
            FIELD_WHERE_EXPRESSION_THREAD_LOCAL.set(combineExpression);
        }
    }

    private Object doAuth(ProceedingJoinPoint joinPoint, Supplier<List<AuthResult>> authSupplier) throws Throwable {
        List<AuthResult> authResults = authSupplier.get();
        if (CollUtil.isEmpty(authResults)) {
            // 无需鉴权
            return joinPoint.proceed();
        }
        boolean isAllowed = authResults.stream().allMatch(AuthResult::isPass);
        if (!isAllowed) {
            throw new CheckException(MessageCode.AUTH_API_AUTH_FAILED);
        }
        return joinPoint.proceed();
    }

    @SuppressWarnings("unchecked")
    private List<AuthResult> singleAuth(ProceedingJoinPoint joinPoint, Auth auth) {
        if (!auth.enabled()) {
            return Collections.emptyList();
        }
        if (auth.onlyAllowSuperAdmin()) {
            if (!authService.isSuperAdmin()) {
                throw new CheckException(MessageCode.AUTH_API_AUTH_FAILED);
            }
        }
        if (auth.actionType().length == 0 && Objects.equals(auth.paramResolver(), DefaultAuthParamResolver.class)) {
            return Collections.emptyList();
        }
        Class<? extends AuthParamResolver> resolverCls = auth.paramResolver();
        if (resolverCls != DefaultAuthParamResolver.class) {
            return complexAuth(joinPoint, auth, resolverCls);
        }
        StandardEvaluationContext spelContext = SpelUtils.initSpelContext(joinPoint);
        if (CharSequenceUtil.isEmpty(auth.fieldKey())) {
            return Collections.emptyList();
        }
        List<Object> resourceIdList = SpelUtils.parseSpelExpression(spelContext, auth.fieldKey(), List.class);
        List<String> resource = new ArrayList<>();
        if (CollUtil.isNotEmpty(resourceIdList)) {
            resourceIdList.forEach(id -> {
                resource.add(id.toString());
            });
        }
        if (CollUtil.isEmpty(resource) && auth.allowFieldKeyNull()) {
            return Collections.emptyList();
        }
        return authService.auth(auth.subjectType(), auth.actionType(), auth.resourceType(), resource);
    }


    private List<AuthResult> complexAuth(ProceedingJoinPoint joinPoint, Auth auth, Class<? extends AuthParamResolver> resolverCls) {
        AuthParamResolver resolver = SpringUtil.getBean(resolverCls);
        AuthInterceptorParam interceptor = new AuthInterceptorParam();
        interceptor.setUniqueId(auth.uniqueId());
        interceptor.setAuth(auth);
        interceptor.setParamsMap(parseMethodParams(joinPoint));
        List<AuthParam> authParams = resolver.authParams(interceptor);
        if (CollUtil.isEmpty(authParams)) {
            return Collections.emptyList();
        }
        ActionType[] actionType = auth.actionType();
        if (actionType.length > 0) {
            authParams.forEach(authParam -> {
                if (authParam.getActionType() == null) {
                    authParam.setActionType(actionType);
                }
            });
        }
        List<AuthResult> authResults = new ArrayList<>();
        for (AuthParam authParam : authParams) {
            List<AuthResult> authResult = authService.auth(authParam.getSubjectType(), authParam.getActionType(), authParam.getResourceType(), Collections.singletonList(authParam.getResourceKey()));
            if (CollUtil.isNotEmpty(authResult) && authResults.stream().anyMatch(result -> !result.isPass())) {
                return AuthResult.ofFailColl(authParam.getSubjectType(), authParam.getResourceType(), authParam.getActionType(), authParam.getResourceKey());
            }
            authResults.addAll(authResult);
        }
        return authResults;
    }

    private Map<String, Object> parseMethodParams(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Map<String, Object> paramsMap = new HashMap<>();
        String[] argNames = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        if (null != argNames) {
            for (int i = 0; i < argNames.length; i++) {
                paramsMap.put(argNames[i], ObjectUtil.cloneIfPossible(args[i]));
            }
        }
        return paramsMap;
    }

    private FieldWhereExpression fillFieldWhereExpression(ProceedingJoinPoint joinPoint, AuthExpression auth) {
        if (!auth.enabled()) {
            return null;
        }
        ActionType[] actionType = auth.actionType();
        SubjectType subjectType = auth.subjectType();
        ResourceType resourceType = auth.resourceType();
        if (auth.authExpressionResolver() != DefaultAuthExpressionResolver.class) {
            AuthExpressionInterceptorParam interceptor = new AuthExpressionInterceptorParam();
            interceptor.setUniqueId(auth.uniqueId());
            interceptor.setAuth(auth);
            interceptor.setParamsMap(parseMethodParams(joinPoint));
            FieldWhereExpression whereExpression = SpringUtil.getBean(auth.authExpressionResolver()).resolve(interceptor);
            if (whereExpression == null) {
                whereExpression = FieldWhereExpression.buildNoAuthExpression();
            }
            return whereExpression;
        }
        return authService.buildCurrUserAuthExpression(subjectType, resourceType, actionType, auth.dbFieldName());
    }
}
