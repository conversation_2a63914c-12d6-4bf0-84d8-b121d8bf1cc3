package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SubtitleConfig {
    /**
     * 是否关闭房间内客户端字幕回调。
     * true：关闭，即不通过客户端接收字幕回调消息。
     * false：开启，通过客户端接收字幕回调消息。开启后，在客户端实现监听 onRoomBinaryMessageReceived（以 Android 为例），即可接收字幕回调消息。
     * 默认值为 false
     */
    private Boolean disableRTSSubtitle = false;
    private String serverMessageUrl;
    private String serverMessageSignature;
    /**
     * 字幕回调时是否需要对齐音频时间戳。
     * 0：对齐音频时间戳。
     * 1：不对齐音频时间戳。取 1 时可更快回调字幕信息。
     * 默认值为 0
     */
    private Integer subtitleMode = 0;
}
