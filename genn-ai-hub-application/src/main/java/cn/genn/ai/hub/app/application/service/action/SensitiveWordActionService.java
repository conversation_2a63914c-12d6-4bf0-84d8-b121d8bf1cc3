package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.SensitiveWordAssembler;
import cn.genn.ai.hub.app.application.command.SensitiveWordSaveCommand;
import cn.genn.ai.hub.app.application.command.SensitiveWordVerifyCommand;
import cn.genn.ai.hub.app.application.command.TagsAssignCommand;
import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.event.AuthCommonModifyEvent;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.sensitiveword.matcher.ScopedSensitiveWordProvider;
import cn.genn.ai.hub.app.domain.sensitiveword.matcher.SensitiveWordMatcherFactory;
import cn.genn.ai.hub.app.domain.sensitiveword.model.config.SensitiveWordConfig;
import cn.genn.ai.hub.app.domain.sensitiveword.model.event.SensitiveWordChangeEvent;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.SensitiveWordRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.SensitiveWordPO;
import cn.genn.ai.hub.app.infrastructure.utils.auth.AuthUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.TransactionUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 敏感词应用服务
 * @date 2025-05-16
 */
@Service
@RequiredArgsConstructor
public class SensitiveWordActionService {

    private final SensitiveWordRepositoryImpl sensitiveWordRepository;

    private final SensitiveWordAssembler sensitiveWordAssembler;

    private final SpringEventPublish springEventPublish;

    private final TagsActionService tagsActionService;

    private final SensitiveWordMatcherFactory sensitiveWordMatcherFactory;

    private final ScopedSensitiveWordProvider scopedSensitiveWordProvider;

    private final AgentInfoRepositoryImpl agentInfoRepository;

    @Transactional
    public void save(SensitiveWordSaveCommand command) {
        // 唯一性校验
        boolean isUnique = sensitiveWordRepository.isWordUnique(
            command.getWords(),
            command.getWordsType(),
            command.getTeamId(),
            CurrentUserHolder.getUserId(),
            command.getId()
        );

        if (!isUnique) {
            throw new BusinessException("敏感词已存在");
        }
        SensitiveWordPO sensitiveWordPO = sensitiveWordAssembler.toPO(command);
        if (sensitiveWordPO.getId() == null) {
            sensitiveWordRepository.save(sensitiveWordPO);
            handleCreateAuth(sensitiveWordPO.getId());
        } else {
            sensitiveWordRepository.updateById(sensitiveWordPO);
        }

        TransactionUtils.doAfterCommit(() -> {
            SensitiveWordPO existSensitiveWord = sensitiveWordRepository.getById(sensitiveWordPO.getId());
            // 更新敏感词缓存
            springEventPublish.publish(SensitiveWordChangeEvent.build(existSensitiveWord));
        });
    }

    @Transactional
    public void delete(Long id) {
        SensitiveWordPO sensitiveWordPO = sensitiveWordRepository.getById(id);
        if (sensitiveWordPO == null) {
            throw new BusinessException("敏感词不存在");
        }
        sensitiveWordRepository.removeById(id);
        // 删除敏感词的权限
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.SENSITIVE_WORD,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(id)), this));

        TransactionUtils.doAfterCommit(() -> {
            // 更新敏感词缓存
            springEventPublish.publish(SensitiveWordChangeEvent.build(sensitiveWordPO));
        });
    }

    public SensitiveWordVerifyDTO verify(SensitiveWordVerifyCommand verifyCommand) {
        AgentInfoDTO agentInfoDTO = agentInfoRepository.getAgentByWorkflowId(verifyCommand.getWorkflowId());
        if (agentInfoDTO == null) {
            throw new BusinessException("智能体不存在");
        }

        // 校验是否开启敏感词校验
        Map<String, Object> extraMap = agentInfoDTO.getExtraConfig();
        SensitiveWordConfig sensitiveWordConfig = JsonUtils.parse(JsonUtils.toJson(extraMap.get("sensitiveWordConfig")), SensitiveWordConfig.class);
        boolean enableVerify = Optional.ofNullable(sensitiveWordConfig)
            .map(SensitiveWordConfig::isEnableVerify)
            .orElse(false);
        if (!enableVerify) {
            return new SensitiveWordVerifyDTO(false, Collections.emptyList());
        }

        verifyCommand.setUserId(agentInfoDTO.getCreateUserId());
        verifyCommand.setTeamId(agentInfoDTO.getTeamId());
        return sensitiveWordMatcherFactory.getActiveMatcher()
            .map(matcher -> matcher.verify(verifyCommand))
            .orElseThrow(() -> new BusinessException("敏感词匹配器未配置或不可用"));
    }

    public void refreshSensitiveWordsCache() {
        scopedSensitiveWordProvider.refreshAllCache();
    }

    public void assignTags(TagsAssignCommand command) {
        SensitiveWordPO sensitiveWordPO = sensitiveWordRepository.getById(command.getId());
        if (sensitiveWordPO == null) {
            throw new BusinessException("敏感词不存在");
        }
        tagsActionService.updateTagRefByResource(String.valueOf(command.getId()), TagTypeEnum.SENSITIVE_WORD,
            sensitiveWordPO.getTeamId(), command.getTagIds());
    }

    private void handleCreateAuth(Long sensitiveWordId) {
        List<AuthCommon> authCommonList = new ArrayList<>();
        authCommonList.add(AuthUtils.buildUserSensitiveWordAuth(sensitiveWordId, CurrentUserHolder.getUserId(), ActionType.MANAGER));
        springEventPublish.publish(AuthCommonModifyEvent
            .buildAddEvent(authCommonList, this));
    }

    public SensitiveWordCacheDTO getCache() {
        // 1. 工作流 敏感词问题，2.敏感词缓存
        Map<String, Set<String>> allScopedKeywords = scopedSensitiveWordProvider.getAllScopedKeywords();
        List<VerifyAgentDTO> agents = agentInfoRepository.getValidAgent().stream().filter(VerifyAgentDTO::getEnableVerify).collect(Collectors.toList());
        return SensitiveWordCacheDTO.builder().allScopedKeywords(allScopedKeywords).agentInfos(agents).build();
    }
}
