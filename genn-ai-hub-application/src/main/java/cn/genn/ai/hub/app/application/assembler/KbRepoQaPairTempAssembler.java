package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoQaPairTempDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoQaPairTempQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairTempPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoQaPairTempAssembler extends QueryAssembler<KbRepoQaPairTempQuery, KbRepoQaPairTempPO, KbRepoQaPairTempDTO>{

    KbRepoQaPairTempAssembler INSTANCE = Mappers.getMapper(KbRepoQaPairTempAssembler.class);

}

