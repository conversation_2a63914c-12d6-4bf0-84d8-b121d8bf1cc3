package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ModelRequestSource {

    WORKFLOW("workflow", "工作流"),
    KNOWLEDGE("knowledge", "知识库"),
    PROMPT("prompt", "提示词"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    ModelRequestSource(String code, String description) {
        this.code = code;
        this.description = description;
    }


}

