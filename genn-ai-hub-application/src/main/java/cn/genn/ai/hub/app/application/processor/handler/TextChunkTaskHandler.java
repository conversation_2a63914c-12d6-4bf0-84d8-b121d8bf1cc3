package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskOfTextChunkBody;
import cn.genn.ai.hub.app.application.enums.CollectionStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.handler.algorithm.FileParseResponse;
import cn.genn.ai.hub.app.application.processor.handler.algorithm.TextChunkRequest;
import cn.genn.ai.hub.app.application.service.action.KbRepoCollectionActionService;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import com.google.gson.reflect.TypeToken;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.TEXT_CHUNK;

@Component
@Slf4j
public class TextChunkTaskHandler extends AbstractTaskHandler {

    @Resource
    private KbRepoCollectionActionService collectionActionService;

    @Override
    public TaskTypeEnum getType() {
        return TEXT_CHUNK;
    }


    @Override
    public void invoke(KbRepoTaskDTO task) {
        // 异步调用算法接口的实现
        // 分块的结果算法通过异步回调 更新任务状态 录入到库中 并且新增索引的处理任务
        async(() -> {
            KbRepoTaskOfTextChunkBody body = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfTextChunkBody.class);
            // 构建请求实体
            TextChunkRequest request = new TextChunkRequest();
            request.setTenantId(task.getTenantId());
            request.setTaskId(task.getId());
            request.setFileId(body.getFileId());
            request.setRawText(body.getRawText());
            request.setDataKey(body.getDataKey());
            request.setSync(body.getSync());
            request.setRepoId(body.getRepoId());
            request.setCollectionId(body.getCollectionId());
            request.setContentType(body.getContentType());
            try {
                String url = properties.getKbInvokeUrl().getRequestUrl(getType());
                TypeToken<FileParseResponse> typeToken = new TypeToken<FileParseResponse>() {
                };
                ResponseResult<FileParseResponse> result = invokeService.fetchPost(url, JsonUtils.toJson(request), typeToken);
                log.info("TextChunkTaskHandler invoke result:{}", JsonUtils.toJson(result));
            } catch (Exception e) {
                log.error("TextChunkTaskHandler invoke error:{}", e);
                throw new RuntimeException(e);
            }
            // 更新数据集状态为分块中
            collectionActionService.updateCollectionStatus(body.getCollectionId(), CollectionStatusEnum.SEGMENTING);
        });
    }

}
