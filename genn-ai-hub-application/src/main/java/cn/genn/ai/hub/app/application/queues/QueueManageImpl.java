package cn.genn.ai.hub.app.application.queues;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueueManageImpl implements QueueManage {


    private final ListOperations<String, String> opsForList;
    private final ZSetOperations<String, String> setOps;

    @Autowired
    public QueueManageImpl(RedisTemplate<String, String> redisTemplate) {
        this.opsForList = redisTemplate.opsForList();
        this.setOps = redisTemplate.opsForZSet();
    }

    @Override
    public boolean isProcessing(String processQueueName, String value) {
        return setOps.score(processQueueName, value) != null;
    }

    @Override
    public Long getProcessQueueSize(String processQueueName) {
        return setOps.size(processQueueName);
    }

    @Override
    public boolean addTaskToProcess(String processQueueName, String value) {
        return setOps.add(processQueueName, value, Double.parseDouble(value));
    }

    @Override
    public void removeTaskFromProcess(String processQueueName, String value) {
        setOps.remove(processQueueName, value);
    }

    @Override
    public void rightPushTaskQueue(String taskQueueName, String value) {
        opsForList.rightPush(taskQueueName, value);
    }

    @Override
    public void leftPushTaskQueue(String taskQueueName, String value) {
        opsForList.leftPush(taskQueueName, value);
    }

    @Override
    public List<String> peekCountFromTaskQueue(String taskQueueName, long count) {
        return opsForList.range(taskQueueName, 0, count);
    }

    @Override
    public List<String> popCountFromTaskQueue(String taskQueueName, long count) {
        return opsForList.leftPop(taskQueueName, count);
    }

    @Override
    public String popFromTaskQueue(String taskQueueName) {
        return opsForList.leftPop(taskQueueName);
    }

    @Override
    public Long getTaskQueueSize(String taskQueueName) {
        return opsForList.size(taskQueueName);
    }

    @Override
    public List<String> popFromTaskQueue(String taskQueueName, Long count) {
        return opsForList.leftPop(taskQueueName, count);
    }
}

