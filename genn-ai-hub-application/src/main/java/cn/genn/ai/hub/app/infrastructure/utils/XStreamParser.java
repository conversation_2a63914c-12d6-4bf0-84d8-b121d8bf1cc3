package cn.genn.ai.hub.app.infrastructure.utils;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.converters.basic.AbstractSingleValueConverter;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.security.WildcardTypePermission;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description xml解析
 * @date 2025-04-18
 */
public class XStreamParser {

    private static final Map<Class<?>, XStream> XSTREAM_CACHE = new ConcurrentHashMap<>();
    private static final String ALLOWED_PACKAGE = "cn.genn.ai.hub.**"; // 替换为实际包路径

    // CDATA 转换器（支持微信消息格式）
    private static class CDataConverter extends AbstractSingleValueConverter {
        @Override
        public boolean canConvert(Class type) {
            return String.class.isAssignableFrom(type);
        }

        @Override
        public String toString(Object obj) {
            return "<![CDATA[" + super.toString(obj) + "]]>";
        }

        @Override
        public String fromString(String str) {
            // 实际处理CDATA内容的逻辑
            if (str.startsWith("<![CDATA[") && str.endsWith("]]>")) {
                return str.substring(9, str.length() - 3);
            }
            return str;
        }
    }

    // 获取线程安全的 XStream 实例
    private static XStream getXStream(Class<?> clazz) {
        return XSTREAM_CACHE.computeIfAbsent(clazz, k -> {
            XStream xstream = new XStream(new DomDriver());

            // 安全配置（必须）
            xstream.addPermission(new WildcardTypePermission(new String[] {
                ALLOWED_PACKAGE,
                clazz.getName()
            }));

            // 基本配置
            xstream.processAnnotations(clazz);
            xstream.alias("xml", clazz);
            xstream.registerConverter(new CDataConverter());
            xstream.ignoreUnknownElements();
            xstream.autodetectAnnotations(true);

            return xstream;
        });
    }

    // 带异常处理的解析方法
    public static <T> T parseSafely(String xml, Class<T> clazz) throws XmlParseException {
        try {
            return parse(xml, clazz);
        } catch (Exception e) {
            throw new XmlParseException("XML解析失败: " + e.getMessage(), e);
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> void populateInstanceFromXml(String xml, T existingInstance) throws Exception {
        Class<T> clazz = (Class<T>) existingInstance.getClass();
        XStream xstream = getXStream(clazz);
        xstream.processAnnotations(clazz); // 如果有注解需要处理

        // 反序列化XML为新实例
        T newInstance = (T) xstream.fromXML(xml);

        // 复制newInstance中的字段值到existingInstance
        copyProperties(newInstance, existingInstance);
    }

    /**
     * 将source对象的属性值复制到target对象中。
     */
    private static <T> void copyProperties(T source, T target) throws IllegalAccessException {
        java.lang.reflect.Field[] fields = source.getClass().getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(source);
            if (value != null) {
                field.set(target, value);
            }
        }
    }

    // 高性能解析方法
    public static <T> T parse(String xml, Class<T> clazz) {
        return clazz.cast(getXStream(clazz).fromXML(xml));
    }

    // 清理缓存（可选）
    public static void clearCache() {
        XSTREAM_CACHE.clear();
    }

    // 自定义异常类
    public static class XmlParseException extends Exception {
        public XmlParseException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
