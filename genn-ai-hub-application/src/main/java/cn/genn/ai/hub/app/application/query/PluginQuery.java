package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 插件查询参数
 * <AUTHOR>
 */
@Data
@Schema(description = "插件查询参数")
public class PluginQuery  {

    @Schema(description = "插件Key")
    @NotEmpty(message = "插件Key不能为空")
    private String pluginKey;

    @Schema(description = "版本")
    @NotEmpty(message = "版本不能为空")
    private String version;
}
