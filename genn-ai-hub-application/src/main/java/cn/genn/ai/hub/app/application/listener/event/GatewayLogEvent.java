package cn.genn.ai.hub.app.application.listener.event;

import cn.genn.spring.boot.starter.event.rocketmq.model.RocketMQBaseEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GatewayLogEvent extends RocketMQBaseEvent<GatewayLogEvent> {

    private ModelLog log;

    @Override
    protected GatewayLogEvent self() {
        return this;
    }
}
