package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.assembler.FSAssembler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 飞书用户查询相关
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FSUserService {

    @Resource
    private FSAssembler fsaAssembler;

    public Object getUserInfo(Object query) {
        return null;
    }
}

