package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 提示词优化类型枚举
 * @date 2025-05-12
 */
@Getter
@AllArgsConstructor
public enum PromptOptimizeTypeEnum {

    /**
     * 提示词生成
     */
    GENERATE(1, "提示词生成"),
    /**
     * 提示词优化
     */
    OPTIMIZE(2, "提示词优化"),
    /**
     * 提示词根据调试结果优化
     */
    DEBUG_OPTIMIZE(3, "提示词根据调试结果优化"),
    ;

    @EnumValue
    @JsonValue
    private final Integer type;

    private final String desc;
}
