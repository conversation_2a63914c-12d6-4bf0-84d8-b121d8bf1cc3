package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.dto.KbRepoTermDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoTermQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataPO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTermPO;
import cn.genn.ai.hub.app.application.assembler.KbRepoTermAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoTermMapper;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoTermQueryService {

    private final KbRepoTermMapper mapper;
    private final KbRepoTermAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoTermDTO分页对象
     */
    public PageResultDTO<KbRepoTermDTO> page(KbRepoTermQuery query) {
        QueryWrapper<KbRepoTermPO> queryWrapper = buildQuery(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    private QueryWrapper<KbRepoTermPO> buildQuery(KbRepoTermQuery query) {
        QueryWrapper<KbRepoTermPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            .eq(Objects.nonNull(query.getRepoId()), KbRepoTermPO::getRepoId, query.getRepoId())
            .and(StrUtil.isNotBlank(query.getKeyword()),wrapper -> wrapper
                .like(KbRepoTermPO::getName, query.getKeyword())
                .or()
                .like(KbRepoTermPO::getNameAliases, query.getKeyword())
                .or()
                .like(KbRepoTermPO::getDescription, query.getKeyword()))
            .eq(KbRepoTermPO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByDesc(KbRepoTermPO::getCreateTime);
        return queryWrapper;
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoTermDTO
     */
    public KbRepoTermDTO get(IdQuery query) {
        return assembler.PO2DTO(mapper.selectById(query.getId()));
    }
}

