package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum TrainingModelEnum {

    CHUNK("chunk", "直接分段"),
    AUTO("auto", "增强处理"),
    QA("qa", "问答拆分"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    TrainingModelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}

