package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 分配标签
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagsAssignCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "资源ID")
    @NotNull(message = "资源ID不能为空")
    private Long id;

    @Schema(description = "标签ID列表")
    private List<Long> tagIds;
}
