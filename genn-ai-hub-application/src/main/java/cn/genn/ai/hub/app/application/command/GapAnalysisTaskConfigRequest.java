package cn.genn.ai.hub.app.application.command;


import cn.genn.job.xxl.model.task.JobTaskPlan;
import cn.genn.job.xxl.model.task.TaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * GapAnalysisTaskConfigRequest
 *
 * <AUTHOR>
 */
@Data
public class GapAnalysisTaskConfigRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "工作流ID")
    @NotEmpty(message = "工作流id不能为空")
    private String appId;

    @Schema(description = "扫描数据范围-前N日")
    private Integer minusDays;

    @Schema(description = "任务状态,running自动启用,stop暂停")
    private TaskStatusEnum status;

    @Schema(description = "2.固定频率")
    private JobTaskPlan plan;

}

