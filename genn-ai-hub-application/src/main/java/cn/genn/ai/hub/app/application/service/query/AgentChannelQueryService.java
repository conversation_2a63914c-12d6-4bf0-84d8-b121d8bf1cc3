package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.AgentChannelAssembler;
import cn.genn.ai.hub.app.application.dto.AgentChannelDTO;
import cn.genn.ai.hub.app.application.dto.channel.TenantChannelDTO;
import cn.genn.ai.hub.app.application.dto.request.AgentChannelQuery;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentChannelRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentChannelPO;
import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体发布渠道查询服务
 * @date 2025-04-25
 */
@Service
@RequiredArgsConstructor
public class AgentChannelQueryService {

    private final AgentChannelRepositoryImpl agentChannelRepository;

    private final TenantChannelQueryService tenantChannelQueryService;

    private final AgentChannelAssembler agentChannelAssembler;

    public List<AgentChannelDTO> list(AgentChannelQuery agentChannelQuery) {
        // 构建查询条件
        LambdaQueryWrapper<AgentChannelPO> queryWrapper = Wrappers.lambdaQuery(AgentChannelPO.class)
            .eq(AgentChannelPO::getWorkflowId, agentChannelQuery.getWorkflowId())
            .in(!agentChannelQuery.getChannelTypes().isEmpty(), AgentChannelPO::getChannelType, agentChannelQuery.getChannelTypes());
        // 查询关联记录
        List<AgentChannelPO> agentChannelPOS = agentChannelRepository.list(queryWrapper);

        if (agentChannelPOS.isEmpty()) {
            return Collections.emptyList();
        }

        return agentChannelPOS.stream()
            .map(agentChannelPO -> {
                AgentChannelDTO agentChannelDTO = agentChannelAssembler.po2Dto(agentChannelPO);
                if (agentChannelPO.getChannelType() == ChannelTypeEnum.FEISHU || agentChannelPO.getChannelType() == ChannelTypeEnum.WX_MP) {
                    TenantChannelDTO tenantChannelDTO = tenantChannelQueryService.getById(agentChannelPO.getChannelRefId());
                    agentChannelDTO.setChannel(tenantChannelDTO);
                }
                return agentChannelDTO;
            }).toList();
    }
}
