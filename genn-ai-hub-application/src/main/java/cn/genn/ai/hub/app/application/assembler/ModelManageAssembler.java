package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.ModelManageDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelManagePO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModelManageAssembler {

    ModelManageAssembler INSTANCE = Mappers.getMapper(ModelManageAssembler.class);

//    @Mapping(target = "commonConfig", expression = "java(cn.genn.core.utils.jackson.JsonUtils.toJson(model.getCommonConfig()))")
    @Mapping(target = "apiKey", ignore = true)
    @Mapping(target = "commonConfig", expression = "java(cn.hutool.json.JSONUtil.toJsonStr(model.getCommonConfig()))")
    ModelManageDTO convert(ModelManagePO model);

    List<ModelManageDTO> convert(List<ModelManagePO> models);

    @Mapping(target = "commonConfig", ignore = true)
    ModelManagePO convertPo(ModelManageDTO create);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<ModelManageDTO> toPageResult(Page<ModelManagePO> pos);
}

