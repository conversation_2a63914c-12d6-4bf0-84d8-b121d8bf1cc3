package cn.genn.ai.hub.app.infrastructure.utils.vector.milvus;

import io.milvus.v2.common.IndexParam;

/**
 * <AUTHOR>
 */
public class MilvusUtils {

    /**
     * 获取相似度
     *
     * @param distance   距离
     * @param metricType 索引类型
     * @return 相似度
     */
    public static double getResultSimilarity(float distance, IndexParam.MetricType metricType) {
        return (metricType == IndexParam.MetricType.IP || metricType == IndexParam.MetricType.COSINE || metricType == IndexParam.MetricType.BM25) ? distance : (1 - distance);
    }
}

