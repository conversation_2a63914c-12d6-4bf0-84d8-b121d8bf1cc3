package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.TeamQueryTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TeamInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeamInfoQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "租户 id")
    private Long tenantId;

    @Schema(description = "团队名称")
    private String name;

    @Schema(description = "团队查询类型：0-全部，1-我创建的，2-我加入的")
    @NotNull(message = "查询类型不能为空")
    private TeamQueryTypeEnum queryType;

}

