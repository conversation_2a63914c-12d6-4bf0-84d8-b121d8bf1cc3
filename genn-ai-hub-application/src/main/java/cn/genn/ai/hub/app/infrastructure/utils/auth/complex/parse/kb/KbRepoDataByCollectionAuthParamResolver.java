package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb;

import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepCollectionRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbRepoDataByCollectionAuthParamResolver extends AbstractRepoAuthParamResolver {

    private final KbRepCollectionRepositoryImpl collectionRepository;

    @Override
    protected Long getRepoIdById(Long id) {
        KbRepoCollectionPO kbRepoCollectionPO = collectionRepository.getById(id);
        return kbRepoCollectionPO.getRepoId();
    }
    @Override
    protected String idFieldName() {
        return "collectionId";
    }
}
