package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.assembler.GovowOperationDrainageAssembler;
import cn.genn.ai.hub.app.application.command.GovowOperationDrainageCreateCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.GovowOperationDrainageMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.GovowOperationDrainagePO;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 格物运营-用户引流服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GovowOperationDrainageService {

    private final GovowOperationDrainageMapper govowOperationDrainageMapper;
    private final GovowOperationDrainageAssembler govowOperationDrainageAssembler;

    /**
     * 创建
     *
     * @param command 创建命令
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(GovowOperationDrainageCreateCommand command) {
        GovowOperationDrainagePO po = govowOperationDrainageAssembler.createCommand2PO(command);
        po.setDeleted(DeletedTypeEnum.NOT_DELETED);
        govowOperationDrainageMapper.insert(po);
        return po.getId();
    }

    public GovowOperationDrainagePO selectByCachekey(String cacheKey){
        return govowOperationDrainageMapper
            .selectOne(new QueryWrapper<GovowOperationDrainagePO>().lambda().eq(GovowOperationDrainagePO::getCacheKey, cacheKey)
            .last("limit 1"));
    }

}
