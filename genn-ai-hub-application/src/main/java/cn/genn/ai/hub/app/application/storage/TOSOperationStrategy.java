package cn.genn.ai.hub.app.application.storage;

import cn.genn.ai.hub.app.application.assembler.FileStorageAssembler;
import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.CacheConstants;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.core.api.tool.FileStorageConfig;
import cn.genn.ai.hub.core.api.tool.FileStorageTypeEnum;
import cn.genn.core.exception.BusinessException;
import cn.hutool.core.util.StrUtil;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TosClientException;
import com.volcengine.tos.TosServerException;
import com.volcengine.tos.comm.HttpMethod;
import com.volcengine.tos.model.object.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 火山云存储
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TOSOperationStrategy implements StorageStrategy {

    private final GennAIHubProperties properties;
    private final StringRedisTemplate stringRedisTemplate;
    private final FileStorageAssembler fileStorageAssembler;

    @Override
    public boolean saveFile(MultipartFile file, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getTos());
        config.setFileStorageType(FileStorageTypeEnum.TOS);
        return saveFile(file, objectKey, config);
    }

    @Override
    public boolean saveContentTOFile(String content, String objectKey) {
        // 参数校验
        if (StringUtils.isBlank(content) || objectKey == null) {
            throw new IllegalArgumentException("content and objectKey cannot be null");
        }
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getTos());
        validateStorageConfig(config);

        // 构建客户端
        TOSV2 tos = buildTosClient(config);

        try (InputStream inputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8))) {
            // 构建上传请求
            PutObjectInput putObjectInput = new PutObjectInput()
                .setBucket(config.getBucketName())
                .setKey(objectKey)
                .setContent(inputStream);
            // 执行上传
            PutObjectOutput output = tos.putObject(putObjectInput);

            // 记录成功日志
            log.info("Object uploaded successfully. Key: {}, ETag: {}, CRC64: {}",
                objectKey, output.getEtag(), output.getHashCrc64ecma());
            return true;
        } catch (IOException e) {
            log.error("Failed to read file stream. Key: {}", objectKey, e);
        } catch (Exception e) {
            log.error("Unexpected error during upload. Key: {}, error: {}", objectKey, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public InputStream getObject(String objectKey) {
        GennAIFileStorageProperties tosProperties = properties.getTos();
        String endpoint = tosProperties.getEndpoint();
        String region = tosProperties.getRegion();
        String accessKey = tosProperties.getAccessKey();
        String secretKey = tosProperties.getSecretKey();
        String bucketName = tosProperties.getBucketName();

        TOSV2 tos = new TOSV2ClientBuilder().build(region, endpoint, accessKey, secretKey);
        try {
            GetObjectV2Input getObjectInput = new GetObjectV2Input().setBucket(bucketName).setKey(objectKey);
            GetObjectV2Output output = tos.getObject(getObjectInput);
            return output.getContent();
        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.error("putObject failed");
            log.error("Message: " + e.getMessage());
            if (e.getCause() != null) {
                e.getCause().printStackTrace();
            }
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            log.error("putObject failed");
            log.error("StatusCode: " + e.getStatusCode());
            log.error("Code: " + e.getCode());
            log.error("Message: " + e.getMessage());
            log.error("RequestID: " + e.getRequestID());
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            log.error("putObject failed");
            log.error("unexpected exception, message: " + t.getMessage());
        }
        throw new RuntimeException();
    }

    @SneakyThrows
    @Override
    public String preSignedUrlGetObject(String objectKey, String originFileName) {
        return preSignedUrlGetObject(objectKey, originFileName, null, fileStorageAssembler.toFileStorageConfig(properties.getTos()));
    }

    @Override
    public String getCachedPreSignedUrl(String objectKey, String originFileName) {
        String cacheKey = CacheConstants.TOS_PRE_SIGNED_URL_KEY + objectKey;
        if (stringRedisTemplate.hasKey(cacheKey)) {
            return stringRedisTemplate.opsForValue().get(cacheKey);
        } else {
            if (StrUtil.isBlank(originFileName)) {
                originFileName = objectKey;
            }
            String newUrl = preSignedUrlGetObject(objectKey, originFileName);
            // 设置160天过期
            stringRedisTemplate.opsForValue().set(cacheKey, newUrl, 160, TimeUnit.DAYS);
            return newUrl;
        }
    }

    @Override
    public boolean saveFile(MultipartFile file, String objectKey, FileStorageConfig config) {
        // 参数校验
        if (file == null || objectKey == null) {
            throw new IllegalArgumentException("File and objectKey cannot be null");
        }
        validateStorageConfig(config);

        // 构建客户端
        TOSV2 tos = buildTosClient(config);

        try (InputStream inputStream = new BufferedInputStream(file.getInputStream())) {
            // 构建上传请求
            PutObjectInput putObjectInput = new PutObjectInput()
                .setBucket(config.getBucketName())
                .setKey(objectKey)
                .setContent(inputStream)
                .setContentLength(file.getSize());

            // 执行上传
            PutObjectOutput output = tos.putObject(putObjectInput);

            // 记录成功日志
            log.info("Object uploaded successfully. Key: {}, ETag: {}, CRC64: {}",
                objectKey, output.getEtag(), output.getHashCrc64ecma());
            return true;
        } catch (IOException e) {
            log.error("Failed to read file stream. Key: {}", objectKey, e);
        } catch (Exception e) {
            log.error("Unexpected error during upload. Key: {}, error: {}", objectKey, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public String preSignedUrlGetObject(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
        // 参数校验
        if (objectKey == null) {
            throw new IllegalArgumentException("objectKey cannot be null");
        }
        validateStorageConfig(config);

        Map<String, String> header = new HashMap<>();
        if (StrUtil.isNotBlank(originFileName)) {
            header.put("response-content-disposition", String.format("attachment;filename=%s", URLEncoder.encode(originFileName, StandardCharsets.UTF_8)));
        }

        // 构建客户端
        TOSV2 tos = buildTosClient(config);

        try {
            PreSignedURLInput input = new PreSignedURLInput().setBucket(config.getBucketName()).setKey(objectKey)
                .setHttpMethod(HttpMethod.GET).setExpires(Optional.ofNullable(expireSeconds).orElse(180 * 24 * 3600L))
                .setQuery(header);
            PreSignedURLOutput output = tos.preSignedURL(input);
            return output.getSignedUrl();
        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.error("preSignedURL failed, message={}", e.getMessage(), e);
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        return null;
    }

    @Override
    public String preSignedUrlGetObjectByCustomDomain(String objectKey, String originFileName, Long expireSeconds, FileStorageConfig config) {
        // 参数校验
        if (objectKey == null) {
            throw new IllegalArgumentException("objectKey cannot be null");
        }
        validateStorageConfig(config);

        // 构建客户端
        TOSV2 tos = buildTosClient(config);

        String domain = config.getCustomDomain();
        try {
            PreSignedURLInput input = new PreSignedURLInput().setKey(objectKey)
                .setCustomDomain(true)
                .setAlternativeEndpoint(domain)
                .setHttpMethod(HttpMethod.GET).setExpires(Optional.ofNullable(expireSeconds).orElse(180 * 24 * 3600L));
            PreSignedURLOutput output = tos.preSignedURL(input);
            return output.getSignedUrl();
        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.error("preSignedURL failed, message={}", e.getMessage(), e);
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        return null;
    }

    // 客户端构建方法
    private TOSV2 buildTosClient(FileStorageConfig config) {
        return new TOSV2ClientBuilder().build(
            config.getRegion(),
            config.getEndpoint(),
            config.getAccessKey(),
            config.getSecretKey()
        );
    }

    // 配置校验方法
    private void validateStorageConfig(FileStorageConfig config) {
        if (config == null) {
            throw new BusinessException(MessageCode.INVALID_STORAGE_CONFIG);
        }

        if (StrUtil.hasBlank(
            config.getEndpoint(),
            config.getRegion(),
            config.getAccessKey(),
            config.getSecretKey(),
            config.getBucketName())) {
            log.error("Incomplete TOS configuration. Endpoint: {}, Region: {}, Bucket: {}",
                config.getEndpoint(), config.getRegion(), config.getBucketName());
            throw new BusinessException(MessageCode.INVALID_STORAGE_CONFIG);
        }
    }

    @Override
    public boolean saveOutPutStream(ByteArrayOutputStream content, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getTos());
        validateStorageConfig(config);

        // 构建客户端
        TOSV2 tos = buildTosClient(config);
        byte[] byteArray = content.toByteArray();
        try (InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(byteArray))) {
            // 构建上传请求
            PutObjectInput putObjectInput = new PutObjectInput()
                .setBucket(config.getBucketName())
                .setKey(objectKey)
                .setContent(inputStream)
                .setContentLength(byteArray.length);

            // 执行上传
            PutObjectOutput output = tos.putObject(putObjectInput);

            // 记录成功日志
            log.info("Object uploaded successfully. Key: {}, ETag: {}, CRC64: {}",
                objectKey, output.getEtag(), output.getHashCrc64ecma());
            return true;
        } catch (IOException e) {
            log.error("Failed to read file stream. Key: {}", objectKey, e);
        } catch (Exception e) {
            log.error("Unexpected error during upload. Key: {}, error: {}", objectKey, e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean saveByteArray(byte[] byteArray, String objectKey) {
        FileStorageConfig config = fileStorageAssembler.toFileStorageConfig(properties.getTos());
        validateStorageConfig(config);
        // 构建客户端
        TOSV2 tos = buildTosClient(config);
        try (InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(byteArray))) {
            // 构建上传请求
            PutObjectInput putObjectInput = new PutObjectInput()
                .setBucket(config.getBucketName())
                .setKey(objectKey)
                .setContent(inputStream)
                .setContentLength(byteArray.length);

            // 执行上传
            PutObjectOutput output = tos.putObject(putObjectInput);

            // 记录成功日志
            log.info("Object uploaded successfully. Key: {}, ETag: {}, CRC64: {}",
                objectKey, output.getEtag(), output.getHashCrc64ecma());
            return true;
        } catch (IOException e) {
            log.error("Failed to read file stream. Key: {}", objectKey, e);
        } catch (Exception e) {
            log.error("Unexpected error during upload. Key: {}, error: {}", objectKey, e.getMessage(), e);
        }
        return false;
    }
}
