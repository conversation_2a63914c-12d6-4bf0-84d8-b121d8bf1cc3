package cn.genn.ai.hub.app.infrastructure.config.auth;

import cn.genn.ai.hub.app.domain.auth.model.valobj.FieldWhereExpression;
import cn.genn.ai.hub.app.infrastructure.utils.auth.SqlUtils;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import net.sf.jsqlparser.expression.Expression;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;

import java.sql.Connection;
import java.util.Objects;

import static cn.genn.ai.hub.app.infrastructure.config.auth.AuthAspect.FIELD_WHERE_EXPRESSION_THREAD_LOCAL;


/**
 * <AUTHOR>
 */
public class AuthSqlInterceptor implements InnerInterceptor {

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        FieldWhereExpression fieldWhereExpression = FIELD_WHERE_EXPRESSION_THREAD_LOCAL.get();
        Boolean authInject = AuthSqlInjectAspect.AUTH_SQL_INJECT_HOLDER.get();
        if (fieldWhereExpression == null || authInject == null || Objects.equals(false, authInject)) {
            return;
        }
        Expression additionalCondition = SqlUtils.toWhereExpression(fieldWhereExpression);
        if (additionalCondition != null) {
            PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
            BoundSql boundSql = mpSh.boundSql();
            String originalSql = boundSql.getSql();
            String newSql = SqlUtils.addWhereExpression(originalSql, additionalCondition);
            PluginUtils.mpBoundSql(boundSql).sql(newSql);
        }
    }
}
