package cn.genn.ai.hub.app.application.processor;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import cn.idev.excel.metadata.data.ReadCellData;
import cn.idev.excel.util.ListUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class NoModelDataListener extends AnalysisEventListener<Map<Integer, String>> {
    private static final int BATCH_COUNT = 5;
    private List<Map<Integer, String>> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    private List<String> headers = new ArrayList<>();


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到表头数据: {}", JsonUtils.toJson(headMap));
        // 显式对 headMap 的 key 进行排序后放入 headers 列表
        headMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                headers.add(entry.getValue().getStringValue());
            });
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        log.info("解析到一条数据: {}", JsonUtils.toJson(data));
        cachedDataList.add(data);
//        if (cachedDataList.size() >= BATCH_COUNT) {
//            saveData();
//            cachedDataList.clear();
//        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("读取完成，共读取了 " + cachedDataList.size() + " 条数据");
    }

    private void saveData() {
        log.info("存储 {} 条数据", cachedDataList.size());
    }

    public List<Map<Integer, String>> getDataList() {
        return cachedDataList;
    }

    public List<String> getHeader() {
        return headers;
    }
}
