package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.ImportTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 导入命令
 * @date 2025-05-12
 */
@Data
public class ImportCommand {

    @Schema(description = "团队空间id")
    private Long teamId;

    @Schema(description = "导入类型: 1-提示词")
    private ImportTypeEnum importType;

    @Schema(description = "导入json")
    private String rawJson;
}
