package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.api.UserAbilityImpl;
import cn.genn.ai.hub.app.application.assembler.TeamInfoAssembler;
import cn.genn.ai.hub.app.application.command.*;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommonSearch;
import cn.genn.ai.hub.app.domain.auth.model.event.AuthCommonModifyEvent;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.auth.service.AuthCommonService;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TeamInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TeamUserRefRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.TeamInfoPO;
import cn.genn.ai.hub.app.infrastructure.utils.auth.AuthUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.KVStruct;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TeamInfoActionService {

    private final TeamInfoRepositoryImpl teamInfoRepository;
    private final TeamInfoAssembler teamInfoAssembler;
    private final TeamUserRefRepositoryImpl teamUserRefRepository;
    private final UserAbilityImpl userAbility;
    private final SpringEventPublish springEventPublish;
    private final AuthCommonService authCommonService;
    private final AgentInfoMapper agentInfoMapper;
    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;

    /**
     * 创建团队
     *
     * @param command 创建团队命令
     * @return 团队ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(TeamInfoCreateCommand command) {
        TeamInfoPO teamInfoPO = teamInfoAssembler.command2PO(command);
        teamInfoPO.setOwnerUserId(CurrentUserHolder.getUserId());
        teamInfoPO.setOwnerUserName(CurrentUserHolder.getUserName());
        Long teamId = teamInfoRepository.createTeam(teamInfoPO);

        // 创建团队后，自动将创建者添加为团队成员
        Long currentUserId = CurrentUserHolder.getUserId();
        teamUserRefRepository.addTeamMember(teamId, currentUserId);

        springEventPublish.publish(AuthCommonModifyEvent.buildAddEvent(Collections.singletonList(AuthUtils.buildUserTeamAuth(teamId, currentUserId, ActionType.MANAGER)), this));
        return teamId;
    }

    /**
     * 更新团队
     *
     * @param command 更新团队命令
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(TeamInfoUpdateCommand command) {
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(command.getId());
        if (existingTeam == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        // 更新团队
        TeamInfoPO teamInfoPO = teamInfoAssembler.command2PO(command);
        return teamInfoRepository.updateTeam(teamInfoPO);
    }

    /**
     * 删除团队
     *
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(IdCommand command) {
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(command.getId());
        if (existingTeam == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        //判断团队下是否有成员
        if (teamUserRefRepository.countTeamMembers(command.getId()) > 1) {
            throw new BusinessException(MessageCode.TEAM_HAS_MEMBERS);
        }
        //判断团队下是否有资源
        Long agentCount = agentInfoMapper.selectCount(Wrappers.<AgentInfoPO>lambdaQuery().eq(AgentInfoPO::getTeamId, command.getId()).eq(AgentInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED));
        if (agentCount > 0) {
            throw new BusinessException(MessageCode.TEAM_HAS_RESOURCE);
        }
        Long kbCount = kbRepoBaseInfoMapper.selectCount(Wrappers.<KbRepoBaseInfoPO>lambdaQuery().eq(KbRepoBaseInfoPO::getTeamId, command.getId()).eq(KbRepoBaseInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED));
        if (kbCount > 0) {
            throw new BusinessException(MessageCode.TEAM_HAS_RESOURCE);
        }
        //删除权限
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(AuthUtils.buildUserTeamUniqueCriteria(command.getId(), CurrentUserHolder.getUserId()), this));
        return teamInfoRepository.deleteTeam(command.getId());
    }

    /**
     * 邀请用户加入团队
     *
     * @param command 团队成员邀请命令
     * @return 是否邀请成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean inviteMembers(TeamMemberInviteCommand command) {
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(command.getTeamId());
        if (existingTeam == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }

        // 检查当前用户是否是团队创建者或成员
        Long currentUserId = CurrentUserHolder.getUserId();
        if (!currentUserId.equals(existingTeam.getOwnerUserId())) {
            throw new CheckException(MessageCode.ONLY_CREATOR_CAN_INVITE);
        }

        //检查用户是否存在
        List<UpmUserDTO> upmUserDTOS = userAbility.listByIds(command.getUserIds());
        if (upmUserDTOS.size() != command.getUserIds().size()) {
            throw new CheckException(MessageCode.USER_NOT_EXIST);
        }

        //检查用户是否合法
        if (!userAbility.checkUserValid(upmUserDTOS)) {
            throw new CheckException(MessageCode.USER_NOT_VALID);
        }

        List<AuthCommon> authCommonList = command.getUserIds().stream()
            .map(userId -> AuthUtils.buildUserTeamAuth(command.getTeamId(), userId, ActionType.VIEW)).collect(Collectors.toList());
        springEventPublish.publish(AuthCommonModifyEvent.buildAddEvent(authCommonList, this));

        // 批量添加团队成员
        return teamUserRefRepository.batchAddTeamMember(command.getTeamId(), command.getUserIds());
    }

    /**
     * 退出团队
     *
     * @param query 团队ID查询
     * @return 是否退出成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean leave(IdQuery query) {
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(query.getId());
        if (existingTeam == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }

        // 创建者不能退出团队
        Long currentUserId = CurrentUserHolder.getUserId();
        if (currentUserId.equals(existingTeam.getOwnerUserId())) {
            throw new BusinessException(MessageCode.CREATOR_CANNOT_LEAVE);
        }

        deleteRelationAuth(currentUserId, existingTeam.getId());
        return teamUserRefRepository.removeTeamMember(query.getId(), currentUserId);
    }

    /**
     * 踢出团队成员
     *
     * @param command 团队成员踢出命令
     * @return 是否踢出成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean kickoutMember(TeamMemberKickoutCommand command) {
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(command.getTeamId());
        if (existingTeam == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }

        // 检查当前用户是否是团队创建者
        Long currentUserId = CurrentUserHolder.getUserId();
        if (!currentUserId.equals(existingTeam.getOwnerUserId())) {
            throw new BusinessException(MessageCode.ONLY_CREATOR_CAN_KICKOUT);
        }

        // 不能踢出自己
        if (command.getUserId().equals(currentUserId)) {
            throw new BusinessException(MessageCode.CANNOT_KICKOUT_SELF);
        }

        // 检查要踢出的用户是否是团队成员
        if (!teamUserRefRepository.isMember(command.getTeamId(), command.getUserId())) {
            throw new BusinessException(MessageCode.USER_NOT_TEAM_MEMBER);
        }

        deleteRelationAuth(command.getUserId(), command.getTeamId());

        // 移除团队成员
        return teamUserRefRepository.removeTeamMember(command.getTeamId(), command.getUserId());
    }

    /**
     * 转让团队
     *
     * @param command 团队转让命令
     * @return 是否转让成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferTeam(TeamMemberTransferCommand command) {
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(command.getTeamId());
        if (existingTeam == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }

        // 检查当前用户是否是团队创建者
        Long currentUserId = CurrentUserHolder.getUserId();
        if (!currentUserId.equals(existingTeam.getOwnerUserId())) {
            throw new BusinessException(MessageCode.ONLY_CREATOR_CAN_TRANSFER);
        }

        // 不能转让给自己
        if (command.getTargetUserId().equals(currentUserId)) {
            throw new BusinessException(MessageCode.CANNOT_TRANSFER_TO_SELF);
        }

        // 检查转让目标是否是团队成员
        if (!teamUserRefRepository.isMember(command.getTeamId(), command.getTargetUserId())) {
            throw new BusinessException(MessageCode.TRANSFER_TARGET_NOT_MEMBER);
        }

        // 更新团队创建者
        UpmUserDTO upmUserDTO = userAbility.getById(command.getTargetUserId());
        if (upmUserDTO == null) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }

        transferTeamResourceAuth(command.getTeamId(), currentUserId, command.getTargetUserId());

        return teamInfoRepository.updateTeamCreator(command.getTeamId(), command.getTargetUserId(), upmUserDTO.getUsername());
    }

    /**
     * 转移资源到团队
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferResource(ResourceTransferCommand command) {
        Long teamId = command.getTeamId();
        // 检查团队是否存在
        TeamInfoPO existingTeam = teamInfoRepository.getTeamById(teamId);
        if (existingTeam == null) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
        }
        //检查资源是否合规
        List<KVStruct<String, ResourceType>> resourceIds = command.getResources();
        List<String> agentWorkflowIds = new ArrayList<>();
        List<Long> kbIds = new ArrayList<>();
        resourceIds.forEach(resource -> {
            if (ResourceType.AGENT.equals(resource.getValue())) {
                agentWorkflowIds.add(resource.getKey());
            } else if (ResourceType.KB_REPO.equals(resource.getValue())) {
                kbIds.add(Long.parseLong(resource.getKey()));
            }
        });
        List<AuthCommon> authCommonList = new ArrayList<>();
        if (CollUtil.isNotEmpty(agentWorkflowIds)) {
            // 查询智能体,传入的是workflowId
            List<AgentInfoPO> agentInfoPOS = agentInfoMapper.selectList(Wrappers.<AgentInfoPO>lambdaQuery().in(AgentInfoPO::getWorkflowId, agentWorkflowIds));
            agentInfoPOS.forEach(agentInfo -> {
                if (agentInfo.getTeamId() != null && !agentInfo.getTeamId().equals(teamId)) {
                    throw new BusinessException(MessageCode.INVALID_RESOURCE);
                }
                if (!agentInfo.getCreateUserId().equals(CurrentUserHolder.getUserId())) {
                    throw new BusinessException(MessageCode.AUTH_API_AUTH_FAILED);
                }
            });
            List<Long> agentIds = agentInfoPOS.stream().map(AgentInfoPO::getId).collect(Collectors.toList());
            agentInfoMapper.update(Wrappers.<AgentInfoPO>lambdaUpdate()
                .set(AgentInfoPO::getTeamId, teamId)
                .in(AgentInfoPO::getId, agentIds));
        }
        if (CollUtil.isNotEmpty(kbIds)) {
            // 查询知识库,传入的是知识库id
            List<KbRepoBaseInfoPO> kbRepoBaseInfoPOS = kbRepoBaseInfoMapper.selectByIds(kbIds);
            kbRepoBaseInfoPOS.forEach(kbRepoBaseInfo -> {
                if (kbRepoBaseInfo.getTeamId() != null && !kbRepoBaseInfo.getTeamId().equals(teamId)) {
                    throw new BusinessException(MessageCode.INVALID_RESOURCE);
                }
                if (!kbRepoBaseInfo.getCreateUserId().equals(CurrentUserHolder.getUserId())) {
                    throw new BusinessException(MessageCode.AUTH_API_AUTH_FAILED);
                }
            });
            kbRepoBaseInfoMapper.update(Wrappers.<KbRepoBaseInfoPO>lambdaUpdate()
                .set(KbRepoBaseInfoPO::getTeamId, teamId)
                .in(KbRepoBaseInfoPO::getId, kbIds));
        }
        if (CollUtil.isNotEmpty(authCommonList)) {
            springEventPublish.publish(
                AuthCommonModifyEvent.buildAddEvent(authCommonList, this)
            );
        }
        return true;
    }

    /**
     * 转让团队资源权限
     *
     * @param teamId        团队ID
     * @param currentUserId 当前用户ID
     * @param targetUserId  目标用户ID
     */
    private void transferTeamResourceAuth(Long teamId, Long currentUserId, Long targetUserId) {
        springEventPublish.publish(
            AuthCommonModifyEvent.buildModifyEvent(AuthUtils.buildUserTeamUniqueCriteria(teamId, currentUserId),
                Collections.singletonList(AuthUtils.buildUserTeamAuth(teamId, currentUserId, ActionType.VIEW)), this)
        );
        springEventPublish.publish(AuthCommonModifyEvent.buildAddEvent(Collections.singletonList(AuthUtils.buildUserTeamAuth(teamId, targetUserId, ActionType.MANAGER)), this));
    }


    private void deleteRelationAuth(Long userId, Long teamId) {
        // 删除团队资源权限
        springEventPublish.publish(
            AuthCommonModifyEvent.buildDeleteEvent(AuthUtils.buildUserTeamUniqueCriteria(teamId, userId), this)
        );

        // 需删除的权限集合
        List<AuthCommon> needDelAuth = new ArrayList<>();

        // 封装：按类型清理权限
        needDelAuth.addAll(findOutTeamResourceAuthToDelete(
            userId,
            teamId,
            ResourceType.AGENT,
            authCommonService,
            agentInfoMapper::selectByIds,
            AgentInfoPO::getTeamId,
            AgentInfoPO::getId
        ));

        needDelAuth.addAll(findOutTeamResourceAuthToDelete(
            userId,
            teamId,
            ResourceType.KB_REPO,
            authCommonService,
            kbRepoBaseInfoMapper::selectByIds,
            KbRepoBaseInfoPO::getTeamId,
            KbRepoBaseInfoPO::getId
        ));

        if (!needDelAuth.isEmpty()) {
            authCommonService.deleteByIds(
                needDelAuth.stream().map(AuthCommon::getId).toList()
            );
        }
    }

    /**
     * 通用过滤方法：找到“非本团队”的资源权限
     */
    private <T> List<AuthCommon> findOutTeamResourceAuthToDelete(
        Long userId,
        Long teamId,
        ResourceType resourceType,
        AuthCommonService authCommonService,
        Function<List<Long>, List<T>> poFetcher,
        Function<T, Long> teamIdGetter,
        Function<T, Long> poIdGetter) {
        List<AuthCommon> auths = authCommonService.list(
            AuthCommonSearch.builder()
                .subjectType(SubjectType.USER)
                .subjectKeys(List.of(userId.toString()))
                .resourceType(List.of(resourceType))
                .actionType(List.of(ActionType.EDIT, ActionType.VIEW))
                .build()
        );
        if (auths.isEmpty()) return List.of();

        // 查询资源对象PO
        List<Long> resourceIds = auths.stream()
            .map(AuthCommon::getResourceKey)
            .map(Long::parseLong)
            .distinct()
            .toList();

        List<T> pos = poFetcher.apply(resourceIds);

        // 只保留本团队资源ID集合
        Set<String> teamResourceKeys = pos.stream()
            .filter(po -> teamId.equals(teamIdGetter.apply(po)))
            .map(po -> String.valueOf(poIdGetter.apply(po)))
            .collect(Collectors.toSet());

        // 筛选出属于本团队的授权
        return auths.stream()
            .filter(auth -> teamResourceKeys.contains(auth.getResourceKey()))
            .toList();
    }

}
