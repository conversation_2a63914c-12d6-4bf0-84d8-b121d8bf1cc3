package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.FeedbackSourceSystemEnum;
import cn.genn.ai.hub.app.application.enums.FeedbackTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 智能体反馈创建命令
 * @date 2025-07-02
 */
@Data
public class AgentFeedbackCreateCommand {

    @Schema(description = "工作流id")
    private String workflowId;

    @Schema(description = "会话id")
    private String chatId;

    @Schema(description = "单论对话id")
    private String taskId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "反馈类型: LIKE (赞)、DISLIKE (踩)、WORD_MARK（划词反馈）")
    private FeedbackTypeEnum feedbackType;

    @Schema(description = "反馈标签")
    private String feedbackTag;

    @Schema(description = "划词文本, 仅反馈类型为划词反馈时有值")
    private String wordMarkText;

    @Schema(description = "用户提供的具体原因")
    private String reason;

    @Schema(description = "反馈来源系统, 1: cerebro, 2: 企业大脑")
    private FeedbackSourceSystemEnum sourceSystem;

    @Schema(description = "创建用户ID（当为外部系统时，为外部系统的用户唯一标识）")
    private Long createUserId;

    @Schema(description = "创建用户名")
    private String createUserName;
}
