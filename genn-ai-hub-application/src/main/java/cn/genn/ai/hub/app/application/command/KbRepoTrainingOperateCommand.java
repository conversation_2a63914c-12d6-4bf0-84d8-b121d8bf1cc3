package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTaskTypeEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoTraining操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoTrainingOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "知识库ID，标识训练任务所属的知识库（关联 repo 表）")
    private Long repoId;

    @Schema(description = "集合ID，标识训练任务关联的集合（关联 collection 表）")
    private Long collectionId;

    @Schema(description = "数据ID，关联原始数据条目（data 表的 id）")
    private Long dataId;

    @Schema(description = "训练类型, file_parse:FILE_PARSE-文件解析, analyzer:ANALYZER-内容分词,segment:SEGMENT-内容分块,index:INDEX-数据索引")
    private TrainingTaskTypeEnum trainingTaskType;

    @Schema(description = "训练模式, chunk:CHUNK-直接分段, auto:AUTO-增强处理,qa:QA-问答拆分")
    private TrainingModelEnum trainingModel;

    @Schema(description = "模型名称，使用的 AI 模型（如 gpt-4）")
    private String aiModel;

    @Schema(description = "权重，控制训练任务的优先级（数值越高越优先）")
    private Integer weight;

    @Schema(description = "重试次数，任务失败时的最大重试次数（默认 5 次）")
    private Integer retryCount;

    @Schema(description = "锁定时间，防止并发处理的排他锁时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lockTime;

    @Schema(description = "任务状态, pending:PENDING-排队中,processing:PROCESSING-处理中,completed:COMPLETED-已完成,failed:FAILED-失败")
    private TaskStatusEnum taskStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

