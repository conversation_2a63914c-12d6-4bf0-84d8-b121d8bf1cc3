package cn.genn.ai.hub.app.application.service.video.protocol;

import lombok.Getter;

@Getter
public enum MessageFlag {
    MSG_FLAG_NO_SEQ((byte) 0),           // 无序列号的非终止包
    MSG_FLAG_POSITIVE_SEQ((byte) 0b1),   // 序列号大于0的非终止包
    MSG_FLAG_LAST_NO_SEQ((byte) 0b10),   // 无序列号的终止包
    MSG_FLAG_NEGATIVE_SEQ((byte) 0b11),  // 序列号小于0的终止包
    MSG_FLAG_WITH_EVENT((byte) 0b100);   // 包含事件号的包

    private final byte value;

    MessageFlag(byte value) {
        this.value = value;
    }

    public static MessageFlag fromValue(int value) {
        for (MessageFlag flag : MessageFlag.values()) {
            if (flag.value == value) {
                return flag;
            }
        }
        throw new IllegalArgumentException("Unknown MessageFlag value: " + value);
    }
}
