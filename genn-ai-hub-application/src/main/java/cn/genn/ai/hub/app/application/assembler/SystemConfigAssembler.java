package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.infrastructure.repository.po.SystemConfigPO;
import cn.genn.ai.hub.core.api.tool.SystemConfig;
import cn.genn.core.utils.jackson.JsonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SystemConfigAssembler{

    SystemConfigAssembler INSTANCE = Mappers.getMapper(SystemConfigAssembler.class);

    @Mapping(target = "extra", expression = "java(cn.genn.core.utils.jackson.JsonUtils.parseToObjectMap(systemConfigPO.getExtra()))")
    SystemConfig assemble(SystemConfigPO systemConfigPO);

    List<SystemConfig> assemble(List<SystemConfigPO> systemConfigPOS);

}

