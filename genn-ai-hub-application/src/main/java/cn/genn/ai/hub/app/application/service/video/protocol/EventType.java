package cn.genn.ai.hub.app.application.service.video.protocol;

import lombok.Getter;

@Getter
public enum EventType {
    // 默认事件
    TYPE_NONE(0),

    // 上行Connection事件 (1-49)
    TYPE_START_CONNECTION(1),
    TYPE_START_TASK(1),
    TYPE_FINISH_CONNECTION(2),
    TYPE_FINISH_TASK(2),

    // 下行Connection事件 (50-99)
    TYPE_CONNECTION_STARTED(50),
    TYPE_TASK_STARTED(50),
    TYPE_CONNECTION_FAILED(51),
    TYPE_TASK_FAILED(51),
    TYPE_CONNECTION_FINISHED(52),
    TYPE_TASK_FINISHED(52),

    // 上行Session事件 (100-149)
    TYPE_START_SESSION(100),
    TYPE_CANCEL_SESSION(101),
    TYPE_FINISH_SESSION(102),

    // 下行Session事件 (150-199)
    TYPE_SESSION_STARTED(150),
    TYPE_SESSION_CANCELED(151),
    TYPE_SESSION_FINISHED(152),
    TYPE_SESSION_FAILED(153),
    TYPE_USAGE_RESPONSE(154),
    TYPE_CHARGE_DATA(154),

    // 上行通用事件 (200-249)
    TYPE_TASK_REQUEST(200),
    TYPE_UPDATE_CONFIG(201),

    // 下行通用事件 (250-299)
    TYPE_AUDIO_MUTED(250),

    // 上行TTS事件 (300-349)
    TYPE_SAY_HELLO(300),

    // 下行TTS事件 (350-399)
    TYPE_TTS_SENTENCE_START(350),
    TYPE_TTS_SENTENCE_END(351),
    TYPE_TTS_RESPONSE(352),
    TYPE_TTS_ENDED(359),
    TYPE_PODCAST_ROUND_START(360),
    TYPE_PODCAST_ROUND_RESPONSE(361),
    TYPE_PODCAST_ROUND_END(362),

    // 下行ASR事件 (450-499)
    TYPE_ASR_INFO(450),
    TYPE_ASR_RESPONSE(451),
    TYPE_ASR_ENDED(459),

    // 上行对话事件 (500-549)
    TYPE_CHAT_TTS_TEXT(500),

    // 下行对话事件 (550-599)
    TYPE_CHAT_RESPONSE(550),
    TYPE_CHAT_ENDED(559),

    // 字幕事件 (650-699)
    TYPE_SOURCE_SUBTITLE_START(650),
    TYPE_SOURCE_SUBTITLE_RESPONSE(651),
    TYPE_SOURCE_SUBTITLE_END(652),
    TYPE_TRANSLATION_SUBTITLE_START(653),
    TYPE_TRANSLATION_SUBTITLE_RESPONSE(654),
    TYPE_TRANSLATION_SUBTITLE_END(655);

    private final int value;

    EventType(int value) {
        this.value = value;
    }

    public static EventType fromValue(int value) {
        for (EventType type : EventType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown EventType value: " + value);
    }
}
