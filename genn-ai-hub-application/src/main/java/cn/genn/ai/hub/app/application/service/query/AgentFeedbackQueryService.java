package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.AgentFeedbackAssembler;
import cn.genn.ai.hub.app.application.dto.AgentFeedbackAnalysisDTO;
import cn.genn.ai.hub.app.application.dto.AgentFeedbackDTO;
import cn.genn.ai.hub.app.application.dto.AgentFeedbackListDTO;
import cn.genn.ai.hub.app.application.dto.GroupAgentFeedbackDTO;
import cn.genn.ai.hub.app.application.enums.FeedbackTypeEnum;
import cn.genn.ai.hub.app.application.query.AgentFeedbackAnalysisQuery;
import cn.genn.ai.hub.app.application.query.AgentFeedbackQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.AgentFeedbackMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentFeedbackPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 智能体反馈查询服务
 * @date 2025-07-02
 */
@Service
@RequiredArgsConstructor
public class AgentFeedbackQueryService {

    private final AgentFeedbackMapper agentFeedbackMapper;
    private final QuestionInfoMapper questionInfoMapper;

    private final AgentFeedbackAssembler agentFeedbackAssembler;

    @IgnoreTenant
    public List<GroupAgentFeedbackDTO> list(List<AgentFeedbackQuery> queryList) {
        List<GroupAgentFeedbackDTO> result = new ArrayList<>();
        for (AgentFeedbackQuery query : queryList) {
            List<AgentFeedbackPO> agentFeedbackPOS = agentFeedbackMapper.selectList(
                Wrappers.lambdaQuery(AgentFeedbackPO.class)
                    .eq(query.getWorkflowId() != null, AgentFeedbackPO::getWorkflowId, query.getWorkflowId())
                    .eq(query.getChatId() != null, AgentFeedbackPO::getChatId, query.getChatId())
                    .eq(query.getTaskId() != null, AgentFeedbackPO::getTaskId, query.getTaskId())
                    .eq(query.getSourceSystem() != null, AgentFeedbackPO::getSourceSystem, query.getSourceSystem())
                    .eq(query.getOnlyMyFeedback() != null && query.getOnlyMyFeedback(), AgentFeedbackPO::getCreateUserId, CurrentUserHolder.getUserId())
            );
            GroupAgentFeedbackDTO group = new GroupAgentFeedbackDTO();
            group.setWorkflowId(query.getWorkflowId());
            group.setChatId(query.getChatId());
            group.setTaskId(query.getTaskId());
            group.setFeedbackList(agentFeedbackAssembler.toDTOList(agentFeedbackPOS));
            result.add(group);
        }
        return result;
    }

    public List<AgentFeedbackListDTO> listByChatId(List<AgentFeedbackQuery> queryList) {
        List<AgentFeedbackListDTO> result = new ArrayList<>();
        for (AgentFeedbackQuery query : queryList) {
            List<AgentFeedbackListDTO> list = this.listByChatId(query);
            if (list != null && !list.isEmpty()) {
                result.addAll(list);
            }
        }
        return result;
    }

    public List<AgentFeedbackListDTO> listByChatId(AgentFeedbackQuery query) {
        List<AgentFeedbackPO> agentFeedbackPOS = agentFeedbackMapper.selectList(
            Wrappers.lambdaQuery(AgentFeedbackPO.class)
                .eq(query.getWorkflowId() != null, AgentFeedbackPO::getWorkflowId, query.getWorkflowId())
                .eq(query.getChatId() != null, AgentFeedbackPO::getChatId, query.getChatId())
                .orderByDesc(AgentFeedbackPO::getCreateTime));
        // 1. 按 workflowId, chatId 分组
        Map<String, Map<String, List<AgentFeedbackPO>>> grouped = agentFeedbackPOS.stream()
            .collect(Collectors.groupingBy(
                AgentFeedbackPO::getWorkflowId,
                Collectors.groupingBy(AgentFeedbackPO::getChatId)
            ));

        // 2. 统计 like/dislike 数量，3. 划词反馈按 tag 分组统计
        List<AgentFeedbackListDTO> result = new ArrayList<>();
        grouped.forEach((workflowId, chatMap) -> {
            chatMap.forEach((chatId, feedbackList) -> {
                long likeCount = feedbackList.stream().filter(f -> FeedbackTypeEnum.LIKE.equals(f.getFeedbackType())).count();
                long dislikeCount = feedbackList.stream().filter(f -> FeedbackTypeEnum.DISLIKE.equals(f.getFeedbackType())).count();

                // 划词反馈分组统计
                List<AgentFeedbackListDTO.WordMarkFeedbackStat> wordMarkFeedbackList = feedbackList.stream()
                    .filter(f -> FeedbackTypeEnum.WORD_MARK.equals(f.getFeedbackType()) && f.getFeedbackTag() != null)
                    .collect(Collectors.groupingBy(
                        AgentFeedbackPO::getFeedbackTag
                    ))
                    .entrySet().stream()
                    .map(entry -> {
                        AgentFeedbackListDTO.WordMarkFeedbackStat wordMarkFeedback = new AgentFeedbackListDTO.WordMarkFeedbackStat();
                        wordMarkFeedback.setFeedbackTag(entry.getKey());
                        wordMarkFeedback.setCount((long) entry.getValue().size());
                        wordMarkFeedback.setWordMarkTexts(
                            entry.getValue().stream()
                                .map(po -> {
                                    AgentFeedbackListDTO.WordMarkFeedback feedback = new AgentFeedbackListDTO.WordMarkFeedback();
                                    feedback.setCreateTime(po.getCreateTime());
                                    feedback.setWordMarkText(po.getWordMarkText());
                                    return feedback;
                                })
                                .collect(Collectors.toList())
                        );
                        return wordMarkFeedback;
                    })
                    .collect(Collectors.toList());

                AgentFeedbackListDTO dto = new AgentFeedbackListDTO();
                dto.setWorkflowId(workflowId);
                dto.setChatId(chatId);
                dto.setLikeCount(likeCount);
                dto.setDislikeCount(dislikeCount);
                dto.setWordMarkFeedbackList(wordMarkFeedbackList);
                result.add(dto);
            });
        });
        return result;
    }

    public AgentFeedbackAnalysisDTO analysis(AgentFeedbackAnalysisQuery query) {
        // 如果 source 为空或空集合，直接返回全为0的统计
        if (query.getSource() != null && query.getSource().isEmpty()) {
            return buildZeroStat(query);
        }

        List<AgentFeedbackPO> feedbackList = agentFeedbackMapper.selectList(
            Wrappers.lambdaQuery(AgentFeedbackPO.class)
                .eq(query.getWorkflowId() != null, AgentFeedbackPO::getWorkflowId, query.getWorkflowId())
                .in(query.getSource() != null, AgentFeedbackPO::getSource, query.getSource())
                .ge(query.getCreateTimeLeft() != null, AgentFeedbackPO::getCreateTime, query.getCreateTimeLeft())
                .le(query.getCreateTimeRight() != null, AgentFeedbackPO::getCreateTime, query.getCreateTimeRight())
        );

        AgentFeedbackAnalysisDTO dto = new AgentFeedbackAnalysisDTO();
        // 总数统计
        dto.setFeedbackCount((long) feedbackList.size());
        dto.setLikeCount(feedbackList.stream().filter(f -> FeedbackTypeEnum.LIKE.equals(f.getFeedbackType())).count());
        dto.setDislikeCount(feedbackList.stream().filter(f -> FeedbackTypeEnum.DISLIKE.equals(f.getFeedbackType())).count());
        dto.setWordMarkCount(feedbackList.stream().filter(f -> FeedbackTypeEnum.WORD_MARK.equals(f.getFeedbackType())).count());

        // 标签统计
        Map<FeedbackTypeEnum, List<AgentFeedbackAnalysisDTO.FeedbackTagStat>> feedbackTagStats = feedbackList.stream()
            .filter(f -> f.getFeedbackTag() != null)
            .collect(Collectors.groupingBy(
                AgentFeedbackPO::getFeedbackType,
                Collectors.groupingBy(AgentFeedbackPO::getFeedbackTag, Collectors.counting())
            )).entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().entrySet().stream().map(e -> {
                    AgentFeedbackAnalysisDTO.FeedbackTagStat stat = new AgentFeedbackAnalysisDTO.FeedbackTagStat();
                    stat.setFeedbackTag(e.getKey());
                    stat.setCount(e.getValue());
                    return stat;
                }).collect(Collectors.toList())
            ));
        dto.setFeedbackTagStats(feedbackTagStats);

        // 日期区间补全统计
        List<AgentFeedbackAnalysisDTO.FeedbackDayStat> feedbackDayStats = new ArrayList<>();
        LocalDate start = query.getCreateTimeLeft() != null ? query.getCreateTimeLeft().toLocalDate() : null;
        LocalDate end = query.getCreateTimeRight() != null ? query.getCreateTimeRight().toLocalDate() : null;
        if (start != null && end != null && !end.isBefore(start)) {
            Map<LocalDate, List<AgentFeedbackPO>> dateMap = feedbackList.stream()
                .collect(Collectors.groupingBy(f -> f.getCreateTime().toLocalDate()));
            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                List<AgentFeedbackPO> dayList = dateMap.getOrDefault(date, Collections.emptyList());
                AgentFeedbackAnalysisDTO.FeedbackDayStat stat = new AgentFeedbackAnalysisDTO.FeedbackDayStat();
                stat.setDate(date);
                stat.setCount((long) dayList.size());
                stat.setLikeCount(dayList.stream().filter(f -> FeedbackTypeEnum.LIKE.equals(f.getFeedbackType())).count());
                stat.setDislikeCount(dayList.stream().filter(f -> FeedbackTypeEnum.DISLIKE.equals(f.getFeedbackType())).count());
                stat.setWordMarkCount(dayList.stream().filter(f -> FeedbackTypeEnum.WORD_MARK.equals(f.getFeedbackType())).count());
                feedbackDayStats.add(stat);
            }
            feedbackDayStats.sort(Comparator.comparing(AgentFeedbackAnalysisDTO.FeedbackDayStat::getDate));
        }
        dto.setFeedbackDayStats(feedbackDayStats);

        // 若无数据，标签统计置空
        if (feedbackList.isEmpty()) {
            dto.setFeedbackTagStats(Collections.emptyMap());
        }

        //截止当前时间用户总数
        questionInfoMapper.getUserHistoryCount(query);
        dto.setUserCount(questionInfoMapper.getUserHistoryCount(query));

        return dto;
    }

    private AgentFeedbackAnalysisDTO buildZeroStat(AgentFeedbackAnalysisQuery query) {
        AgentFeedbackAnalysisDTO dto = new AgentFeedbackAnalysisDTO();
        dto.setFeedbackCount(0L);
        dto.setLikeCount(0L);
        dto.setDislikeCount(0L);
        dto.setWordMarkCount(0L);
        dto.setFeedbackTagStats(Collections.emptyMap());

        LocalDate start = query.getCreateTimeLeft() != null ? query.getCreateTimeLeft().toLocalDate() : null;
        LocalDate end = query.getCreateTimeRight() != null ? query.getCreateTimeRight().toLocalDate() : null;
        List<AgentFeedbackAnalysisDTO.FeedbackDayStat> feedbackDayStats = new ArrayList<>();
        if (start != null && end != null && !end.isBefore(start)) {
            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                AgentFeedbackAnalysisDTO.FeedbackDayStat stat = new AgentFeedbackAnalysisDTO.FeedbackDayStat();
                stat.setDate(date);
                stat.setCount(0L);
                stat.setLikeCount(0L);
                stat.setDislikeCount(0L);
                stat.setWordMarkCount(0L);
                feedbackDayStats.add(stat);
            }
        }
        dto.setFeedbackDayStats(feedbackDayStats);
        return dto;
    }

    @IgnoreTenant
    public List<AgentFeedbackDTO> externalList(AgentFeedbackQuery query) {
        List<AgentFeedbackPO> agentFeedbackPOS = agentFeedbackMapper.selectList(
            Wrappers.lambdaQuery(AgentFeedbackPO.class)
                .eq(query.getWorkflowId() != null, AgentFeedbackPO::getWorkflowId, query.getWorkflowId())
                .eq(query.getChatId() != null, AgentFeedbackPO::getChatId, query.getChatId())
                .eq(query.getTaskId() != null, AgentFeedbackPO::getTaskId, query.getTaskId())
                .eq(query.getSourceSystem() != null, AgentFeedbackPO::getSourceSystem, query.getSourceSystem())
                .eq(query.getCreateUserId() != null, AgentFeedbackPO::getCreateUserId, query.getCreateUserId())
        );
        return agentFeedbackAssembler.toDTOList(agentFeedbackPOS);
    }
}
