package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.QaPairStatusEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoQaPairTemp操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoQaPairTempOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    private Long repoId;

    @Schema(description = "问答对唯一标识")
    private String qaPairKey;

    @Schema(description = "问题")
    private String question;

    @Schema(description = "回答")
    private String answer;

    @Schema(description = "问答对状态,0:NOT_CHECK-未校验,1:NOT_ACCEPT-未采纳,2:ACCEPTED-已采纳")
    private QaPairStatusEnum qaPairStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

