package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建问答对
 *
 * @Date: 2025/4/10
 * @Author: kang<PERSON>an
 */
@Data
public class KbRepoQaPairCreateCommand {

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    @NotNull(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "问题")
    @NotBlank(message = "问题不能为空")
    @Size(max = 255, message = "问题长度不能超过255个字符")
    private String question;

    @Schema(description = "相似问题")
    private List<String> similarQuestions;

    @NotBlank(message = "回答不能为空")
    @Schema(description = "回答")
    @Size(max = 2048, message = "回答长度不能超过2048个字符")
    private String answer;

}
