package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * TeamInfo创建命令
 *
 * <AUTHOR>
 */
@Data
public class TeamInfoCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队名称")
    @NotBlank(message = "团队名称不能为空")
    private String name;

}
