package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum TrainingTypeEnum {

    MANUAL("manual", "手动"),
    AUTO("auto", "自动"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    TrainingTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}

