package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体反馈分析查询
 * @date 2025-07-03
 */
@Data
public class AgentFeedbackAnalysisQuery {

    @Schema(description = "工作流ID")
    private String workflowId;

    @Schema(description = "来源")
    private List<String> source;

    @Schema(description = "创建时间左区间")
    private LocalDateTime createTimeLeft;

    @Schema(description = "创建时间右区间")
    private LocalDateTime createTimeRight;
}
