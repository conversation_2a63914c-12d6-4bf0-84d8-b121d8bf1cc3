package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.dto.KbRepoQaPairTempDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoQaPairTempQuery;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairTempPO;
import cn.genn.ai.hub.app.application.assembler.KbRepoQaPairTempAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoQaPairTempMapper;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoQaPairTempQueryService {

    private final KbRepoQaPairTempMapper mapper;
    private final KbRepoQaPairTempAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoQaPairTempDTO分页对象
     */
    public PageResultDTO<KbRepoQaPairTempDTO> page(KbRepoQaPairTempQuery query) {
        KbRepoQaPairTempPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoQaPairTempDTO
     */
    public KbRepoQaPairTempDTO get(IdQuery query) {
        return assembler.PO2DTO(mapper.selectById(query.getId()));
    }
}

