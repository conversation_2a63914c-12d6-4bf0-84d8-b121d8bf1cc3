package cn.genn.ai.hub.app.infrastructure.config.auth;

import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.service.AuthCommonService;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthActionWrapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.wrapper.DefaultAuthActionWrapper;
import cn.genn.core.model.KVStruct;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.web.spring.component.base.AbstractAnnotationResponseBodyAdvice;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Order(value = Ordered.HIGHEST_PRECEDENCE + 20)
@Slf4j
public class AuthActionResponseBodyAdvice extends AbstractAnnotationResponseBodyAdvice<AuthAction> {

    @Resource
    private AuthCommonService authCommonService;

    @Override
    protected Object doBeforeBodyWrite(Object body, AuthAction authAction, HttpServletRequest request, HttpServletResponse response, HttpHeaders httpHeaders) {
        if (body == null) {
            return null;
        }
        if (!authAction.enabled()) {
            return body;
        }
        Class<? extends AuthActionWrapper> wrapperCls = authAction.resultWrapper();
        if (wrapperCls != DefaultAuthActionWrapper.class) {
            AuthActionWrapper wrapper = SpringUtil.getBean(wrapperCls);
            wrapper.wrap(body, authAction);
            return body;
        }
        switch (body) {
            case Collection<?> list -> setListAuthActions(authAction, list);
            case PageResultDTO<?> pageRes -> setListAuthActions(authAction, pageRes.getList());
            case Page<?> p -> {
                PageImpl<?> page = (PageImpl<?>) p;
                setListAuthActions(authAction, page.getContent());
            }
            case ResponseResult<?> ignored -> {
                return body;
            }
            default -> setAuthActions(authAction, body, new ConcurrentHashMap<>());
        }
        return body;
    }


    private void setListAuthActions(AuthAction authAction, Collection<?> list) {
        ConcurrentHashMap<String, List<AuthCommon>> cacheMap = new ConcurrentHashMap<>();
        for (Object obj : list) {
            setAuthActions(authAction, obj, cacheMap);
        }
    }

    private void setAuthActions(AuthAction authAction, Object obj, ConcurrentHashMap<String, List<AuthCommon>> cacheMap) {
        //获取资源id的值
        String idField = authAction.resourceField();
        List<ActionType> actionTypes = Stream.of(authAction.actionRange()).collect(Collectors.toList());
        String resourceId = ReflectUtil.getFieldValue(obj, idField).toString();
        List<KVStruct<SubjectType, String>> relations = new ArrayList<>();
        SubjectRelation[] subjectRelations = authAction.subjectRelation();
        relations.add(KVStruct.of(SubjectType.USER, CurrentUserHolder.getUserId().toString()));
        if (subjectRelations.length > 0) {
            relations.addAll(Stream.of(subjectRelations).map(subjectRelation -> {
                SubjectType subjectType = subjectRelation.subjectType();
                Object fieldValue = ReflectUtil.getFieldValue(obj, subjectRelation.param());
                if (fieldValue == null) {
                    return null;
                }
                String subjectKey = fieldValue.toString();
                return KVStruct.of(subjectType, subjectKey);
            }).filter(Objects::nonNull).toList());
        }
        List<ActionType> authActions = authCommonService.listAuthActions(authAction.resourceType(), resourceId, relations, actionTypes, cacheMap);
        ReflectUtil.setFieldValue(obj, "authActions", authActions);
    }

}
