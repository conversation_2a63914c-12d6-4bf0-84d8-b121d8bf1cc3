package cn.genn.ai.hub.app.application.processor;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;

import java.util.Map;

public interface TaskHandler {
    /**
     * 核心处理函数：处理任务
     *
     * @param taskId   任务ID
     * @param tenantId 租户ID
     */
    void handleTask(String taskId, String tenantId);

    /**
     * 执行任务处理
     *
     * @param task taskInfo
     */
    void invoke(KbRepoTaskDTO task);

    /**
     * handler类型
     */
    TaskTypeEnum getType();

    /**
     * @param tenantId 租户ID
     * @return 处理队列名称
     */
    String getProcessQueueName(String tenantId);

    /**
     * @param tenantId 租户ID
     * @return 任务队列名称
     */
    String getTaskQueueName(String tenantId);

    /**
     * 获取任务/处理 队列状态（任务数）
     *
     * @param tenantId 租户ID
     * @return 处理队列名称
     */
    Map<String, Long> getQueueState(String tenantId);

    /**
     * 指定key删除进行中队列的元素
     *
     * @param task 任务
     */
    void removeTaskFromProcessQueue(KbRepoTaskDTO task);
}
