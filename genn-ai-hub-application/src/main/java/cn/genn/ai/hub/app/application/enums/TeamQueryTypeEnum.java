package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 团队查询类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TeamQueryTypeEnum {

    /**
     * 我创建的团队
     */
    OWNER_BY_ME(1, "我拥有的"),

    /**
     * 我加入的团队
     */
    JOINED_BY_ME(2, "我加入的");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;

}
