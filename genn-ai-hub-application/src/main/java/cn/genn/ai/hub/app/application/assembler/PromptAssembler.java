package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.PromptSaveCommand;
import cn.genn.ai.hub.app.application.dto.PromptDTO;
import cn.genn.ai.hub.app.application.dto.PromptVersionDTO;
import cn.genn.ai.hub.app.domain.prompt.model.entity.Prompt;
import cn.genn.ai.hub.app.domain.prompt.model.entity.PromptVersion;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.PromptVersionPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 提示词转换器
 * @date 2025-05-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PromptAssembler {

    Prompt command2Entity(PromptSaveCommand command);

    @Mappings({
        // Prompt 的属性映射到 PromptDTO
        @Mapping(source = "prompt.id", target = "id"),
        @Mapping(source = "prompt.tenantId", target = "tenantId"),
        @Mapping(source = "prompt.teamId", target = "teamId"),
        @Mapping(source = "prompt.title", target = "title"),
        @Mapping(source = "prompt.promptType", target = "promptType"),
        @Mapping(source = "prompt.latestVersion", target = "latestVersion"),
        @Mapping(source = "prompt.status", target = "status"),
        @Mapping(source = "prompt.createTime", target = "createTime"),
        @Mapping(source = "prompt.createUserId", target = "createUserId"),
        @Mapping(source = "prompt.createUserName", target = "createUserName"),
        @Mapping(source = "prompt.updateTime", target = "updateTime"),
        @Mapping(source = "prompt.updateUserId", target = "updateUserId"),
        @Mapping(source = "prompt.updateUserName", target = "updateUserName"),
        @Mapping(source = "promptVersion", target = "promptVersion")
    })
    PromptDTO toPromptDTOWithVersion(Prompt prompt, PromptVersion promptVersion);

    @Mappings(
        {
            @Mapping(source = "promptVersion.id", target = "id"),
            @Mapping(source = "promptVersion.tenantId", target = "tenantId"),
            @Mapping(source = "promptVersion.promptId", target = "promptId"),
            @Mapping(source = "promptVersion.version", target = "version"),
            @Mapping(source = "promptVersion.content", target = "content"),
            @Mapping(source = "prompt.title", target = "title"),
            @Mapping(source = "promptVersion.description", target = "description"),
            @Mapping(source = "promptVersion.createTime", target = "createTime"),
            @Mapping(source = "promptVersion.createUserId", target = "createUserId"),
            @Mapping(source = "promptVersion.createUserName", target = "createUserName"),
            @Mapping(source = "promptVersion.updateTime", target = "updateTime"),
            @Mapping(source = "promptVersion.updateUserId", target = "updateUserId"),
            @Mapping(source = "promptVersion.updateUserName", target = "updateUserName")
        })
    PromptVersionDTO toPromptVersionDTO(Prompt prompt, PromptVersion promptVersion);

    PromptVersionDTO toPromptVersionDTO(PromptVersionPO promptVersion);

    List<PromptDTO> toPromptDTO(List<PromptPO> promptPO);
}
