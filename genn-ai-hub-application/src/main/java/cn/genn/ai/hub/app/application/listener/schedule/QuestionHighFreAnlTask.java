package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import cn.genn.ai.hub.app.domain.question.service.QuestionGapAnalysisService;
import cn.genn.ai.hub.app.infrastructure.config.AgentProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.AgentAppConstants;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionRepositoryImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class QuestionHighFreAnlTask extends IJobHandler {

    @Resource
    private QuestionRepositoryImpl questionRepository;
    @Resource
    private QuestionGapAnalysisService questionGapAnalysisService;
    @Resource
    private GennAIHubProperties gennAIHubProperties;

    /**
     * TODO
     *
     * @throws Exception
     */
    @Override
    public void execute() throws Exception {
        log.info("QuestionHighFreAnlTask begin");
        String jobParam = XxlJobHelper.getJobParam();
        List<QuestionInfoDTO> infoDTOS = questionRepository.getAllAnalysisInfo(jobParam);
        AgentProperties agent = gennAIHubProperties.getAgent();
        List<String> filter = agent.getFilter();
        String url = agent.getInvokeDomain() + AgentAppConstants.INVOKE_APP_URL;
        for (QuestionInfoDTO infoDTO : infoDTOS) {
            if (filter.contains(infoDTO.getAppId())) {
                continue;
            }
            String format = "{\"variables\": {\"flowId\": \"%s\",\"source\": \"%s\"},\"chatId\": \"\",\"stream\": true,\"detail\": false,\"messages\": [{\"role\": \"user\",\"content\": \"高频问题分析统计\"}]}";
            String request = String.format(format, infoDTO.getAppId(), infoDTO.getSource());
            questionGapAnalysisService.invokeWorkflow(url, request, agent.getHighFreApiKey(), agent.getMaxRetries());
        }

        log.info("QuestionHighFreAnlTask end");
    }


}
