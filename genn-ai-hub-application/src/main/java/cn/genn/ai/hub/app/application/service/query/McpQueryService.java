package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.McpAssembler;
import cn.genn.ai.hub.app.application.dto.mcp.ApiMcpServerStatusDTO;
import cn.genn.ai.hub.app.application.dto.mcp.McpDTO;
import cn.genn.ai.hub.app.application.enums.McpServerStatus;
import cn.genn.ai.hub.app.application.enums.OverallStatus;
import cn.genn.ai.hub.app.application.query.McpQuery;
import cn.genn.ai.hub.app.domain.mcp.model.entity.Mcp;
import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroup;
import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroupRef;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpGroupRefRepository;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpGroupRepository;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpRepository;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.McpMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpPO;
import cn.genn.ai.hub.app.infrastructure.utils.McpGatewayInvokeUtils;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.GennUtils;
import cn.genn.database.mybatisplus.query.QueryWrapperUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class McpQueryService {

    private final McpMapper mapper;
    private final McpAssembler assembler;
    private final IMcpRepository mcpRepository;
    private final IMcpGroupRefRepository mcpGroupRefRepository;
    private final IMcpGroupRepository mcpGroupRepository;
    private final GennAIHubProperties gennAIHubProperties;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return McpDTO分页对象
     */
    public PageResultDTO<McpDTO> page(McpQuery query) {
        QueryWrapper<McpPO> queryWrapper = QueryWrapperUtil.build(query);
        queryWrapper.lambda().eq(McpPO::getDeleted, DeletedTypeEnum.NOT_DELETED).orderByDesc(McpPO::getId);
        PageResultDTO<McpDTO> pageResult = assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
        if (GennUtils.pageIsEmpty(pageResult)) {
            return pageResult;
        }
        List<McpDTO> mcpDTOS = pageResult.getList();
        List<ApiMcpServerStatusDTO> allMcpServersStatus = McpGatewayInvokeUtils.getAllMcpServersStatus();
        Map<Long, List<ApiMcpServerStatusDTO>> statusMap = allMcpServersStatus.stream()
            .collect(Collectors.groupingBy(ApiMcpServerStatusDTO::getId));
        mcpDTOS.forEach(mcpDTO -> {
            List<ApiMcpServerStatusDTO> statusDTOS = statusMap.getOrDefault(mcpDTO.getId(), Collections.emptyList());
            mcpDTO.setDetailStatus(statusDTOS);
            mcpDTO.setStatus(McpServerStatus.convertFromStatus(statusDTOS));
            mcpDTO.setTools(statusDTOS == null ? Collections.emptyList() :
                statusDTOS.stream().filter(status ->
                        status.getOverallStatus().equals(OverallStatus.HEALTHY))
                    .findFirst()
                    .map(ApiMcpServerStatusDTO::getTools)
                    .orElse(Collections.emptyList()));
        });
        return pageResult;
    }

    /**
     * 根据id查询
     *
     * @param query ID查询对象
     * @return McpDTO
     */
    public McpDTO get(IdQuery query) {
        // 使用仓储接口获取领域实体
        Mcp mcp = mcpRepository.getById(query.getId());
        // 使用装配器将领域实体转换为DTO
        McpDTO mcpDTO = assembler.toDTO(mcp);
        List<ApiMcpServerStatusDTO> mcpServerStatus = McpGatewayInvokeUtils.getMcpServerStatus(mcpDTO.getId());
        mcpDTO.setDetailStatus(mcpServerStatus);
        mcpDTO.setStatus(McpServerStatus.convertFromStatus(mcpServerStatus));
        mcpDTO.setTools(mcpServerStatus == null ? Collections.emptyList() : mcpServerStatus.stream().filter(status ->
            status.getOverallStatus().equals(OverallStatus.HEALTHY)).findFirst().map(ApiMcpServerStatusDTO::getTools).orElse(Collections.emptyList()));
        return mcpDTO;
    }

    /**
     * 根据ID查询MCP的详细信息。
     */
    public McpDTO getDetail(Long mcpId) {
        Mcp mcp = mcpRepository.getById(mcpId);
        if (mcp == null) {
            return null;
        }

        List<McpGroupRef> groupRefs = mcpGroupRefRepository.listByMcpId(mcpId);
        if (groupRefs.isEmpty()) {
            mcp.setGroups(Collections.emptyList());
            return assembler.toDTO(mcp);
        }

        Set<Long> allGroupIds = groupRefs.stream()
            .map(McpGroupRef::getGroupId)
            .collect(Collectors.toSet());

        List<McpGroup> mcpGroups = mcpGroupRepository.batchGetByIds(allGroupIds);

        Map<Long, McpGroup> groupMap = mcpGroups.stream()
            .collect(Collectors.toMap(McpGroup::getId, group -> group));

        List<McpGroup> groups = groupRefs.stream()
            .map(McpGroupRef::getGroupId)
            .map(groupMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        mcp.setGroups(groups);

        return assembler.toDTO(mcp);
    }

    /**
     * 获取所有的MCP，并填充groupKeys
     *
     * @return List<McpDTO>
     */
    public List<McpDTO> listAllMcps() { // Added method
        List<Mcp> mcps = mcpRepository.listAll();
        if (mcps.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> mcpIds = mcps.stream().map(Mcp::getId).collect(Collectors.toList());
        Map<Long, List<McpGroupRef>> mcpGroupRefsMap = mcpGroupRefRepository.listByMcpIds(mcpIds);

        // 提取所有相关的GroupId
        Set<Long> allGroupIds = mcpGroupRefsMap.values().stream()
            .flatMap(List::stream)
            .map(McpGroupRef::getGroupId)
            .collect(Collectors.toSet());

        // 批量查询所有McpGroup
        List<McpGroup> mcpGroups = mcpGroupRepository.batchGetByIds(allGroupIds);

        Map<Long, McpGroup> groupMap = mcpGroups.stream()
            .collect(Collectors.toMap(McpGroup::getId, group -> group));

        for (Mcp mcp : mcps) {
            List<McpGroupRef> refs = mcpGroupRefsMap.getOrDefault(mcp.getId(), Collections.emptyList());
            if (!refs.isEmpty()) {
                List<McpGroup> groups = refs.stream()
                    .map(McpGroupRef::getGroupId)
                    .map(groupMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                mcp.setGroups(groups);
            } else {
                mcp.setGroups(Collections.emptyList());
            }
        }
        return mcps.stream().map(assembler::toDTO).collect(Collectors.toList());
    }

    public List<String> listAllInstance() {
        return McpGatewayInvokeUtils.getInstances();
    }

    public String getMcpGatewayAddress() {
        return gennAIHubProperties.getMcp().getMcpGatewayUrl();
    }
}
