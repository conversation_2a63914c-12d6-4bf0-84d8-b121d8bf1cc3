package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.wrapper;

import cn.genn.ai.hub.app.infrastructure.config.auth.AuthAction;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AbstractAuthActionWrapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DefaultAuthActionWrapper extends AbstractAuthActionWrapper<Object> {

    @Override
    public void wrap(Object returnValue, AuthAction authAction) {

    }
}
