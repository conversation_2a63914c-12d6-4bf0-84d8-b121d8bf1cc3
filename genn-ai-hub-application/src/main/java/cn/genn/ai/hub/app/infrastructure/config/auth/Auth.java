package cn.genn.ai.hub.app.infrastructure.config.auth;


import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParamResolver;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.DefaultAuthParamResolver;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Auth {

    /**
     * 是否需要鉴权
     */
    boolean enabled() default true;

    /**
     * 是否只允许超级管理员访问
     */
    boolean onlyAllowSuperAdmin() default false;

    /**
     * 主体类型
     */
    SubjectType subjectType() default SubjectType.USER;

    /**
     * 操作类型,只要有一个即可鉴权通过
     */
    ActionType[] actionType() default {};

    /**
     * 资源类型
     */
    ResourceType resourceType() default ResourceType.EMPTY;

    /**
     * 资源实例 key，具体的资源实例属性，一般为参数的某个字段，spel表达式
     */
    String fieldKey() default "";

    /**
     * 是否允许资源实例 key 对应的值为空.
     * true: 没有传入资源时,鉴权通过
     * false: 没有传入资源时,鉴权失败
     */
    boolean allowFieldKeyNull() default false;

    /**
     * 自定义参数解析
     */
    Class<? extends AuthParamResolver> paramResolver() default DefaultAuthParamResolver.class;

    /**
     * 自定义唯一标识，当参数解析器可以复用时，可以自定义id用来区分
     */
    String uniqueId() default "";
}
