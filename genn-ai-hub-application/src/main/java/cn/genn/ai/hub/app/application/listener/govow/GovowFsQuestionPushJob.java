package cn.genn.ai.hub.app.application.listener.govow;

import cn.genn.ai.hub.app.application.service.govow.GovowOperationService;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.hutool.core.text.CharSequenceUtil;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class GovowFsQuestionPushJob extends AbstractJobHandler {

    private final GovowOperationService govowOperationService;

    @Override
    public void doExecute() {
        String jobParam = XxlJobHelper.getJobParam();
        if (CharSequenceUtil.isEmpty(jobParam)) {
            XxlJobHelper.log("GovowFsQuestionPushJob jobParam is empty");
            return;
        }
        GovowFsQuestionPushParam pushParam = JsonUtils.parse(jobParam, GovowFsQuestionPushParam.class);
        if (pushParam == null) {
            XxlJobHelper.log("GovowFsQuestionPushJob pushParam is null");
            return;
        }
        govowOperationService.pushQuestionToFeishu(pushParam);
    }
}
