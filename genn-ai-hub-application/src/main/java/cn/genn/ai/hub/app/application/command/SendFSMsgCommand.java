package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.feishu.FSMsgContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SendFSMsgCommand {

    @NotNull(message = "消息类型不能为空")
    @Schema(description = "消息类型, text:文本; interactive:卡片", required = true)
    private FSMsgContentTypeEnum contentType;

    @NotBlank(message = "消息内容")
    @Schema(description = "消息内容", required = true)
    private String content;

    @Schema(description = "消息标题-卡片消息标题")
    private String headerTitle;

    @Schema(description = "卡片跳转地址")
    private String cardLink;

}
