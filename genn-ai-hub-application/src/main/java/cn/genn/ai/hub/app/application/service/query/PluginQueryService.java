package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.PluginAssembler;
import cn.genn.ai.hub.app.application.dto.PluginDTO;
import cn.genn.ai.hub.app.application.query.PluginListQuery;
import cn.genn.ai.hub.app.application.query.PluginQuery;
import cn.genn.ai.hub.plugin.common.PluginCommonService;
import cn.genn.ai.hub.plugin.common.repository.mapper.PluginCategoryMapper;
import cn.genn.ai.hub.plugin.common.repository.mapper.PluginDefinitionMapper;
import cn.genn.ai.hub.plugin.common.repository.mapper.PluginVersionDefinitionMapper;
import cn.genn.ai.hub.plugin.common.repository.po.PluginCategoryPO;
import cn.genn.ai.hub.plugin.common.repository.po.PluginDefinitionPO;
import cn.genn.ai.hub.plugin.common.repository.po.PluginVersionDefinitionPO;
import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PluginQueryService {

    private final PluginAssembler pluginAssembler;
    private final PluginDefinitionMapper pluginDefinitionMapper;
    private final PluginVersionDefinitionMapper pluginVersionDefinitionMapper;
    private final PluginCategoryMapper pluginCategoryMapper;
    private final PluginCommonService pluginCommonService;

    /**
     * 查询插件列表
     *
     * @param query 查询参数
     * @return 插件列表
     */
    public PageResultDTO<PluginDTO> queryPluginPage(PluginListQuery query) {
        LambdaQueryWrapper<PluginDefinitionPO> wrapper = Wrappers.lambdaQuery(PluginDefinitionPO.class);
        wrapper.eq(query.getCatId() != null, PluginDefinitionPO::getCatId, query.getCatId());
        wrapper.eq(query.getPluginType() != null, PluginDefinitionPO::getPluginType, query.getPluginType());
        wrapper.like(CharSequenceUtil.isNotEmpty(query.getPluginName()), PluginDefinitionPO::getPluginName, query.getPluginName());

        // 查询插件基本信息
        Page<PluginDefinitionPO> poPage = pluginDefinitionMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), wrapper);

        // 获取所有插件的最新版本信息
        if (!poPage.getRecords().isEmpty()) {
            //查询目录信息
            List<PluginCategoryPO> categoryPOS =
                pluginCategoryMapper.selectByIds(poPage.getRecords().stream().map(PluginDefinitionPO::getCatId).distinct().collect(Collectors.toList()));
            Map<Long, PluginCategoryPO> catMap = categoryPOS.stream().collect(Collectors.toMap(PluginCategoryPO::getId, Function.identity()));
            PageResultDTO<PluginDTO> pageResult = pluginAssembler.toPageResult(poPage, pluginCommonService.getPluginVersionMap(poPage.getRecords()));
            pageResult.getList().forEach(pluginDTO -> {
                pluginDTO.setCatName(pluginAssembler.getCategoryName(pluginDTO.getCatId(), catMap));
            });
            return pageResult;
        }
        return pluginAssembler.toPageResult(poPage);
    }

    public PluginDTO getPlugin(PluginQuery query) {
        PluginDefinitionPO pluginDefinition = pluginDefinitionMapper.selectOne(Wrappers.lambdaQuery(PluginDefinitionPO.class)
            .eq(PluginDefinitionPO::getPluginKey, query.getPluginKey()));
        if (pluginDefinition == null) {
            return null;
        }
        PluginVersionDefinitionPO pluginVersionDefinitionPO = pluginVersionDefinitionMapper.selectOne(Wrappers.lambdaQuery(PluginVersionDefinitionPO.class)
            .eq(PluginVersionDefinitionPO::getPluginKey, query.getPluginKey())
            .eq(PluginVersionDefinitionPO::getVersion, query.getVersion()));
        if (pluginVersionDefinitionPO == null) {
            return null;
        }
        return pluginAssembler.toPluginDTO(pluginDefinition, pluginVersionDefinitionPO);
    }
}
