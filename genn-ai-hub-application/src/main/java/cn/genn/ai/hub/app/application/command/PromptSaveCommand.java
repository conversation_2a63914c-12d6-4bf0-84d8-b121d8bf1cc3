package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 提示词创建命令
 * @date 2025-05-12
 */
@Data
public class PromptSaveCommand {

    @Schema(description = "提示词ID")
    private Long id;

    @Schema(description = "团队空间ID")
    private Long teamId;

    @Schema(description = "提示词标题/名称")
    private String title;

    @Schema(description = "提示词描述")
    private String description;

    @Schema(description = "提示词内容")
    private String content;

    @Schema(description = "发布版本号")
    private String version;

}
