package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.ToolInfoAssembler;
import cn.genn.ai.hub.app.application.command.SendFSMsgCommand;
import cn.genn.ai.hub.app.application.dto.Md2FileTemplateDTO;
import cn.genn.ai.hub.app.application.dto.ToolInfoDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FeiShuCardMessage;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.enums.ToolSourceEnum;
import cn.genn.ai.hub.app.application.processor.handler.msg.SendFSMsgHandler;
import cn.genn.ai.hub.app.application.query.TagsQuery;
import cn.genn.ai.hub.app.application.query.ToolInfoQuery;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ToolInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ToolInfoPO;
import cn.genn.ai.hub.core.api.tool.SystemConfig;
import cn.genn.ai.hub.core.api.tool.SystemConfigAbilityApi;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.GennUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ToolInfoQueryService {

    private final ToolInfoMapper mapper;
    private final ToolInfoAssembler assembler;
    private final TagsQueryService tagsQueryService;
    private final SystemConfigAbilityApi systemConfigAbilityApi;
    private final SendFSMsgHandler sendMsgHandler;
    private final McpGroupQueryService mcpGroupQueryService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return ToolInfoDTO分页对象
     */
    public PageResultDTO<ToolInfoDTO> page(ToolInfoQuery query) {
        // 处理标签相关的查询
        Set<Long> toolIds = null;

        // 如果有标签相关的查询条件
        if (CollUtil.isNotEmpty(query.getTagIds()) || CharSequenceUtil.isNotEmpty(query.getTagName())) {
            TagsQuery tagsQuery = TagsQuery.builder()
                .tagType(TagTypeEnum.GENERAL_TOOL)
                .name(query.getTagName())
                .tagIds(query.getTagIds())
                .build();

            // 使用标签查询服务的方法获取资源key列表
            Set<String> resourceKeys = tagsQueryService.getResourceKeysByTagConditions(tagsQuery);

            if (resourceKeys.isEmpty()) {
                // 如果没有找到任何资源key，返回空结果
                return PageResultDTO.empty();
            }

            toolIds = resourceKeys.stream()
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        }

        LambdaQueryWrapper<ToolInfoPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(toolIds), ToolInfoPO::getId, toolIds);
        wrapper.like(CharSequenceUtil.isNotEmpty(query.getName()), ToolInfoPO::getName, query.getName());
        wrapper.like(CharSequenceUtil.isNotEmpty(query.getIntro()), ToolInfoPO::getIntro, query.getIntro());
        wrapper.eq(query.getId() != null, ToolInfoPO::getId, query.getId());
        wrapper.eq(CharSequenceUtil.isNotEmpty(query.getToolKey()), ToolInfoPO::getToolKey, query.getToolKey());
        wrapper.eq(query.getToolSource() != null, ToolInfoPO::getToolSource, query.getToolSource());
        wrapper.eq(ToolInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        wrapper.orderByDesc(ToolInfoPO::getSortOrder);

        PageResultDTO<ToolInfoDTO> pageResult = assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), wrapper));
        if (GennUtils.pageIsEmpty(pageResult)) {
            return pageResult;
        }
        fillTagInfo(pageResult.getList());
        return pageResult;
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return ToolInfoDTO
     */
    public ToolInfoDTO get(IdQuery query) {
        ToolInfoDTO toolInfoDTO = assembler.PO2DTO(mapper.selectById(query.getId()));
        if (toolInfoDTO == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }
        ToolSourceEnum toolSource = toolInfoDTO.getToolSource();
        if (toolSource == ToolSourceEnum.MCP) {
            // 如果是MCP工具，需要填充mcp额外配置
            toolInfoDTO.setExtraConfig(Map.of("mcpConfig", mcpGroupQueryService.getExposeConfigByGroupKey(toolInfoDTO.getToolKey().substring("mcp-".length()))));
        }
        fillTagInfo(List.of(toolInfoDTO));
        return toolInfoDTO;
    }

    /**
     * 获取md2File模板列表
     */
    public List<Md2FileTemplateDTO> listMdTemplates() {
        List<SystemConfig> systemConfigs = systemConfigAbilityApi.listByType("md2FileTemplate");
        return systemConfigs.stream()
            .map(config ->
                Md2FileTemplateDTO.builder()
                    .templateKey(config.getConfigKey())
                    .name(config.getDescription())
                    .params(Optional.ofNullable(config.getExtra())
                        .map(extra -> extra.get("extra"))
                        .map(JsonUtils::toJson)
                        .map(json -> JsonUtils.parseToList(json, Md2FileTemplateDTO.Param.class))
                        .orElse(null))
                    .build())
            .collect(Collectors.toList());
    }


    private void fillTagInfo(List<ToolInfoDTO> toolInfoList) {
        if (CollUtil.isEmpty(toolInfoList)) {
            return;
        }
        tagsQueryService.fillTagsInfo(
            toolInfoList,
            TagTypeEnum.GENERAL_TOOL,
            null,
            ToolInfoDTO::getId,
            ToolInfoDTO::setTagList
        );
    }


    public Object sendFSMsg(SendFSMsgCommand command) {
        String msg = buildMsg(command);
        return sendMsgHandler.sendMsg(msg, command.getContentType());
    }


    /**
     *  {
     *     "schema": "2.0",
     *     "header": {
     *         "title": {
     *             "tag": "plain_text",
     *             "content": "示例标题"
     *         }
     *     },
     *     "card_link": {
     *         "url": "https://www.baidu.com"
     *     },
     *     "body": {
     *         "elements": [{
     *             "tag": "div",
     *             "text": {
     *                 "tag": "plain_text",
     *                 "content": "这是文本内容"
     *             }
     *         }]
     *     }
     * }
     */
    private String buildMsg(SendFSMsgCommand command) {
        switch (command.getContentType()) {
            case TEXT -> {
                return String.format("{\"text\":\"%s\"}", command.getContent());
            }
            case INTERACTIVE -> {
                FeiShuCardMessage.FeiShuCardMessageBuilder messageBuilder = FeiShuCardMessage.builder();

                messageBuilder.schema("2.0");
                // 构建 header（仅当 getHeaderTitle 不为空）
                if (CharSequenceUtil.isNotEmpty(command.getHeaderTitle())) {
                    FeiShuCardMessage.Title title = FeiShuCardMessage.Title.builder()
                        .tag("plain_text")
                        .content(command.getHeaderTitle())
                        .build();
                    FeiShuCardMessage.Header header = FeiShuCardMessage.Header.builder()
                        .title(title)
                        .build();
                    messageBuilder.header(header);
                }

                // 构建 card_link（仅当 getCardLink 不为空）
                if (CharSequenceUtil.isNotEmpty(command.getCardLink())) {
                    FeiShuCardMessage.CardLink cardLink = FeiShuCardMessage.CardLink.builder()
                        .url(command.getCardLink())
                        .build();
                    messageBuilder.card_link(cardLink);
                }

                // 构建 body
                FeiShuCardMessage.Body body = FeiShuCardMessage.Body.builder()
                    .elements(List.of(FeiShuCardMessage.Element.builder()
                        .tag("div")
                        .text(FeiShuCardMessage.Element.Text.builder()
                            .tag("plain_text")
                            .content(command.getContent())
                            .build())
                        .build()))
                    .build();
                messageBuilder.body(body);
                return JsonUtils.toJson(messageBuilder.build());
            }
        }
        return command.getContent();
    }

}

