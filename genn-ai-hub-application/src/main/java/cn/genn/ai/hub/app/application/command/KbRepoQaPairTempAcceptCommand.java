package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 采纳问答对
 *
 * @Date: 2025/4/10
 * @Author: kang<PERSON>an
 */
@Data
public class KbRepoQaPairTempAcceptCommand {

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    @NotNull(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "问答对id")
    @NotNull(message = "问答对id不能为空")
    private Long id;

    @Schema(description = "问答对唯一标识")
    @NotNull(message = "问答对唯一标识不能为空")
    private String qaPairKey;

    @Schema(description = "问题")
    @NotNull(message = "问题不能为空")
    private String question;

    @Schema(description = "回答")
    @NotNull(message = "回答不能为空")
    private String answer;

}
