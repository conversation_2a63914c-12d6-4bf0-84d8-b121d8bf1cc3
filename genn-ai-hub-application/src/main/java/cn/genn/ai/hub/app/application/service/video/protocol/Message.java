package cn.genn.ai.hub.app.application.service.video.protocol;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Data
public class Message {
    private byte version = VersionBits.Version1.getValue();
    private byte headerSize = HeaderSizeBits.HeaderSize4.getValue();
    private MessageType type;
    private MessageFlag flag;
    private byte serialization = SerializationBits.JSON.getValue();
    private byte compression = 0;

    private EventType event;
    private String sessionId;
    private String connectId;
    private int sequence;
    private int errorCode;

    private byte[] payload;

    public Message(MessageType type, MessageFlag flag) {
        this.type = type;
        this.flag = flag;
    }

    public byte[] marshal() throws Exception {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();

        // Write header
        buffer.write((version & 0x0F) << 4 | (headerSize & 0x0F));
        buffer.write((type.getValue() & 0x0F) << 4 | (flag.getValue() & 0x0F));
        buffer.write((serialization & 0x0F) << 4 | (compression & 0x0F));

        int headerSizeInt = 4 * (int) headerSize;
        int padding = headerSizeInt - buffer.size();
        while (padding > 0) {
            buffer.write(0);
            padding -= 1;
        }


        // Write event if present
        if (event != null) {
            byte[] eventBytes = ByteBuffer.allocate(4).putInt(event.getValue()).array();
            buffer.write(eventBytes);
        }

        // Write sessionId if present
        if (sessionId != null) {
            byte[] sessionIdBytes = sessionId.getBytes(StandardCharsets.UTF_8);
            buffer.write(ByteBuffer.allocate(4).putInt(sessionIdBytes.length).array());
            buffer.write(sessionIdBytes);
        }

        // Write connectId if present
        if (connectId != null) {
            byte[] connectIdBytes = connectId.getBytes(StandardCharsets.UTF_8);
            buffer.write(ByteBuffer.allocate(4).putInt(connectIdBytes.length).array());
            buffer.write(connectIdBytes);
        }

        // Write sequence if present
        if (sequence != 0) {
            buffer.write(ByteBuffer.allocate(4).putInt(sequence).array());
        }

        // Write errorCode if present
        if (errorCode != 0) {
            buffer.write(ByteBuffer.allocate(4).putInt(errorCode).array());
        }

        // Write payload if present
        if (payload != null && payload.length > 0) {
            buffer.write(ByteBuffer.allocate(4).putInt(payload.length).array());
            buffer.write(payload);
        }
        return buffer.toByteArray();
    }

    public static Message unmarshal(byte[] data) throws Exception {
        ByteBuffer buffer = ByteBuffer.wrap(data);

        byte type_and_flag = data[1];
        MessageType type = MessageType.fromValue((type_and_flag >> 4) & 0x0F);
        log.info("type: {}", type);
        MessageFlag flag = MessageFlag.fromValue(type_and_flag & 0x0F);
        log.info("flag: {}", flag);

        // 读取版本和头部大小
        int versionAndHeaderSize = buffer.get();
        VersionBits version = VersionBits.fromValue((versionAndHeaderSize >> 4) & 0x0F);
        HeaderSizeBits headerSize = HeaderSizeBits.fromValue(versionAndHeaderSize & 0x0F);

        // 跳过第二个字节
        buffer.get();

        // 读取序列化和压缩方法
        int serializationCompression = buffer.get();
        SerializationBits serialization = SerializationBits.fromValue((serializationCompression >> 4) & 0x0F);
        CompressionBits compression = CompressionBits.fromValue(serializationCompression & 0x0F);

        // 跳过填充字节
        int headerSizeInt = 4 * (int) headerSize.getValue();
        int paddingSize = headerSizeInt - 3;
        while (paddingSize > 0) {
            buffer.get();
            paddingSize -= 1;
        }

        Message message = new Message(type, flag);
        message.setVersion(version.getValue());
        message.setHeaderSize(headerSize.getValue());
        message.setSerialization(serialization.getValue());
        message.setCompression(compression.getValue());

        // Read sequence if present
        if (flag == MessageFlag.MSG_FLAG_POSITIVE_SEQ || flag == MessageFlag.MSG_FLAG_NEGATIVE_SEQ) {
            // 从ByteBuffer读取4字节并解析为int（大端序）
            byte[] sequeueBytes = new byte[4];
            if (buffer.remaining() >= 4) {
                buffer.get(sequeueBytes);  // 读取4字节到数组
                ByteBuffer wrapper = ByteBuffer.wrap(sequeueBytes);
                wrapper.order(ByteOrder.BIG_ENDIAN);  // 设置大端序
                message.setSequence(wrapper.getInt());
                log.info("sequence: {}", message.sequence);
            }
        }

        // Read event if present
        if (flag == MessageFlag.MSG_FLAG_WITH_EVENT) {
            // 从ByteBuffer读取4字节并解析为int（大端序）
            byte[] eventBytes = new byte[4];
            if (buffer.remaining() >= 4) {
                buffer.get(eventBytes);  // 读取4字节到数组
                ByteBuffer wrapper = ByteBuffer.wrap(eventBytes);
                wrapper.order(ByteOrder.BIG_ENDIAN);  // 设置大端序
                message.setEvent(EventType.fromValue(wrapper.getInt()));
            }

            if (type != MessageType.MSG_TYPE_ERROR && !(message.event == EventType.TYPE_START_CONNECTION || message.event == EventType.TYPE_FINISH_CONNECTION ||
                    message.event == EventType.TYPE_CONNECTION_STARTED || message.event == EventType.TYPE_CONNECTION_FAILED ||
                    message.event == EventType.TYPE_CONNECTION_FINISHED)) {
                // Read sessionId if present
                int sessionIdLength = buffer.getInt();
                if (sessionIdLength > 0) {
                    byte[] sessionIdBytes = new byte[sessionIdLength];
                    buffer.get(sessionIdBytes);
                    log.info("sessionID: {}", new String(sessionIdBytes, StandardCharsets.UTF_8));
                    message.setSessionId(new String(sessionIdBytes, StandardCharsets.UTF_8));
                }
            }

            if (message.event == EventType.TYPE_CONNECTION_STARTED || message.event == EventType.TYPE_CONNECTION_FAILED || message.event == EventType.TYPE_CONNECTION_FINISHED) {
                // Read connectId if present
                int connectIdLength = buffer.getInt();
                if (connectIdLength > 0) {
                    byte[] connectIdBytes = new byte[connectIdLength];
                    buffer.get(connectIdBytes);
                    log.info("connectId: {}", new String(connectIdBytes, StandardCharsets.UTF_8));
                    message.setConnectId(new String(connectIdBytes, StandardCharsets.UTF_8));
                }
            }
        }

        // Read errorCode if present
        if (type == MessageType.MSG_TYPE_ERROR) {
            // 从ByteBuffer读取4字节并解析为int（大端序）
            byte[] errorCodeBytes = new byte[4];
            if (buffer.remaining() >= 4) {
                buffer.get(errorCodeBytes);  // 读取4字节到数组
                ByteBuffer wrapper = ByteBuffer.wrap(errorCodeBytes);
                wrapper.order(ByteOrder.BIG_ENDIAN);  // 设置大端序
                message.setErrorCode(wrapper.getInt());
            }
        }

        // Read remaining bytes as payload
        if (buffer.remaining() > 0) {
            // 4 个字节的长度
            int payloadLength = buffer.getInt();
            if (payloadLength > 0) {
                byte[] payloadBytes = new byte[payloadLength];
                buffer.get(payloadBytes);
                message.setPayload(payloadBytes);
            }
        }

        return message;
    }
}
