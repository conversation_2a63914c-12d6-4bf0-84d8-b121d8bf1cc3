package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class LLMConfig {

    private String mode = "CustomLLM";

    private String URL = "https://cerebro-sit.genn.cn/web-api/v1/chat/completions";
    /**
     * <68649edb406695b79e5ec604, gennai-oDAS5DgmML73qikgVnTcRcgRemwfNnV1iTdfVaKres02h9HFRVQtS0Qanmwrd4th>
     * <xxx, gennai-ruO8lvmjGx2lZn2r9xPbNmmSuwzcphiozDfc0xY3tKCffpltrvrVnN>
     */
    private Map<String, String> apiKeyMap = new HashMap<>();

    private Integer maxTokens = 160000;
    private Float temperature = 0.1f;
    private Float topP = 0.3f;
    private Integer historyLength = 0;

    /**
     * 大模型 System 角色预设指令，可用于控制模型输出
     */
    private String[] systemMessages;

    private Boolean prefill = false;

    @NestedConfigurationProperty
    private VisionConfig visionConfig = new VisionConfig();

    String custom;

    @Data
    public static class VisionConfig {

        Boolean enable = true;
        @NestedConfigurationProperty
        SnapshotConfig snapshotConfig = new SnapshotConfig();
        @NestedConfigurationProperty
        StorageConfig storageConfig = new StorageConfig();

        @Data
        public static class SnapshotConfig {
            /**
             * 0 主流
             * 1 屏幕流
             */
            private Integer streamType = 0;
            /**
             * high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。
             * low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。
             * auto：自动模式。根据图片分辨率，自动选择适合的模式。
             * 默认值为 auto。
             */
            private String imageDetail = "high";
            /**
             * 送入大模型视频帧高度，取值范围为 [0, 1792]，单位为像素。
             */
            private Integer height = 640;
            /**
             * 相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。
             */
            private Integer interval = 1000;
            /**
             * 单次送大模型图片数。取值范围为 [0, 50]。
             */
            private Integer imagesLimit = 1;

        }

        @Data
        public static class StorageConfig {
            /**
             * 0 Base 64 编码存入本地，会话结束后自动删除。
             * 1 TOS存储通
             */
            private Integer type = 0; //
            @NestedConfigurationProperty
            private TosConfig tosConfig;

            @Data
            public static class TosConfig {
                private String accountId;
                private Integer region;
                private String bucket;
            }
        }
    }

}
