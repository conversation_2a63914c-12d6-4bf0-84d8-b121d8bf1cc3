package cn.genn.ai.hub.app.application.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 事件回调命令
 * @date 2025-04-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventCallbackCommand<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String challenge;

    private String type;

    private String token;

    private String schema;

    private EventCallbackHeaderCommand header;

    private T event;
}
