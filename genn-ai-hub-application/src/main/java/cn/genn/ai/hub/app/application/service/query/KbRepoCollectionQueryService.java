package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.KbRepoCollectionAssembler;
import cn.genn.ai.hub.app.application.dto.KbCatFullPath;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.MeetPreviewAgentDTO;
import cn.genn.ai.hub.app.application.dto.feishu.ExistFileDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionListQuery;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionQuery;
import cn.genn.ai.hub.app.application.query.MeetPreviewAgentQuery;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCollectionMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepCollectionRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepDataIndexRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepFileRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoCollectionQueryService {

    private final KbRepoCollectionMapper mapper;
    private final KbRepoCollectionAssembler assembler;
    private final KbRepCollectionRepositoryImpl collectionRepository;
    private final KbRepDataIndexRepositoryImpl dataIndexRepository;
    private final FileStorageOperation fileStorageOperation;
    private final KbRepFileRepositoryImpl fileRepository;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoCollectionDTO分页对象
     */
    public PageResultDTO<KbRepoCollectionDTO> page(KbRepoCollectionQuery query) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = buildCollectionQuery(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    private QueryWrapper<KbRepoCollectionPO> buildCollectionQuery(KbRepoCollectionQuery query) {
        QueryWrapper<KbRepoCollectionPO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .lambda()
            // 知识库ID
            .eq(Objects.nonNull(query.getRepoId()), KbRepoCollectionPO::getRepoId, query.getRepoId())
            // 父id
            .eq(Objects.nonNull(query.getPid()), KbRepoCollectionPO::getPid, query.getPid())
            // 集合名称
            .like(StringUtils.isNotBlank(query.getName()), KbRepoCollectionPO::getName, query.getName())
            // 集合类型
            .eq(Objects.nonNull(query.getCollectionType()), KbRepoCollectionPO::getCollectionType, query.getCollectionType())
            // 训练模式
            .eq(Objects.nonNull(query.getTrainingType()), KbRepoCollectionPO::getTrainingType, query.getTrainingType())
            // 处理方式
            .eq(Objects.nonNull(query.getHandleType()), KbRepoCollectionPO::getHandleType, query.getHandleType())
            // 是否启用
            .eq(Objects.nonNull(query.getEnabled()), KbRepoCollectionPO::getEnabled, query.getEnabled())
            // 数据集处理状态
            .eq(Objects.nonNull(query.getCollectionStatus()), KbRepoCollectionPO::getCollectionStatus, query.getCollectionStatus())
            // 创建时间
            .eq(Objects.nonNull(query.getCreateTime()), KbRepoCollectionPO::getCreateTime, query.getCreateTime())
            // 创建人
            .eq(Objects.nonNull(query.getCreateUserId()), KbRepoCollectionPO::getCreateUserId, query.getCreateUserId())
            // 创建者名称
            .eq(StringUtils.isNotBlank(query.getCreateUserName()), KbRepoCollectionPO::getCreateUserName, query.getCreateUserName())
            // 修改时间
            .eq(Objects.nonNull(query.getUpdateTime()), KbRepoCollectionPO::getUpdateTime, query.getUpdateTime())
            // 修改人
            .eq(Objects.nonNull(query.getUpdateUserId()), KbRepoCollectionPO::getUpdateUserId, query.getUpdateUserId())
            // 修改人名称
            .eq(StringUtils.isNotBlank(query.getUpdateUserName()), KbRepoCollectionPO::getUpdateUserName, query.getUpdateUserName())
            .eq(KbRepoCollectionPO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByDesc(KbRepoCollectionPO::getId)
        ;
        return queryWrapper;
    }

    /**
     * 根据id查询
     *
     * @return KbRepoCollectionDTO
     */
    public KbRepoCollectionDTO get(Long id) {
        KbRepoCollectionDTO po2DTO = assembler.PO2DTO(mapper.selectById(id));
        try {
            // 先查询文件信息
            if (po2DTO.getFileId() != null) {
                KbRepoFilePO fileInfo = fileRepository.getBaseMapper().selectById(po2DTO.getFileId());
                if (fileInfo != null) {
                    po2DTO.setContentType(fileInfo.getContentType());
                }
            }
            // 获取外链
            if (StrUtil.isNotBlank(po2DTO.getExternalFileId())) {
                String originName = null;
                if (po2DTO.getExternalFileId().contains("/")) {
                    originName = po2DTO.getExternalFileId().substring(po2DTO.getExternalFileId().lastIndexOf("/") + 1);
                } else {
                    originName = po2DTO.getName();
                }
                String url = fileStorageOperation.preSignedUrlGetObject(po2DTO.getExternalFileId(), originName);
                po2DTO.setExternalFileUrl(url);
            }
        } catch (Exception e) {
            log.error("获取外链失败", e);
        }
        List<KbRepoDataIndexPO> indexList =
            Optional.ofNullable(dataIndexRepository.selectCustomerIndexFromCollection(po2DTO.getRepoId(), po2DTO.getId()))
                .orElse(Lists.newArrayList());
        po2DTO.setIndexAmount(indexList.size());
        return po2DTO;
    }

    /**
     * 根据知识库查询下面的所有的数据集
     */
    public PageResultDTO<KbRepoCollectionDTO> getKbRepoCollectionList(KbRepoCollectionListQuery query) {
        Page<KbRepoCollectionPO> pageResult = collectionRepository.pageKbRepoCollection(query);
        PageResultDTO<KbRepoCollectionDTO> result = assembler.toPageResult(pageResult);
        return result;
    }

    public List<KbCatFullPath> fullPath(IdQuery query) {
        LinkedList<KbCatFullPath> result = Lists.newLinkedList();
        findRootPatch(query.getId(), result);
        return result;
    }

    private void findRootPatch(Long repoId, LinkedList<KbCatFullPath> result) {
        if (repoId == null || repoId == 0) {
            result.push(KbCatFullPath
                .builder()
                .isCat(false)
                .id(0L)
                .name("根目录")
                .build());
            return;
        }
        KbRepoCollectionPO collectionPO = mapper.selectById(repoId);
        result.push(KbCatFullPath
            .builder()
            .isCat(false)
            .id(collectionPO.getId())
            .name(collectionPO.getName())
            .build());
        findRootPatch(collectionPO.getPid(), result);
    }

    public List<ExistFileDTO> alreadyExist(Long repoId) {
        List<KbRepoCollectionDTO> collDTOs = collectionRepository.selectCollectByRepoId(repoId, null);
        return collDTOs.stream().map(coll ->
            ExistFileDTO.builder()
                .collId(coll.getId())
                .repoId(coll.getRepoId())
                .objToken(coll.getExternalFileId())
                // todo
                .sheetId("")
                .build()
        ).collect(Collectors.toList());
    }

    public MeetPreviewAgentDTO queryMeetPreview(MeetPreviewAgentQuery query) {
        MeetPreviewAgentDTO result = new MeetPreviewAgentDTO();
        List<String> docUrls = new ArrayList<>();
        List<MeetPreviewAgentDTO.FileNode> nodes = new ArrayList<>();
        result.setDocUrls(docUrls);
        result.setFileNodes(nodes);

        LocalDate now = LocalDate.now().minusDays(query.getMinusDays());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        String format = now.format(formatter);

        List<KbRepoCollectionDTO> collDTOs = collectionRepository.selectCollByRepoIdAndName(query.getRepoIds(), format);

        for (KbRepoCollectionDTO collDTO : collDTOs) {
            try {
                // 获取外链
                if (StrUtil.isNotBlank(collDTO.getExternalFileId())) {
                    String originName = null;
                    if (collDTO.getExternalFileId().contains("/")) {
                        originName = collDTO.getExternalFileId().substring(collDTO.getExternalFileId().lastIndexOf("/") + 1);
                    } else {
                        originName = collDTO.getName();
                    }
                    String url = fileStorageOperation.preSignedUrlGetObject(collDTO.getExternalFileId(), originName);
                    docUrls.add(url);
                    MeetPreviewAgentDTO.FileNode node = MeetPreviewAgentDTO.FileNode
                        .builder()
                        .fileName(originName)
                        .externalFileUrl(url)
                        .build();

                    nodes.add(node);
                }
            } catch (Exception e) {
                log.error("获取外链失败", e);
            }
        }
        return result;
    }
}

