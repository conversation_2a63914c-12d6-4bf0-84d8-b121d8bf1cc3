package cn.genn.ai.hub.app.application.command;

import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * KbRepoFile操作对象
 *
 * <AUTHOR>
 */
@Data
public class KbRepoFileOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "关联的知识库 ID")
    private Long repoId;

    @Schema(description = "关联的集合 ID（与 collection 表关联）")
    private Long collectionId;

    @Schema(description = "文件存储标识（本地或第三方存储）")
    private String filePlatform;

    @Schema(description = "外部文件 ID（如 S3/OSS）")
    private String externalFileId;

    @Schema(description = "外部文件访问 URL（支持动态变量如 {{fileId}}）")
    private String externalFileUrl;

    @Schema(description = "分块大小（单位：字节）")
    private Integer chunkSize;

    @Schema(description = "文件总大小（单位：字节）")
    private Integer length;

    @Schema(description = "原始文件名（含扩展名）")
    private String fileName;

    @Schema(description = "文件类型（如 text/plain, application/pdf）")
    private String contentType;

    @Schema(description = "扩展元数据（包含权限信息）")
    private String metadata;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;


}

