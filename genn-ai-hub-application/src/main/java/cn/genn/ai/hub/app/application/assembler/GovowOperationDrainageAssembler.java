package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.GovowOperationDrainageCreateCommand;
import cn.genn.ai.hub.app.application.dto.GovowCacheDTO;
import cn.genn.ai.hub.app.application.dto.GovowOperationDrainageDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.GovowOperationDrainagePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 格物运营-用户引流转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GovowOperationDrainageAssembler {

    GovowOperationDrainageAssembler INSTANCE = Mappers.getMapper(GovowOperationDrainageAssembler.class);

    /**
     * 将创建命令转换为PO
     *
     * @param command 创建命令
     * @return PO对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserAvatar", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    GovowOperationDrainagePO createCommand2PO(GovowOperationDrainageCreateCommand command);

    /**
     * PO转DTO
     *
     * @param po PO对象
     * @return DTO对象
     */
    @Mapping(target = "name", source = "userName")
    GovowCacheDTO po2DTO(GovowOperationDrainagePO po);

}
