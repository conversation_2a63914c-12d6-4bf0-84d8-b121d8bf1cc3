package cn.genn.ai.hub.app.application.listener.govow;

import cn.genn.feishu.table.model.FeishuBitableQueryParam;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GovowFsQuestionPushParam {

    /**
     * 查询和发送范围限制
     */
    private SendRange sendRange;

    /**
     * 读取日报时间范围限制,默认处理上周
     */
    private QueryTimeRange queryTimeRange;

    /**
     * startTime和endTime优先级大于type
     */
    @Data
    public static class QueryTimeRange{
        //仅支持以下类型:yesterday,lastWeek,thisWeek,customize
        private String type;

        private LocalDate startTime;

        private LocalDate endTime;
    }

    /**
     * 人员或部门筛选查询和发送人员范围, 默认为所有
     * 两个参数都填关系为且
     */
    @Data
    public static class SendRange{

        /**
         * 飞书手机号码列表
         */
        private List<String> telephones;

        /**
         * 飞书部门列表
         */
        private List<String> departments;

    }

}
