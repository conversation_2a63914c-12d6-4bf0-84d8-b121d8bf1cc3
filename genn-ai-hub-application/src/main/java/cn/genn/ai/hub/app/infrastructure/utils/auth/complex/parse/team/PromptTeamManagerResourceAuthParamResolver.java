package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team;

import cn.genn.ai.hub.app.application.command.PromptPublishCommand;
import cn.genn.ai.hub.app.application.command.PromptSaveCommand;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.PromptMapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.core.model.ddd.IdCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 提示词团队管理资源鉴权
 * @date 2025-05-19
 */
@Component
@RequiredArgsConstructor
public class PromptTeamManagerResourceAuthParamResolver extends TeamManagerResourceAuthParamResolver {

    private final PromptMapper promptMapper;

    @Override
    protected Long getTeamId(AuthInterceptorParam interceptor) {
        String uniqueId = interceptor.getUniqueId();
        Long promptId = switch (uniqueId) {
            case "updatePrompt" -> ((PromptSaveCommand) interceptor.getParamsMap().get("command")).getId();
            case "publishPrompt" -> ((PromptPublishCommand) interceptor.getParamsMap().get("command")).getId();
            case "deletePrompt" -> ((IdCommand) interceptor.getParamsMap().get("command")).getId();
            default -> throw new IllegalArgumentException("Unsupported uniqueId: " + uniqueId);
        };
        return promptMapper.selectById(promptId).getTeamId();
    }
}
