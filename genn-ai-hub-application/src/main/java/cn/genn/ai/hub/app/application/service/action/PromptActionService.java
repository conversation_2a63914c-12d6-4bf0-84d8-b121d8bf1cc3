package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.PromptAssembler;
import cn.genn.ai.hub.app.application.command.PromptDebugCommand;
import cn.genn.ai.hub.app.application.command.PromptOptimizeCommand;
import cn.genn.ai.hub.app.application.command.PromptSaveCommand;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.event.AuthCommonModifyEvent;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.domain.prompt.LLMModelCallUtils;
import cn.genn.ai.hub.app.domain.prompt.PromptVersionGenerator;
import cn.genn.ai.hub.app.domain.prompt.model.entity.Prompt;
import cn.genn.ai.hub.app.domain.prompt.model.entity.PromptVersion;
import cn.genn.ai.hub.app.domain.prompt.optimizer.PromptOptimizerFactory;
import cn.genn.ai.hub.app.domain.prompt.repository.PromptRepository;
import cn.genn.ai.hub.app.infrastructure.constant.PromptConstants;
import cn.genn.ai.hub.app.infrastructure.utils.auth.AuthUtils;
import cn.genn.core.exception.BusinessException;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 提示词应用服务
 * @date 2025-05-14
 */
@Service
@RequiredArgsConstructor
public class PromptActionService {

    private final PromptRepository promptRepository;

    private final PromptAssembler promptAssembler;

    private final PromptOptimizerFactory promptOptimizerFactory;

    private final AIModelManager aiModelManager;

    private final SpringEventPublish springEventPublish;

    /**
     * 创建或更新提示词
     */
    @Transactional
    public void savePrompt(PromptSaveCommand command) {
        Prompt prompt;
        // 校验提示词标题唯一性
        boolean titleUnique = promptRepository.isTitleUnique(command.getTitle(), command.getTeamId(), command.getId());
        if (!titleUnique) {
            throw new BusinessException("提示词标题已存在，请修改后重试。");
        }
        if (command.getId() == null) {
            command.setVersion(PromptConstants.INITIAL_VERSION);
            prompt = Prompt.create(command, promptAssembler);
        } else {
            prompt = promptRepository.findById(command.getId())
                .orElseThrow(() -> new RuntimeException("未找到ID为 " + command.getId() + " 的提示词"));
            String nextVersion = PromptVersionGenerator.generateNextVersion(prompt.getPromptVersions()); // 生成下一个版本号
            command.setVersion(nextVersion);
            prompt.handleSaveCommand(command);
        }
        promptRepository.save(prompt);

        // 创建提示词时，默认添加当前用户的权限
        handleCreatePromptAuth(command.getId() == null, prompt.getId());
    }

    /**
     * 发布当前草稿为一个新的指定版本号。
     */
    @Transactional
    public void publishDraft(Long promptId, String versionToPublish) {
        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new RuntimeException("未找到ID为 " + promptId + " 的提示词"));

        PromptVersion draft = prompt.getDraftVersion()
            .orElseThrow(() -> new RuntimeException("提示词 " + promptId + " 没有可供发布的草稿。"));

        prompt.publishNewVersion(versionToPublish, draft.getContent(), draft.getDescription());
        promptRepository.save(prompt);
    }

    /**
     * 删除提示词
     */
    @Transactional
    public void deletePrompt(Long promptId) {
        Prompt prompt = promptRepository.findById(promptId)
            .orElseThrow(() -> new BusinessException("未找到ID为 " + promptId + " 的提示词"));
        promptRepository.deleteById(prompt.getId());

        // 删除权限
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.PROMPT,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(promptId)), this));
    }

    public SseEmitter optimize(PromptOptimizeCommand command) {
        return promptOptimizerFactory.getOptimizer(command.getOptimizeType()).optimize(command);
    }

    public SseEmitter debug(PromptDebugCommand command) {
        ChatClient chatClient = aiModelManager.getChatClient(Optional.ofNullable(command.getModelKey()).orElse(PromptConstants.DEFAULT_MODEL));
        return LLMModelCallUtils.callStream(chatClient, command.getSystemPrompt(), command.getUserPrompt());
    }

    private void handleCreatePromptAuth(Boolean isAdd, Long promptId) {
        if (!isAdd) {
            return;
        }
        List<AuthCommon> authCommonList = new ArrayList<>();
        authCommonList.add(AuthUtils.buildUserPromptAuth(promptId, CurrentUserHolder.getUserId(), ActionType.MANAGER));
        springEventPublish.publish(AuthCommonModifyEvent
            .buildAddEvent(authCommonList, this));
    }
}
