package cn.genn.ai.hub.app.application.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 插件查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书文档子列表查询参数")
public class FSPluginQuery {

    @Schema(description = "应用ID", required = true)
    @NotEmpty(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "appSecret", required = true)
    @NotEmpty(message = "appSecret 不能为空")
    private String appSecret;

    @Schema(description = "配置的token(wiki)-获取表格sheet列表", required = true)
    private String folderToken;


}
