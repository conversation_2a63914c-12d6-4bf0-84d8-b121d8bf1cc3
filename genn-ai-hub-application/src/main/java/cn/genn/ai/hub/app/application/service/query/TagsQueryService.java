package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.TagsAssembler;
import cn.genn.ai.hub.app.application.dto.TagsDTO;
import cn.genn.ai.hub.app.application.dto.TagsSimpleDTO;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.query.TagsQuery;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.TagsRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.TagsPO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TagsQueryService {

    private final TagsRepositoryImpl tagsRepository;
    private final TagsAssembler tagsAssembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return TagsDTO分页对象
     */
    public PageResultDTO<TagsDTO> page(TagsQuery query) {
        return tagsRepository.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return TagsDTO
     */
    public TagsDTO get(IdQuery query) {
        return tagsRepository.getTag(query.getId());
    }

    /**
     * 根据标签查询条件获取资源key列表
     *
     * @param query 标签查询条件
     * @return 资源key列表
     */
    public Set<String> getResourceKeysByTagConditions(TagsQuery query) {

        // 从查询条件中提取标签ID列表和标签名称
        List<Long> tagIds = query.getTagIds();
        String tagName = query.getName();

        // 如果没有标签ID列表和标签名称，则返回空结果
        if (CollUtil.isEmpty(tagIds) && CharSequenceUtil.isEmpty(tagName)) {
            return Set.of();
        }
        Set<String> resourceKeys = new HashSet<>();

        resourceKeys.addAll(tagsRepository.getResourceKeysByTagIds(tagIds));
        resourceKeys.addAll(tagsRepository.getResourceKeysByNameConditions(query));

        return resourceKeys;
    }

    /**
     * 根据资源key列表获取标签列表，并按资源key分组
     *
     * @param resourceKeys 资源key列表
     * @param tagType      标签类型
     * @param teamId       团队ID
     * @return 按资源key分组的标签DTO列表
     */
    public Map<String, List<TagsDTO>> getTagByResource(List<String> resourceKeys, TagTypeEnum tagType, Long teamId) {
        Map<String, List<Long>> resourceTagRefMap = tagsRepository.getTagIdsByResourceKey(resourceKeys);
        if (MapUtil.isEmpty(resourceTagRefMap)) {
            return Map.of();
        }
        List<TagsPO> tagsPOList = tagsRepository.listByIdsNoTenantIds(resourceTagRefMap.values().stream().flatMap(List::stream).distinct().toList());
        Map<Long, TagsPO> tagMap = tagsPOList.stream().collect(Collectors.toMap(TagsPO::getId, tagsPO -> tagsPO));
        Long currUserId = CurrentUserHolder.getUserId();

        return resourceKeys.stream()
            .filter(resourceTagRefMap::containsKey)
            .collect(Collectors.toMap(
                resourceKey -> resourceKey,
                resourceKey -> resourceTagRefMap.get(resourceKey).stream()
                    .filter(tagMap::containsKey)
                    .map(tagId -> {
                        TagsPO tagsPO = tagMap.get(tagId);
                        if (tagType == tagsPO.getTagType() && // 标签类型匹配
                            (tagType == TagTypeEnum.GENERAL_TOOL  //通用工具不区分空间
                                || teamId == null && tagsPO.getCreateUserId().equals(currUserId) // 个人空间
                                || teamId != null && teamId.equals(tagsPO.getTeamId()))) { // 团队空间
                            return tagsAssembler.PO2DTO(tagsPO);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            ));
    }

    /**
     * 填充标签信息的通用方法
     *
     * @param resourceList 需要填充标签的资源列表
     * @param tagType 标签类型
     * @param teamId 团队ID
     * @param idGetter 获取资源ID的函数
     * @param tagSetter 设置标签列表的函数
     * @param <T> 资源类型
     */
    public <T> void fillTagsInfo(List<T> resourceList, TagTypeEnum tagType, Long teamId,
                                  Function<T, Long> idGetter, BiConsumer<T, List<TagsSimpleDTO>> tagSetter) {
        if (CollUtil.isEmpty(resourceList)) {
            return;
        }

        List<String> resourceIds = resourceList.stream()
            .map(idGetter)
            .map(String::valueOf)
            .collect(Collectors.toList());
        Map<String, List<TagsDTO>> resourceTagMap = getTagByResource(resourceIds, tagType, teamId);
        // 填充标签信息
        for (T resource : resourceList) {
            String resourceId = String.valueOf(idGetter.apply(resource));
            List<TagsDTO> tagsDTOS = resourceTagMap.get(resourceId);
            if (CollUtil.isEmpty(tagsDTOS)) {
                continue;
            }
            List<TagsSimpleDTO> tagList = tagsDTOS.stream()
                .map(tagsDTO -> TagsSimpleDTO.builder()
                    .id(tagsDTO.getId())
                    .name(tagsDTO.getName())
                    .avatar(tagsDTO.getAvatar())
                    .build())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            tagSetter.accept(resource, tagList);
        }
    }
}

