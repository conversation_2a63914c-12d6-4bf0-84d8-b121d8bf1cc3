package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.McpGroupAssembler;
import cn.genn.ai.hub.app.application.command.McpGroupCreateCommand;
import cn.genn.ai.hub.app.application.command.McpGroupPublishCommand;
import cn.genn.ai.hub.app.application.command.McpGroupUpdateCommand;
import cn.genn.ai.hub.app.application.dto.mcp.McpGroupDTO;
import cn.genn.ai.hub.app.application.service.query.McpGroupQueryService;
import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroup;
import cn.genn.ai.hub.app.domain.mcp.service.McpGroupDomainService;
import cn.genn.ai.hub.app.infrastructure.constant.Constants;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ToolInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.ToolInfoPO;
import cn.genn.ai.hub.app.infrastructure.utils.McpGatewayInvokeUtils;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * MCP分组应用操作服务,负责增删改的实现.事务在这一层控制
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class McpGroupActionService {

    private final McpGroupDomainService mcpGroupDomainService;
    private final McpGroupQueryService mcpGroupQueryService;
    private final McpGroupAssembler mcpGroupAssembler;
    private final ToolInfoActionService toolInfoActionService;
    private final ToolInfoMapper toolInfoMapper;

    /**
     * 创建MCP分组
     *
     * @param command MCP分组创建命令
     * @return MCP分组ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createMcpGroup(McpGroupCreateCommand command) {
        McpGroup mcpGroup = mcpGroupAssembler.toEntity(command);
        Long groupId = mcpGroupDomainService.createMcpGroup(mcpGroup, command.getMcpIds());
        McpGatewayInvokeUtils.addMcpGroup(mcpGroupQueryService.get(IdQuery.builder().id(mcpGroup.getId()).build()));
        return groupId;
    }

    /**
     * 更新MCP分组。
     * @param command MCP分组更新命令，包含要更新的MCP分组信息以及关联的MCP ID列表。
     * @return 如果更新成功返回true，否则返回false。
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMcpGroup(McpGroupUpdateCommand command) {
        McpGroup mcpGroup = mcpGroupAssembler.toEntity(command);
        boolean result = mcpGroupDomainService.updateMcpGroup(mcpGroup, command.getMcpIds());
        // 更新通用工具中的MCP分组信息
        toolInfoMapper.update(Wrappers.<ToolInfoPO>lambdaUpdate()
                .set(ToolInfoPO::getName, mcpGroup.getName())
                .set(ToolInfoPO::getIntro, mcpGroup.getDescription())
                .eq(ToolInfoPO::getToolKey, Constants.MCP_COMMON_TOOL_PREFIX + mcpGroup.getGroupKey()));
        McpGatewayInvokeUtils.updateMcpGroup(mcpGroup.getGroupKey(), mcpGroupQueryService.get(IdQuery.builder().id(mcpGroup.getId()).build()));
        return result;
    }

    /**
     * 删除MCP分组
     *
     * @param command ID命令
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMcpGroup(IdCommand command) {
        boolean result = mcpGroupDomainService.deleteMcpGroup(command.getId());
        McpGroupDTO mcpGroupDTO = mcpGroupQueryService.get(IdQuery.builder().id(command.getId()).build());
        McpGatewayInvokeUtils.deleteMcpGroup(mcpGroupDTO.getGroupKey());
        return result;
    }

    /**
     * 发布MCP分组到通用工具
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishMcpGroup(McpGroupPublishCommand command) {
        McpGroupDTO mcpGroupDTO = mcpGroupQueryService.get(IdQuery.builder().id(command.getId()).build());
        Boolean result = toolInfoActionService.publishMcpGroup(mcpGroupDTO.getGroupKey(), mcpGroupDTO.getName(), mcpGroupDTO.getDescription(), command.getToolAvatar(), command.getTagIds());
        //更新发布状态
        mcpGroupDomainService.publishMcpGroup(mcpGroupDTO.getId());
        return result;
    }

    /**
     * 取消发布MCP分组到通用工具。
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean groupUnpublish(IdCommand command) {
        McpGroupDTO mcpGroupDTO = mcpGroupQueryService.get(IdQuery.builder().id(command.getId()).build());
        Boolean result = toolInfoActionService.unpublishMcpGroup(mcpGroupDTO.getGroupKey());
        //更新发布状态
        mcpGroupDomainService.unpublishMcpGroup(mcpGroupDTO.getId());
        return result;
    }
}
