package cn.genn.ai.hub.app.infrastructure.utils.vector.milvus;

import cn.genn.ai.hub.app.infrastructure.utils.vector.FilterBuilder;
import cn.hutool.core.collection.CollUtil;

import java.util.List;
import java.util.function.Function;

/**
 * Fluent风格的FilterBuilder,用于构建Milvus的filter表达式
 * <AUTHOR>
 */
public class MilvusFluentFilterBuilder implements FilterBuilder {

    private final StringBuilder filterExpression = new StringBuilder();

    public static FilterBuilder create() {
        return new MilvusFluentFilterBuilder();
    }

    @Override
    public FilterBuilder eq(String field, Object value) {
        addCondition(field + " == " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder ne(String field, Object value) {
        addCondition(field + " != " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder gt(String field, Object value) {
        addCondition(field + " > " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder gte(String field, Object value) {
        addCondition(field + " >= " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder lt(String field, Object value) {
        addCondition(field + " < " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder lte(String field, Object value) {
        addCondition(field + " <= " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder in(String field, List<?> values) {
        if (CollUtil.isEmpty(values)) {
            return this;
        }
        addCondition(field + " in " + formatArray(values));
        return this;
    }

    @Override
    public FilterBuilder notIn(String field, List<?> values) {
        if (CollUtil.isEmpty(values)) {
            return this;
        }
        addCondition(field + " not in " + formatArray(values));
        return this;
    }

    @Override
    public FilterBuilder like(String field, String pattern) {
        addCondition(field + " LIKE " + formatValue(pattern));
        return this;
    }

    @Override
    public FilterBuilder isNull(String field) {
        addCondition(field + " IS NULL");
        return this;
    }

    @Override
    public FilterBuilder isNotNull(String field) {
        addCondition(field + " IS NOT NULL");
        return this;
    }

    @Override
    public FilterBuilder and(Function<FilterBuilder, FilterBuilder> filterFunction) {
        if (filterExpression.length() > 0) {
            filterExpression.append(" AND (");
        } else {
            filterExpression.append("(");
        }
        filterFunction.apply(this);
        filterExpression.append(")");
        return this;
    }

    @Override
    public FilterBuilder or(Function<FilterBuilder, FilterBuilder> filterFunction) {
        if (filterExpression.length() > 0) {
            filterExpression.append(" OR (");
        } else {
            filterExpression.append("(");
        }
        filterFunction.apply(this);
        filterExpression.append(")");
        return this;
    }

    @Override
    public FilterBuilder not() {
        filterExpression.insert(0, "NOT (");
        filterExpression.append(")");
        return this;
    }

    @Override
    public FilterBuilder add(String field1, String field2, Object value) {
        addCondition(field1 + " + " + field2 + " == " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder subtract(String field1, String field2, Object value) {
        addCondition(field1 + " - " + field2 + " == " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder multiply(String field1, String field2, Object value) {
        addCondition(field1 + " * " + field2 + " == " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder divide(String field1, String field2, Object value) {
        addCondition(field1 + " / " + field2 + " == " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder modulo(String field, Object value) {
        addCondition(field + " % " + formatValue(value) + " == 0");
        return this;
    }

    @Override
    public FilterBuilder power(String field, Object exponent, Object value) {
        addCondition(field + " ** " + formatValue(exponent) + " == " + formatValue(value));
        return this;
    }

    @Override
    public FilterBuilder expression(String expression) {
        if (!filterExpression.isEmpty()) {
            filterExpression.append(" AND ");
        }
        filterExpression.append("(").append(expression).append(")");
        return this;
    }

    @Override
    public String end() {
        return filterExpression.toString();
    }

    private void addCondition(String condition) {
        if (!filterExpression.isEmpty()) {
            filterExpression.append(" AND ");
        }
        filterExpression.append(condition);
    }

    private String formatValue(Object value) {
        if (value instanceof String) {
            return "\"" + value + "\"";
        }
        return String.valueOf(value);
    }

    private String formatArray(List<?> values) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < values.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(formatValue(values.get(i)));
        }
        sb.append("]");
        return sb.toString();
    }
}
