package cn.genn.ai.hub.app.application.listener.event;

import cn.genn.ai.hub.app.application.enums.OpType;
import cn.genn.spring.boot.starter.event.redis.model.RedisBaseEvent;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelManageEvent extends RedisBaseEvent {

    /**
     * 操作类型
     */
    private OpType type;

    /**
     * 模型ID
     */
    private Long modelId;
}
