package cn.genn.ai.hub.app.application.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户问题统计查询对象
 *
 * <AUTHOR>
 */
@Data
public class UserQuestionCountQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流id")
    @NotNull(message = "工作流ID不能为空")
    private String appId;

    @Schema(description = "来源")
    private List<String> sources;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "top N", example = "10")
    private Integer limit = 10;
}
