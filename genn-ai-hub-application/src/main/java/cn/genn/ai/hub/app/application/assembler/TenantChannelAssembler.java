package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.channel.FeishuChannelConfigDTO;
import cn.genn.ai.hub.app.application.dto.channel.TenantChannelDTO;
import cn.genn.ai.hub.app.application.dto.channel.WxMpChannelConfigDTO;
import cn.genn.ai.hub.core.api.channel.FeishuConfig;
import cn.genn.ai.hub.core.api.channel.WxMpConfig;
import cn.genn.ai.hub.app.infrastructure.repository.po.TenantChannelRefPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description 外部渠道转换器
 * @date 2025-04-21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TenantChannelAssembler {

    TenantChannelAssembler INSTANCE = Mappers.getMapper(TenantChannelAssembler.class);

    TenantChannelDTO convert2DTO(TenantChannelRefPO po);

    FeishuChannelConfigDTO convert2FeishuConfigDTO(FeishuConfig feishuConfig);

    WxMpChannelConfigDTO convert2WxMpConfigDTO(WxMpConfig wxMpConfig);
}
