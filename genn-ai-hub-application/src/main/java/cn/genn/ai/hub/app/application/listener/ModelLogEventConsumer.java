package cn.genn.ai.hub.app.application.listener;

import cn.genn.ai.hub.app.application.enums.ModelRequestSource;
import cn.genn.ai.hub.app.application.listener.event.GatewayLogEvent;
import cn.genn.ai.hub.app.application.listener.event.ModelLog;
import cn.genn.ai.hub.app.infrastructure.converter.ModelLogConverter;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ModelLogMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ModelLogPO;
import cn.genn.spring.boot.starter.event.rocketmq.component.BaseConsumerListener;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.hutool.core.text.CharSequenceUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = "${genn.event.rocketmq.naming.topic.genn-ai-gateway-model-log}",
    selectorExpression = "AIModelLog",
    maxReconsumeTimes = 3,
    consumeMode = ConsumeMode.ORDERLY,
    consumerGroup = "${genn.event.rocketmq.naming.consumer.genn-ai-hub-model-log}")
public class ModelLogEventConsumer extends BaseConsumerListener<GatewayLogEvent> {

    @Resource
    private ModelLogMapper modelLogMapper;
    @Resource
    private ModelLogConverter modelLogConverter;
    @Resource
    private AgentInfoRepositoryImpl agentInfoRepository;
    @Resource
    private KbRepoBaseInfoRepositoryImpl kbRepoBaseInfoRepository;
    @Resource
    private IUpmUserService upmUserService;

    @Override
    protected void process(GatewayLogEvent event) {
        log.info("ModelLogEventConsumer process event");
        ModelLog log = event.getLog();
        handleModelLog(log);
    }

    private void handleModelLog(ModelLog log) {
        ModelLogPO modelLogPO = modelLogConverter.MQLogToPO(log);
        if (StringUtils.isNotBlank(log.getUserId()) && !StringUtils.equals(log.getUserId(), "undefined")) {
            try {
                Long userId = Long.parseLong(log.getUserId());
                UpmUserDTO upmUserDTO = upmUserService.get(userId);
                if (upmUserDTO != null) {
                    modelLogPO.setCreateUserId(userId);
                    modelLogPO.setCreateUserName(upmUserDTO.getNick());
                }
            } catch (Exception e) {
               // log不能用？
            }
        }
        if (ModelRequestSource.WORKFLOW.getCode().equals(log.getRequestSource())) {
            String workflowId = modelLogPO.getRequestKey();
            Long tenantId = agentInfoRepository.getTenantIdByWorkflowId(workflowId);
            modelLogPO.setTenantId(tenantId);
        } else if (ModelRequestSource.KNOWLEDGE.getCode().equals(log.getRequestSource())) {
            String knowledgeId = modelLogPO.getRequestKey();
            if (CharSequenceUtil.isNotEmpty(knowledgeId)) {
                Long tenantId = kbRepoBaseInfoRepository.getTenantIdByKnowledgeId(Long.parseLong(knowledgeId));
                modelLogPO.setTenantId(tenantId);
            }
        } else {
            modelLogPO.setTenantId(0L);
        }
        modelLogMapper.insert(modelLogPO);
    }
}
