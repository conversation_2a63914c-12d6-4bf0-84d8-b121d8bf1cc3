package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.KbRepoFileAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoFileQuery;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoFileMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoFileQueryService {

    private final KbRepoFileMapper mapper;
    private final KbRepoFileAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return KbRepoFileDTO分页对象
     */
    public PageResultDTO<KbRepoFileDTO> page(KbRepoFileQuery query) {
        KbRepoFilePO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return KbRepoFileDTO
     */
    public KbRepoFileDTO get(IdQuery query) {
        return assembler.PO2DTO(mapper.selectById(query.getId()));
    }
}

