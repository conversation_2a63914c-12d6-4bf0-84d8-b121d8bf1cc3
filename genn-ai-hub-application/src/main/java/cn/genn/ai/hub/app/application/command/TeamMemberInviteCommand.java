package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 团队成员邀请命令
 *
 * <AUTHOR>
 */
@Data
public class TeamMemberInviteCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队ID")
    @NotNull(message = "团队ID不能为空")
    private Long teamId;

    @Schema(description = "被邀请用户ID列表")
    @NotNull(message = "被邀请用户ID列表不能为空")
    private List<Long> userIds;

}
