package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoQaPairMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoQaPairPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbRepoQaAuthParamResolver extends AbstractRepoAuthParamResolver {

    private final KbRepoQaPairMapper repoQaPairMapper;
    @Override
    protected Long getRepoIdById(Long id) {
        KbRepoQaPairPO kbRepoQaPairPO = repoQaPairMapper.selectById(id);
        return kbRepoQaPairPO.getRepoId();
    }
}
