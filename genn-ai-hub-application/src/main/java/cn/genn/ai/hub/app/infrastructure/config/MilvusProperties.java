package cn.genn.ai.hub.app.infrastructure.config;

import io.milvus.v2.common.IndexParam;
import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class MilvusProperties {

    /**
     * milvus服务地址
     */
    private String url;

    /**
     * 默认重排序模型名称
     */
    private String rerankModel = "bge-reranker-v2-m3";

    /**
     * 默认知识库分块集合名称
     */
    private String collectionName = "knowledge";

    /**
     * 默认知识库问答对集合名称
     */
    private String collectionNameOfQaPair = "qa_pair";

    /**
     * 向量模型和向量库分块集合名称的对应关系 每个 embeddingModel ->对应一个集合
     */
    private Map<String, String> embeddingModelToCollectionNameMapping;

    /**
     * 向量模型和问答对的对应关系
     */
    private Map<String, String> embeddingModelToCollectionNameOfQaPairMapping;

    /**
     * 问答对拦截匹配的最低分数
     */
    private Double qaPairInterceptScoreThreshold = 0.9;


    /**
     * 默认数据库名称
     */
    private String databaseName = "genn_cerebro";

    /**
     * 默认语义检索字段
     */
    private String semanticField = "embedding";

    /**
     * 默认语义检索字段索引类型
     */
    private IndexParam.MetricType semanticMetricType = IndexParam.MetricType.COSINE;

    /**
     * 默认全文检索检索字段
     */
    private String keywordField = "content_embeddings";

    /**
     * 默认全文检索字段索引类型
     */
    private IndexParam.MetricType keywordMetricType = IndexParam.MetricType.BM25;

    /**
     * 混合检索时权重策略
     */
    @NestedConfigurationProperty
    private RerankerProperties reranker = new RerankerProperties();


    public String getCollectionName(String embeddingModel) {
        // 先从embeddingModelToCollectionNameMapping获取
        if (embeddingModelToCollectionNameMapping != null && !embeddingModelToCollectionNameMapping.isEmpty()) {
            return embeddingModelToCollectionNameMapping.getOrDefault(embeddingModel, collectionName);
        }
        // 获取不到返回默认值
        return collectionName;
    }

    public String getCollectionNameOfQaPair(String embeddingModel) {
        // 先从embeddingModelToCollectionNameOfQaPairMapping获取
        if (embeddingModelToCollectionNameOfQaPairMapping != null && !embeddingModelToCollectionNameOfQaPairMapping.isEmpty()) {
            return embeddingModelToCollectionNameOfQaPairMapping.getOrDefault(embeddingModel, collectionNameOfQaPair);
        }
        // 获取不到返回默认值
        return collectionNameOfQaPair;
    }
}
