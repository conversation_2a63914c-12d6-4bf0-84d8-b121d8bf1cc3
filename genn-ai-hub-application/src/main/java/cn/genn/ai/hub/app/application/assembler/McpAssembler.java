package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.McpOperateCommand;
import cn.genn.ai.hub.app.application.dto.mcp.McpDTO;
import cn.genn.ai.hub.app.domain.mcp.model.entity.Mcp;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpPO;
import cn.genn.core.model.page.PageResultDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * MCP装配器，负责DTO与领域实体之间的转换
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface McpAssembler {

    McpAssembler INSTANCE = Mappers.getMapper(McpAssembler.class);

    /**
     * 将领域实体转换为DTO
     *
     * @param mcp 领域实体
     * @return DTO
     */
    McpDTO toDTO(Mcp mcp);

    /**
     * 将DTO转换为领域实体
     *
     * @param mcpDTO DTO
     * @return 领域实体
     */
    Mcp toEntity(McpDTO mcpDTO);

    /**
     * 将操作命令转换为领域实体
     *
     * @param command 操作命令
     * @return 领域实体
     */
    Mcp toEntity(McpOperateCommand command);

    /**
     * 将分页结果转换为DTO分页结果
     *
     * @param page 分页结果
     * @return DTO分页结果
     */
    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<McpDTO> toPageResult(IPage<McpPO> page);
}
