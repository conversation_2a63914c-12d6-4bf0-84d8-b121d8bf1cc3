package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * AgentInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentInfoQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "标签id列表")
    private List<Long> tagIds;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "团队id，如果为空，代表是个人空间创建")
    private Long teamId;

    @Schema(description = "mongo对应的工作流id")
    private String workflowId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "智能体类型，chat：CHAT-对话智能体, workflow：WORKFLOW-工作流智能体, tool_workflow：TOOL_WORKFLOW-工作流工具, tool_http：TOOL_HTTP-HTTP工具")
    @NotNull(message = "智能体类型不能为空")
    private AgentTypeEnum agentType;

    @Schema(description = "是否是全部智能体")
    private BooleanTypeEnum all;

    @Schema(description = "描述")
    private String description;


}

