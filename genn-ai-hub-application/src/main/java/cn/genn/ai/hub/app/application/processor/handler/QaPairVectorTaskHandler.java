package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.dto.KbRepoQaPairDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskOfQaPairVectorBody;
import cn.genn.ai.hub.app.application.enums.HandleStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.application.service.action.KbRepoQaPairActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoQaPairQueryService;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.QaPairVectorUtil;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.QA_PAIR_VECTOR;

/**
 * 问答对向量化任务处理器
 */
@Component
@Slf4j
public class QaPairVectorTaskHandler extends AbstractTaskHandler {

    @Resource
    private KbRepoQaPairActionService qaPairActionService;
    @Resource
    private KbRepoQaPairQueryService qaPairQueryService;
    @Resource
    private RepoTaskRepositoryImpl taskRepository;
    @Resource
    private QaPairVectorUtil qaPairVectorUtil;

    @Override
    public TaskTypeEnum getType() {
        return QA_PAIR_VECTOR;
    }


    @Override
    public void invoke(KbRepoTaskDTO task) {
        // 异步调用算法接口的实现
        // 接受算法的异步回调结果 更新任务状态 更新库里索引的算法处理状态
        async(() -> {
            KbRepoTaskOfQaPairVectorBody body = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfQaPairVectorBody.class);
            KbRepoQaPairDTO qaPairDTO =  qaPairQueryService.getByQaPairKey(body.getQaPairKey());
            if (Objects.isNull(qaPairDTO)) {
                // 更新任务为完成
                taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
                return;
            }
            qaPairActionService.updateHandleStatusOfQaPairKey(body.getQaPairKey(), HandleStatusEnum.PROCESSING);
            qaPairVectorUtil.handleQaPairVector(body.getVectorModelKey(), qaPairDTO);

            // 更新问答对为算法处理完成
            qaPairActionService.updateHandleStatusOfQaPairKey(body.getQaPairKey(), HandleStatusEnum.DONE);
            // 更新任务为完成
            taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
            // 移除进行队列任务
            SpringUtil.getBean(TaskHandlerFactory.class).removeProcessTask(task);
        });
    }

}
