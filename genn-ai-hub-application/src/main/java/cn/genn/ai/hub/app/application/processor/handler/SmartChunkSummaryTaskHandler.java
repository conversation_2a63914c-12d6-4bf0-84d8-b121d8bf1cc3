package cn.genn.ai.hub.app.application.processor.handler;

import cn.genn.ai.hub.app.application.dto.KbRepoDataDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskOfIndexVectorBody;
import cn.genn.ai.hub.app.application.dto.KbRepoTaskOfSmartChunkSummaryBody;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.enums.TaskTypeEnum;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.application.service.action.KbRepoDataActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoDataIndexActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoTaskActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataQueryService;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.ai.utils.AIUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Component;

import static cn.genn.ai.hub.app.application.enums.TaskTypeEnum.SMART_CHUNK_SUMMARY;

/**
 * 智能分块摘要任务处理
 */
@Component
@Slf4j
public class SmartChunkSummaryTaskHandler extends AbstractTaskHandler {

    @Resource
    private KbRepoDataActionService dataActionService;
    @Resource
    private KbRepoDataQueryService dataQueryService;
    @Resource
    private KbRepoDataIndexActionService dataIndexActionService;
    @Resource
    private RepoTaskRepositoryImpl taskRepository;

    @Override
    public TaskTypeEnum getType() {
        return SMART_CHUNK_SUMMARY;
    }

    @Override
    public void invoke(KbRepoTaskDTO task) {
        // 异步调用算法接口的实现
        // 接受算法的异步回调结果 更新任务状态 更新库里索引的算法处理状态
        async(() -> {
            KbRepoTaskOfSmartChunkSummaryBody body = JsonUtils.parse(task.getBusinessBody(), KbRepoTaskOfSmartChunkSummaryBody.class);
            KbRepoDataDTO dataDTO = dataQueryService.selectDataByKey(body.getDataKey());
            if (dataDTO == null) {
                log.error("数据不存在");
                return;
            }
            /**
             * 调用大模型生成智能分块摘要
             */
            String smartSummary = "";
            try {
                AIModelManager modelManager = SpringUtil.getBean(AIModelManager.class);
                ChatClient chatClient = modelManager.getChatClient(body.getAgentModelKey());
                String prompt = buildPrompt(dataDTO.getQuestion());
                String aiAnswer = chatClient.prompt(prompt)
                        .call()
                        .content();
                smartSummary = AIUtils.removeCodeBlock(aiAnswer);
                log.info("智能分块摘要生成成功 dataKey:{} smartSummary:{}", dataDTO.getDataKey(), smartSummary);
            } catch (Exception e) {
                log.error("智能分块摘要生成失败", e);
            }
            if (StrUtil.isNotBlank(smartSummary)) {
                dataActionService.updateDataContent(body.getDataKey(), null, smartSummary);
            }
            // 更新任务为完成
            taskRepository.updateTaskStatus(task.getId(), TaskStatusEnum.COMPLETED);
            SpringUtil.getBean(TaskHandlerFactory.class).removeProcessTask(task);

            // 生成智能分块摘要成功后 创建索引向量化的任务
            KbRepoTaskOfIndexVectorBody kbRepoTaskOfIndexVectorBody = KbRepoTaskOfIndexVectorBody.builder()
                    .tenantId(task.getTenantId())
                    .repoId(body.getRepoId())
                    .collectionId(body.getCollectionId())
                    .dataKey(body.getDataKey())
                    .indexKey(body.getIndexKey())
                    .rawText(dataIndexActionService.handleRawText(body.getDataKey(), body.getIndexKey()))
                    .vectorModelKey(body.getVectorModelKey())
                    .build();
            SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfIndexVector(kbRepoTaskOfIndexVectorBody);
        });
    }

    private String buildPrompt(String question) {
        return KbRepoDataActionService.DEFAULT_OPTIMIZATION_PROMPT.replace("{{cluster_content}}", question);
    }



}
