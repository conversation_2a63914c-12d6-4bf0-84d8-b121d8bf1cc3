package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.enums.TaskStatusEnum;
import cn.genn.ai.hub.app.application.processor.TaskHandler;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.application.queues.QueueManageImpl;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RetryProcessTasks extends IJobHandler {

    @Resource
    protected RepoTaskRepositoryImpl taskRepository;

    @Resource
    protected TaskHandlerFactory handlerFactory;

    /**
     * TODO
     *
     * @throws Exception
     */
    @Override
    public void execute() throws Exception {
        log.info("RetryProcessTasks begin");
        String jobParam = XxlJobHelper.getJobParam();
        if (StrUtil.isNotEmpty(jobParam)) {
            log.info("RetryProcessTasks process param {}", jobParam);
            String[] split = StringUtils.split(jobParam, ",");
            for (String taskId : split) {
                KbRepoTaskDTO info = taskRepository.getInfo(Long.parseLong(taskId));
//                if (!info.getTaskStatus().equals(TaskStatusEnum.PROCESSING)) {
//                    continue;
//                }

                GennRequestContext.initTenantId(info.getTenantId());
                TaskHandler handler = handlerFactory.getHandler(info.getTaskType());
                String processQueueName = handler.getProcessQueueName(Long.toString(info.getTenantId()));
                // 检查处理队列中任务
                handler.invoke(info);
                GennRequestContext.clear();
            }
            return;
        } else {
            // 把等待中的任务加入到处理队列中
            List<KbRepoTaskDTO> pendingTasks = taskRepository.findPendingTasks();
            for (KbRepoTaskDTO pendingTask : pendingTasks) {
                GennRequestContext.initTenantId(pendingTask.getTenantId());

                handlerFactory.getHandler(pendingTask.getTaskType())
                    .handleTask(Long.toString(pendingTask.getId()), Long.toString(pendingTask.getTenantId()));

                GennRequestContext.clear();
            }
        }

        log.info("RetryProcessTasks end");
    }


}
