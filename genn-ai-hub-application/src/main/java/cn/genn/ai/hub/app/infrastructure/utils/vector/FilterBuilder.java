package cn.genn.ai.hub.app.infrastructure.utils.vector;

import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public interface FilterBuilder {
    FilterBuilder eq(String field, Object value);
    FilterBuilder ne(String field, Object value);  // 添加不等于操作符
    FilterBuilder gt(String field, Object value);
    FilterBuilder gte(String field, Object value);
    FilterBuilder lt(String field, Object value);
    FilterBuilder lte(String field, Object value);
    FilterBuilder in(String field, List<?> values);
    FilterBuilder notIn(String field, List<?> values);
    FilterBuilder like(String field, String pattern);  // 添加 LIKE 操作符
    FilterBuilder isNull(String field);  // 添加 IS NULL 操作符
    FilterBuilder isNotNull(String field);  // 添加 IS NOT NULL 操作符
    FilterBuilder and(Function<FilterBuilder, FilterBuilder> filterFunction);
    FilterBuilder or(Function<FilterBuilder, FilterBuilder> filterFunction);
    FilterBuilder not();

    // 添加算术操作符方法
    FilterBuilder add(String field1, String field2, Object value);
    FilterBuilder subtract(String field1, String field2, Object value);
    FilterBuilder multiply(String field1, String field2, Object value);
    FilterBuilder divide(String field1, String field2, Object value);
    FilterBuilder modulo(String field1, Object value);
    FilterBuilder power(String field, Object exponent, Object value);

    // 添加表达式方法
    FilterBuilder expression(String expression);

    String end();
}
