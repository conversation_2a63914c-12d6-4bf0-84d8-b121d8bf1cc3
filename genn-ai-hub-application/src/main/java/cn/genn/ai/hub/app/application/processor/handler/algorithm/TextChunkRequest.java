package cn.genn.ai.hub.app.application.processor.handler.algorithm;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TextChunkRequest {

    private Long tenantId;
    private Long taskId;
    private Long repoId;
    private Long fileId;
    private Long collectionId;

    /**
     * 用户传入的未分段的原始文本
     */
    private String rawText;

    /**
     * 新增或更新算法那边的分块内容的时候需要
     */
    private String dataKey;

    /**
     * 是否新增或更新算法那边的分块内容
     */
    private Boolean sync;

    private String contentType;


}
