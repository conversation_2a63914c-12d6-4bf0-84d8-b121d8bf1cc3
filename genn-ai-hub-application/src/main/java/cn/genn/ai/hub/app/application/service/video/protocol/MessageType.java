package cn.genn.ai.hub.app.application.service.video.protocol;

import lombok.Getter;

@Getter
public enum MessageType {
    MSG_TYPE_INVALID((byte) 0),
    MSG_TYPE_FULL_CLIENT((byte) 0b1),
    MSG_TYPE_AUDIO_ONLY_CLIENT((byte) 0b10),
    MSG_TYPE_FULL_SERVER((byte) 0b1001),
    MSG_TYPE_AUDIO_ONLY_SERVER((byte) 0b1011),
    MSG_TYPE_FRONT_END_RESULT_SERVER((byte) 0b1100),
    MSG_TYPE_ERROR((byte) 0b1111);

    private final byte value;

    MessageType(byte value) {
        this.value = value;
    }

    public static MessageType fromValue(int value) {
        for (MessageType type : MessageType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown MessageType value: " + value);
    }

    @Override
    public String toString() {
        switch (this) {
            case MSG_TYPE_FULL_CLIENT:
                return "FullClient";
            case MSG_TYPE_AUDIO_ONLY_CLIENT:
                return "AudioOnlyClient";
            case MSG_TYPE_FULL_SERVER:
                return "FullServer";
            case MSG_TYPE_AUDIO_ONLY_SERVER:
                return "AudioOnlyServer/ServerACK";
            case MSG_TYPE_ERROR:
                return "Error";
            case MSG_TYPE_FRONT_END_RESULT_SERVER:
                return "TtsFrontEndResult";
            default:
                return "MessageType(" + value + ")";
        }
    }
}
