package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoCatDetailDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCategoryQuery;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoCategoryDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCategoryPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoCategoryAssembler extends QueryAssembler<KbRepoCategoryQuery, KbRepoCategoryPO, KbRepoCategoryDTO>{

    KbRepoCategoryAssembler INSTANCE = Mappers.getMapper(KbRepoCategoryAssembler.class);

    @Mapping(target = "catId", source = "id")
    @Mapping(target = "catpId", source = "pid")
    KbRepoCatDetailDTO KbRepoCategoryDTO2KbRepoCatDetailDTO(KbRepoCategoryDTO catDTO);
}

