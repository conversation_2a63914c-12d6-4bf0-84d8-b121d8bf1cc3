package cn.genn.ai.hub.app.application.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.database.mybatisplus.query.annotation.QueryType;
import cn.genn.database.mybatisplus.query.constant.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * MCP分组查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class McpGroupQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "分组名称")
    @QueryType(condition = Condition.LIKE)
    private String name;

    @Schema(description = "分组唯一标识")
    @QueryType
    private String groupKey;
}
