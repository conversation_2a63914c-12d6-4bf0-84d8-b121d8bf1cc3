package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CollectionTypeEnum {

    FOLDER("folder", "文件夹"),
    FILE("file", "文件"),
    MANUAL_TEXT("manual_text", "手动文本"),
    WEB("web", "网页"),
    SHEET("sheet", "表格"),
    FS_DOCX("fs_docx", "飞书文档"),
    FS_SHEET("fs_sheet", "飞书电子表格"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    CollectionTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static boolean isFSDoc(CollectionTypeEnum type) {
        return FS_DOCX.equals(type) || FS_SHEET.equals(type);
    }
}

