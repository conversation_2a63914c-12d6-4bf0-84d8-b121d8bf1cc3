package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.CodeRepoAssembler;
import cn.genn.ai.hub.app.application.dto.CodeRepoDTO;
import cn.genn.ai.hub.app.application.enums.CodeRepoTypeEnum;
import cn.genn.ai.hub.app.application.query.CodeRepoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.CodeRepoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.CodeRepoPO;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 代码库查询服务
 * @date 2025-05-16
 */
@Service
@RequiredArgsConstructor
public class CodeRepoQueryService {

    private final CodeRepoRepositoryImpl codeRepoRepository;

    private final CodeRepoAssembler codeRepoAssembler;

    @IgnoreTenant
    public PageResultDTO<CodeRepoDTO> page(CodeRepoQuery query) {

        Page<CodeRepoPO> page = new Page<>(query.getPageNo(), query.getPageSize());
        LambdaQueryWrapper<CodeRepoPO> wrapper = new LambdaQueryWrapper<>();

        // 通用查询条件：名称和代码语言类型
        wrapper.like(StrUtil.isNotBlank(query.getName()), CodeRepoPO::getName, query.getName())
            .eq(query.getCodeType() != null, CodeRepoPO::getCodeType, query.getCodeType());

        // 根据 codeRepoType 是否传入，构造不同的查询逻辑
        if (query.getCodeRepoType() == null) {
            // 情况1: codeRepoType 未传入，查询系统代码库 或 用户代码库
            wrapper.and(mainOr -> mainOr
                // 条件A: 系统代码库 (codeRepoType = SYSTEM)，不进行 teamId 或 createUserId 过滤
                .eq(CodeRepoPO::getCodeRepoType, CodeRepoTypeEnum.SYSTEM)
                // 条件B: 用户代码库 (codeRepoType = USER)，并应用 teamId 或 createUserId 过滤
                .or(userRepoConditions -> {
                    userRepoConditions.eq(CodeRepoPO::getCodeRepoType, CodeRepoTypeEnum.USER);
                    userRepoConditions.eq(CodeRepoPO::getTenantId, CurrentUserHolder.getTenantId());
                    if (query.getTeamId() != null) {
                        // 团队空间的用户代码库
                        userRepoConditions.eq(CodeRepoPO::getTeamId, query.getTeamId());
                    } else {
                        // 个人空间的用户代码库
                        userRepoConditions.isNull(CodeRepoPO::getTeamId);
                        userRepoConditions.eq(CodeRepoPO::getCreateUserId, CurrentUserHolder.getUserId());
                    }
                })
            );
        } else {
            // 情况2: codeRepoType 已传入，按指定类型查询
            wrapper.eq(CodeRepoPO::getCodeRepoType, query.getCodeRepoType());

            // 如果查询的是用户代码库 (USER)，则应用 teamId 或 createUserId 过滤
            if (CodeRepoTypeEnum.USER.equals(query.getCodeRepoType())) {
                wrapper.eq(CodeRepoPO::getTenantId, CurrentUserHolder.getTenantId());
                if (query.getTeamId() != null) {
                    // 团队空间的用户代码库
                    wrapper.eq(CodeRepoPO::getTeamId, query.getTeamId());
                } else {
                    // 个人空间的用户代码库
                    wrapper.isNull(CodeRepoPO::getTeamId);
                    wrapper.eq(CodeRepoPO::getCreateUserId, CurrentUserHolder.getUserId());
                }
            }
        }

        wrapper.orderByDesc(CodeRepoPO::getCreateTime); // 统一按创建时间降序
        Page<CodeRepoPO> codeRepoPOPage = codeRepoRepository.getBaseMapper().selectPage(page, wrapper);
        List<CodeRepoDTO> codeRepoDTOS = codeRepoAssembler.toDTO(codeRepoPOPage.getRecords()); // 变量名修改得更通用
        return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), codeRepoPOPage.getTotal(), codeRepoDTOS);
    }
}
