package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.dto.AsyncGatewayRequest;
import cn.genn.ai.hub.app.application.dto.ModelManageDTO;
import cn.genn.ai.hub.app.application.dto.VerifyAgentDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FSUserAccessTokenResp;
import cn.genn.ai.hub.app.application.enums.OpType;
import cn.genn.ai.hub.app.infrastructure.config.AIGatewayProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 同步网关缓存
 *
 * @Author: cuiyp
 */
@Slf4j
@Component
public class SyncAIGatewayCacheService {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private GennAIHubProperties gennAIHubProperties;

    public void asyncModel(ModelManageDTO modelInfo, OpType type) {
        Thread.startVirtualThread(() -> {
            AIGatewayProperties aiGateway = gennAIHubProperties.getAiGateway();
            HttpHeaders headers = buildHttpHeaders();
            AsyncGatewayRequest body = new AsyncGatewayRequest();
            body.setModelInfo(modelInfo);
            body.setOpType(type);
            restTemplate.postForObject(aiGateway.getModeURL(), new HttpEntity<>(body, headers), FSUserAccessTokenResp.class);
        });
    }

    public void asyncAgent(VerifyAgentDTO agent) {
        Thread.startVirtualThread(() -> {
            AIGatewayProperties aiGateway = gennAIHubProperties.getAiGateway();
            HttpHeaders headers = buildHttpHeaders();
            restTemplate.postForObject(aiGateway.getAgentURL(), new HttpEntity<>(agent, headers), FSUserAccessTokenResp.class);
        });
    }

    public void asyncSensitive() {
        Thread.startVirtualThread(() -> {
            AIGatewayProperties aiGateway = gennAIHubProperties.getAiGateway();
            HttpHeaders headers = buildHttpHeaders();
            restTemplate.postForObject(aiGateway.getSensitiveURL(), new HttpEntity<>(null, headers), FSUserAccessTokenResp.class);
        });
    }

    private HttpHeaders buildHttpHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=utf-8");
        headers.add("Authorization", "Bearer " + gennAIHubProperties.getDefaultApiKey());
        return headers;
    }

}
