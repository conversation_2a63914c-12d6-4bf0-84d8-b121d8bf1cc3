package cn.genn.ai.hub.app.application.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 敏感词校验命令
 * @date 2025-05-16
 */
@Data
public class SensitiveWordVerifyCommand {

    @Schema(description = "工作流id")
    @NotEmpty(message = "工作流id不能为空")
    private String workflowId;

    @Schema(description = "团队ID，如果为null，则结合userId表示个人空间")
    private Long teamId;

    @Schema(description = "用户ID，当teamId为null时，用于查找个人空间敏感词。如果teamId和userId都为null，则只校验系统敏感词。")
    private Long userId;

    @NotEmpty(message = "待检查文本不能为空")
    @Schema(description = "内容")
    private String content;
}
