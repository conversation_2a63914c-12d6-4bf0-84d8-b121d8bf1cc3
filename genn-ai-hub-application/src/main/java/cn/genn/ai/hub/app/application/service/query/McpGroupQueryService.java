package cn.genn.ai.hub.app.application.service.query;

import cn.genn.ai.hub.app.application.assembler.McpAssembler;
import cn.genn.ai.hub.app.application.assembler.McpGroupAssembler;
import cn.genn.ai.hub.app.application.dto.TagsDTO;
import cn.genn.ai.hub.app.application.dto.mcp.*;
import cn.genn.ai.hub.app.application.enums.McpTypeEnum;
import cn.genn.ai.hub.app.application.enums.OverallStatus;
import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.ai.hub.app.application.query.McpGroupQuery;
import cn.genn.ai.hub.app.domain.mcp.model.entity.Mcp;
import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroup;
import cn.genn.ai.hub.app.domain.mcp.model.entity.McpGroupRef;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.McpConfig;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.SseMcpConfig;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.StreamableHttpMcpConfig;
import cn.genn.ai.hub.app.domain.mcp.model.valobj.Tool;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpGroupRefRepository;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpGroupRepository;
import cn.genn.ai.hub.app.domain.mcp.repository.IMcpRepository;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.Constants;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.McpGroupMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.ToolInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.McpGroupPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.ToolInfoPO;
import cn.genn.ai.hub.app.infrastructure.utils.McpGatewayInvokeUtils;
import cn.genn.core.exception.CheckException;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.query.QueryWrapperUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * MCP分组应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class McpGroupQueryService {

    private final McpGroupMapper mapper;
    private final McpGroupAssembler assembler;
    private final McpAssembler mcpAssembler;
    private final IMcpGroupRepository mcpGroupRepository;
    private final IMcpGroupRefRepository mcpGroupRefRepository;
    private final IMcpRepository mcpRepository;
    private final GennAIHubProperties gennAIHubProperties;
    private final ToolInfoMapper toolInfoMapper;
    private final TagsQueryService tagsQueryService;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return McpGroupDTO分页对象
     */
    public PageResultDTO<McpGroupDTO> page(McpGroupQuery query) {
        log.info("分页查询MCP分组列表, query: {}", query);
        // 创建查询条件
        QueryWrapper<McpGroupPO> queryWrapper = QueryWrapperUtil.build(query);
        queryWrapper.lambda().eq(McpGroupPO::getDeleted, DeletedTypeEnum.NOT_DELETED).
                orderByDesc(McpGroupPO::getId);
        // 执行查询
        Page<McpGroupPO> page = mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper);
        // 转换为DTO
        PageResultDTO<McpGroupDTO> result = assembler.toPageResult(page);

        // 查询每个分组关联的MCP列表
        if (result.getList() != null && !result.getList().isEmpty()) {
            // 获取所有分组ID
            List<Long> groupIds = result.getList().stream()
                    .map(McpGroupDTO::getId)
                    .collect(Collectors.toList());
            List<String> toolKeys = result.getList().stream().map(mcpGroupDTO -> Constants.MCP_COMMON_TOOL_PREFIX + mcpGroupDTO.getGroupKey()).collect(Collectors.toList());

            // 批量获取分组关联的MCP列表
            Map<Long, List<McpDTO>> groupMcpMap = getMcpListByGroupIds(groupIds);

            // 批量获取分组关联的通用工具列表
            List<ToolInfoPO> toolInfoPOList = toolInfoMapper.selectList(Wrappers.<ToolInfoPO>lambdaQuery().in(ToolInfoPO::getToolKey, toolKeys));

            Map<String, ToolInfoPO> toolMap = toolInfoPOList.stream().collect(Collectors.toMap(ToolInfoPO::getToolKey, toolInfoPO -> toolInfoPO));
            Map<String, List<TagsDTO>> tagMap = new HashMap<>();
            if (CollUtil.isNotEmpty(toolInfoPOList)) {
                tagMap =tagsQueryService.getTagByResource(toolInfoPOList.stream().map(ToolInfoPO::getId).map(String::valueOf).collect(Collectors.toList()), TagTypeEnum.GENERAL_TOOL, null);
            }

            // 设置每个分组的MCP列表
            for (McpGroupDTO dto : result.getList()) {
                dto.setMcpList(groupMcpMap.getOrDefault(dto.getId(), Collections.emptyList()));
                McpGroupPublishDTO publishDTO = new McpGroupPublishDTO();
                ToolInfoPO toolInfoPO = toolMap.get(Constants.MCP_COMMON_TOOL_PREFIX + dto.getGroupKey());
                if (toolInfoPO != null) {
                    publishDTO.setToolAvatar(toolInfoPO.getAvatar());
                    publishDTO.setTagIds(tagMap.getOrDefault(toolInfoPO.getId().toString(), Collections.emptyList()).stream().map(TagsDTO::getId).collect(Collectors.toList()));
                    dto.setPublishInfo(publishDTO);
                }
            }
        }

        return result;
    }

    /**
     * 根据id查询
     *
     * @param query ID查询对象
     * @return McpGroupDTO
     */
    public McpGroupDTO get(IdQuery query) {
        log.info("根据ID查询MCP分组, query: {}", query);
        // 使用仓储接口获取领域实体
        McpGroup mcpGroup = mcpGroupRepository.getById(query.getId());
        if (mcpGroup == null) {
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST);
        }

        // 使用装配器将领域实体转换为DTO
        McpGroupDTO dto = assembler.toDTO(mcpGroup);

        // 查询关联的MCP列表
        Map<Long, List<McpDTO>> groupMcpMap = getMcpListByGroupIds(Collections.singletonList(dto.getId()));
        dto.setMcpList(groupMcpMap.getOrDefault(dto.getId(), Collections.emptyList()));
        dto.setMcpIds(dto.getMcpList().stream()
                .map(McpDTO::getId)
                .collect(Collectors.toList()));
        //查询发布相关配置
        ToolInfoPO toolInfoPO = toolInfoMapper.selectOne(Wrappers.<ToolInfoPO>lambdaQuery().eq(ToolInfoPO::getToolKey, Constants.MCP_COMMON_TOOL_PREFIX + dto.getGroupKey()));
        if (toolInfoPO != null) {
            McpGroupPublishDTO publishDTO = new McpGroupPublishDTO();
            publishDTO.setToolAvatar(toolInfoPO.getAvatar());
            Map<String, List<TagsDTO>> tagMap = tagsQueryService.getTagByResource(Collections.singletonList(toolInfoPO.getId().toString()), TagTypeEnum.GENERAL_TOOL, null);
            publishDTO.setTagIds(tagMap.getOrDefault(toolInfoPO.getId().toString(), Collections.emptyList()).stream().map(TagsDTO::getId).collect(Collectors.toList()));
            dto.setPublishInfo(publishDTO);
        }
        return dto;
    }


    public McpExposeDTO getExposeConfigByGroupKey(String groupKey) {
        log.info("获取分组暴露配置信息，groupKey: {}", groupKey);
        McpGroup mcpGroup = mcpGroupRepository.getByGroupKey(groupKey);
        if (mcpGroup == null) {
            log.warn("未找到 groupKey 为 '{}' 的MCP分组", groupKey);
            throw new CheckException(MessageCode.RESOURCE_NOT_EXIST, "MCP分组 (key: " + groupKey + ") 不存在");
        }

        McpConfig serverConfig = mcpGroup.getServerConfig();

        if (serverConfig == null) {
            //设置默认配置
            serverConfig = new SseMcpConfig();
            serverConfig.setMcpType(McpTypeEnum.SSE);
            serverConfig.setTimeout(60); // 默认超时时间
            ((SseMcpConfig) serverConfig).setUrl(gennAIHubProperties.getMcp().getMcpGatewayUrl() + "/sse" + "?group=" + groupKey);
            ((SseMcpConfig) serverConfig).setHeaders(Collections.emptyMap());
        }

        List<McpDTO> mcpList = getMcpListByGroupIds(Collections.singletonList(mcpGroup.getId()))
            .getOrDefault(mcpGroup.getId(), Collections.emptyList());

        List<ApiMcpServerStatusDTO> allMcpServersStatus = McpGatewayInvokeUtils.getAllMcpServersStatus();

        Map<Long, List<ApiMcpServerStatusDTO>> statusMap = allMcpServersStatus.stream()
            .collect(Collectors.groupingBy(ApiMcpServerStatusDTO::getId));


        List<Tool> tools = mcpList.stream()
            .map(mcpDTO -> {
                List<ApiMcpServerStatusDTO> mcpServerStatus = statusMap.getOrDefault(mcpDTO.getId(), Collections.emptyList());
                return mcpServerStatus == null ? new ArrayList<Tool>() : mcpServerStatus.stream().filter(status ->
                    status.getOverallStatus().equals(OverallStatus.HEALTHY)).findFirst().map(ApiMcpServerStatusDTO::getTools).orElse(Collections.emptyList());
            }).flatMap(Collection::stream).collect(Collectors.toList());

        McpExposeDTO.McpExposeDTOBuilder builder = McpExposeDTO.builder()
            .mcpType(serverConfig.getMcpType())
            .timeout(serverConfig.getTimeout())
            .tools(tools);

        switch (serverConfig) {
            case SseMcpConfig sseConfig -> {
                builder.url(sseConfig.getUrl());
                builder.headers(Optional.ofNullable(sseConfig.getHeaders()).orElse(Collections.emptyMap()));
            }
            case StreamableHttpMcpConfig streamHttpConfig -> {
                 builder.url(streamHttpConfig.getUrl());
                 builder.headers(Optional.ofNullable(streamHttpConfig.getHeaders()).orElse(Collections.emptyMap()));
             }
            default -> {
                log.warn("未处理或通用的 McpConfig 子类型: {} (groupKey: {}). URL 和 Headers 可能缺失或为默认值。",
                    serverConfig.getClass().getSimpleName(), groupKey);
                builder.url(null);
                builder.headers(Collections.emptyMap());
            }
        }

        return builder.build();
    }


    /**
     * 根据分组ID列表批量获取MCP列表
     *
     * @param groupIds 分组ID列表
     * @return 分组ID到MCP DTO列表的映射
     */
    private Map<Long, List<McpDTO>> getMcpListByGroupIds(List<Long> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询所有分组的关联关系
        Map<Long, List<McpGroupRef>> groupRefMap = mcpGroupRefRepository.listByGroupIds(groupIds);

        // 收集所有需要查询的MCP ID
        Set<Long> allMcpIds = new HashSet<>();
        for (List<McpGroupRef> refs : groupRefMap.values()) {
            for (McpGroupRef ref : refs) {
                allMcpIds.add(ref.getMcpId());
            }
        }

        // 如果没有关联的MCP，直接返回空结果
        if (allMcpIds.isEmpty()) {
            return groupIds.stream()
                    .collect(Collectors.toMap(groupId -> groupId, groupId -> Collections.emptyList()));
        }

        // 批量查询所有MCP
        List<Mcp> allMcps = mcpRepository.listMcpsByIds(new ArrayList<>(allMcpIds));

        // 创建MCP ID到DTO的映射，方便快速查找
        Map<Long, McpDTO> mcpDtoMap = new HashMap<>();
        for (Mcp mcp : allMcps) {
            mcpDtoMap.put(mcp.getId(), mcpAssembler.toDTO(mcp));
        }

        // 为每个分组组装MCP DTO列表
        Map<Long, List<McpDTO>> result = new HashMap<>(groupIds.size());
        for (Long groupId : groupIds) {
            List<McpGroupRef> refs = groupRefMap.getOrDefault(groupId, Collections.emptyList());
            List<McpDTO> mcpDtos = new ArrayList<>(refs.size());

            for (McpGroupRef ref : refs) {
                McpDTO mcpDto = mcpDtoMap.get(ref.getMcpId());
                if (mcpDto != null) {
                    mcpDtos.add(mcpDto);
                }
            }

            result.put(groupId, mcpDtos);
        }

        return result;
    }

}
