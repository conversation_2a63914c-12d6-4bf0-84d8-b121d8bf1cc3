package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.application.processor.TaskHandler;
import cn.genn.ai.hub.app.application.processor.TaskHandlerFactory;
import cn.genn.ai.hub.app.application.queues.QueueManageImpl;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.RepoTaskRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTaskPO;
import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class ProcessingTasksSchedule extends IJobHandler {

    @Resource
    protected RepoTaskRepositoryImpl taskRepository;
    @Resource
    protected TaskHandlerFactory handlerFactory;
    @Resource
    protected GennAIHubProperties aiHubProperties;

    @Resource
    protected QueueManageImpl queueManage;
    /**
     * TODO
     *
     * @throws Exception
     */
    @Override
    public void execute() throws Exception {
        log.info("ProcessingTasksSchedule begin");
        String jobParam = XxlJobHelper.getJobParam();
        if (StrUtil.isNotEmpty(jobParam)) {
            log.info("ProcessingTasksSchedule process param {}", jobParam);
            GennRequestContext.initTenantId(Long.parseLong(jobParam));
            List<KbRepoTaskDTO> processingTasks = taskRepository.findProcessingTasks();
            for (KbRepoTaskDTO task : processingTasks) {
                LocalDateTime updateTime = task.getUpdateTime();
                // updateTime 添加 60分钟
                Long retryIntervalTime = aiHubProperties.getKbQueueLimit().getRetryIntervalTime();
                LocalDateTime newUpdateTime = updateTime.plusMinutes(retryIntervalTime);
                if (newUpdateTime.isBefore(LocalDateTime.now())) {
                    TaskHandler handler = handlerFactory.getHandler(task.getTaskType());
//                    String processQueueName = handler.getProcessQueueName(Long.toString(task.getTenantId()));
//                    boolean processing = queueManage.isProcessing(processQueueName, task.getId().toString());
                    // 检查处理队列中任务
                    handler.invoke(task);

                    KbRepoTaskPO update = new KbRepoTaskPO();
                    update.setId(task.getId());
                    update.setExecuteCount(task.getExecuteCount() + 1);
                    taskRepository.updateById(update);
                }

            }

            GennRequestContext.clear();
            return;
        }
        log.info("ProcessingTasksSchedule end");
    }


}
