package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.KbRepoTaskDTO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTaskPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KbRepoTaskAssembler {

    KbRepoTaskAssembler INSTANCE = Mappers.getMapper(KbRepoTaskAssembler.class);

    KbRepoTaskDTO convert(KbRepoTaskPO kbRepoTaskPO);

    List<KbRepoTaskDTO> convert(List<KbRepoTaskPO> kbRepoTaskPOS);
}

