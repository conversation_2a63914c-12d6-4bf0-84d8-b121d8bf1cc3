package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.dto.feishu.NodeData;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 飞书文档同步
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书文档同步")
public class FSDocPreviewCommand {

    @Schema(description = "知识库ID", required = true)
    @NotEmpty(message = "知识库ID不能为空")
    private Long repoId;

    @Schema(description = "应用ID", required = true)
    @NotEmpty(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "选中的文件", required = true)
    @NotEmpty(message = "知识库ID不能为空")
    private NodeData nodeData;

}
