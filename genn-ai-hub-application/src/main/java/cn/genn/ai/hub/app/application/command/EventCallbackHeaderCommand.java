package cn.genn.ai.hub.app.application.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 事件回调请求头
 * @date 2025-04-09
 */
@Data
public class EventCallbackHeaderCommand implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 事件 ID。
     */
    @JsonProperty("event_id")
    private String eventId;

    /**
     * 事件类型。
     */
    @JsonProperty("event_type")
    private String eventType;

    /**
     * 事件创建时间戳（单位：毫秒）。
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 事件 Token。
     */
    private String token;

    /**
     * 应用 ID。
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 租户 Key。
     */
    @JsonProperty("tenant_key")
    private String tenantKey;
}
