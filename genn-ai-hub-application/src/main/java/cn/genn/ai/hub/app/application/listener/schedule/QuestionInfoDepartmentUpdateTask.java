package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.QuestionInfoPO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.FsDepartmentDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 补全question_info表first_department和last_department字段的定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class QuestionInfoDepartmentUpdateTask extends IJobHandler {

    @Resource
    private QuestionInfoMapper questionInfoMapper;

    @Resource
    private IUpmUserService upmUserService;

    @Override
    @IgnoreTenant
    public void execute() throws Exception {
        log.info("QuestionInfoDepartmentUpdateTask begin");

        try {
            int pageSize = 500;
            int pageNum = 1;
            int totalUpdated = 0;

            while (true) {
                // 分页查询question_info表，只查询没有部门信息的记录
                Page<QuestionInfoPO> page = new Page<>(pageNum, pageSize);
                LambdaQueryWrapper<QuestionInfoPO> wrapper = new QueryWrapper<QuestionInfoPO>()
                    .lambda()
                    .isNull(QuestionInfoPO::getFirstDepartment)
                    .isNull(QuestionInfoPO::getLastDepartment)
                    .isNotNull(QuestionInfoPO::getUserId);

                Page<QuestionInfoPO> result = questionInfoMapper.selectPage(page, wrapper);

                if (!result.hasNext()) {
                    break;
                }

                // 处理当前页数据
                int batchUpdated = processBatch(result.getRecords());
                totalUpdated += batchUpdated;

                log.info("第{}页处理完成，更新了{}条记录", pageNum, batchUpdated);

                pageNum++;
            }

            log.info("QuestionInfoDepartmentUpdateTask completed, total updated {} records", totalUpdated);
            XxlJobHelper.handleSuccess("成功更新 " + totalUpdated + " 条记录");

        } catch (Exception e) {
            log.error("QuestionInfoDepartmentUpdateTask failed", e);
            XxlJobHelper.handleFail("任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 处理一批数据
     */
    private int processBatch(List<QuestionInfoPO> questionList) {
        int updatedCount = 0;

        // 批量获取所有用户ID
        List<Long> userIds = questionList.stream()
            .map(QuestionInfoPO::getUserId)
            .filter(userId -> userId != null)
            .distinct()
            .toList();

        if (userIds.isEmpty()) {
            return 0;
        }

        // 批量查询用户信息
        UpmUserQuery upmUserQuery = new UpmUserQuery();
        upmUserQuery.setUserIdList(userIds);
        List<UpmUserDTO> upmUsers = upmUserService.conditionList(upmUserQuery);

        // 构建用户ID到用户信息的映射
        Map<Long, UpmUserDTO> userMap = upmUsers.stream()
            .collect(java.util.stream.Collectors.toMap(UpmUserDTO::getId, user -> user));

        // 批量处理每个问题记录
        for (QuestionInfoPO question : questionList) {
            try {
                UpmUserDTO upmUserDTO = userMap.get(question.getUserId());
                if (upmUserDTO != null && ObjUtil.isNotNull(upmUserDTO.getFsUser())
                    && CollUtil.isNotEmpty(upmUserDTO.getFsUser().getFsDepartments())) {

                    List<FsDepartmentDTO> fsDepartments = upmUserDTO.getFsUser().getFsDepartments();
                    String lastDepartment = fsDepartments.getFirst().getName();
                    String firstDepartment = fsDepartments.getLast().getName();

                    // 更新部门信息
                    UpdateWrapper<QuestionInfoPO> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda()
                        .eq(QuestionInfoPO::getId, question.getId())
                        .set(QuestionInfoPO::getFirstDepartment, firstDepartment)
                        .set(QuestionInfoPO::getLastDepartment, lastDepartment);

                    int updated = questionInfoMapper.update(null, updateWrapper);
                    if (updated > 0) {
                        updatedCount++;
                    }
                }
            } catch (Exception e) {
                log.error("处理数据失败，questionId: {}", question.getId(), e);
            }
        }

        return updatedCount;
    }
}
