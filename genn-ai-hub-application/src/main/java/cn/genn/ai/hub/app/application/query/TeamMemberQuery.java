package cn.genn.ai.hub.app.application.query;

import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 团队成员查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TeamMemberQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队ID")
    @NotNull(message = "团队ID不能为空")
    private Long teamId;

    @Schema(description = "用户名称，模糊查询")
    private String userName;

}
