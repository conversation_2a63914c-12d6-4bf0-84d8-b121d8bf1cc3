package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.AgentInfoCreateCommand;
import cn.genn.ai.hub.app.application.command.AgentInfoUpdateCommand;
import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.VerifyAgentDTO;
import cn.genn.ai.hub.app.application.query.AgentInfoQuery;
import cn.genn.ai.hub.app.domain.sensitiveword.model.config.SensitiveWordConfig;
import cn.genn.ai.hub.app.infrastructure.repository.po.AgentInfoPO;
import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.utils.jackson.JsonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AgentInfoAssembler extends QueryAssembler<AgentInfoQuery, AgentInfoPO, AgentInfoDTO> {

    AgentInfoAssembler INSTANCE = Mappers.getMapper(AgentInfoAssembler.class);

    /**
     * 将创建智能体命令转换为智能体PO
     *
     * @param command 创建智能体命令
     * @return 智能体PO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    AgentInfoPO createCommand2PO(AgentInfoCreateCommand command);

    /**
     * 将更新智能体命令转换为智能体PO
     *
     * @param command 更新智能体命令
     * @return 智能体PO
     */
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUserId", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateUserId", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    AgentInfoPO updateCommand2PO(AgentInfoUpdateCommand command);


    default VerifyAgentDTO convertVAD(AgentInfoPO agentInfoPO) {
        if (agentInfoPO == null) {
            return null;
        }
        return VerifyAgentDTO
            .builder()
            .id(agentInfoPO.getId())
            .teamId(agentInfoPO.getTeamId())
            .workflowId(agentInfoPO.getWorkflowId())
            .enableVerify(isEnableVerify(agentInfoPO))
            .userId(agentInfoPO.getCreateUserId()).build();
    }

    List<VerifyAgentDTO> convertVAD(List<AgentInfoPO> agentInfoPOS);

    private boolean isEnableVerify(AgentInfoPO verifyAgentInfo) {
        Map<String, Object> extraMap = verifyAgentInfo.getExtraConfig();
        SensitiveWordConfig sensitiveWordConfig = JsonUtils.parse(JsonUtils.toJson(extraMap.get("sensitiveWordConfig")), SensitiveWordConfig.class);
        return Optional.ofNullable(sensitiveWordConfig)
            .map(SensitiveWordConfig::isEnableVerify)
            .orElse(false);
    }

}

