package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 */
@Data
public class TTSConfig {


    /**
     * volcano（服务自上而下语音生成速度递减，情感表现力递增）
     *      火山引擎语音合成
     *      火山引擎语音合成大模型（非流式输入流式输出）
     *      火山引擎声音复刻大模型（非流式输入流式输出）
     * volcano_bidirection（服务自上而下语音生成速度递减，情感表现力递增）
     *      火山引擎语音合成大模型（流式输入流式输出）
     *      火山引擎声音复刻大模型（流式输入流式输出）
     * minimax：MiniMax 语音合成
     * ai_gateway：自部署语音合成模型（通过火山边缘大模型网关接入的）
     */
    private String provider = "volcano_bidirection";
    /**
     * 火山引擎语音合成大模型_流式输入流式输出
     */
    @NestedConfigurationProperty
    private TTSProviderParams providerParams = new TTSProviderParams();

}
