package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.SensitiveWordType;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 敏感词查询
 * @date 2025-05-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SensitiveWordQuery extends PageSortQuery {

    @Schema(description = "团队空间ID")
    private Long teamId;

    @Schema(description = "敏感词")
    private String words;

    @Schema(description = "敏感词类型")
    private SensitiveWordType wordsType;
}
