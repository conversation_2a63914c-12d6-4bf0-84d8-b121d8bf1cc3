package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.KbRepoFileAssembler;
import cn.genn.ai.hub.app.application.command.TosTempStsConfigCreateCommand;
import cn.genn.ai.hub.app.application.dto.FileStorageTempStsConfigDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepFileRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.KVStruct;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.component.ISsoAuthClient;
import cn.genn.spring.boot.starter.upm.model.LoginUserAuthInfoDTO;
import cn.genn.spring.boot.starter.upm.model.SsoUserTokenQuery;
import cn.hutool.core.lang.UUID;
import com.huaweicloud.sdk.core.auth.GlobalCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.*;
import com.huaweicloud.sdk.iam.v3.region.IamRegion;
import com.volcengine.model.request.AssumeRoleRequest;
import com.volcengine.model.response.AssumeRoleResponse;
import com.volcengine.service.sts.ISTSService;
import com.volcengine.service.sts.impl.STSServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoFileActionService {

    private final KbRepFileRepositoryImpl fileRepository;
    private final KbRepoFileAssembler assembler;
    private final FileStorageOperation fileStorageOperation;
    private final GennAIHubProperties gennAIHubProperties;
    private final ISsoAuthClient ssoAuthClient;

    /**
     * 上传文件后记录
     *
     * @param kbRepoFileDTO
     * @return
     */
    @Transactional
    public Long uploadLog(KbRepoFileDTO kbRepoFileDTO) {
        KbRepoFilePO po = assembler.DTO2PO(kbRepoFileDTO);
        // 生成一个外链
        String preSignedUrl = fileStorageOperation.preSignedUrlGetObject(po.getExternalFileId(), po.getFileName());
        po.setExternalFileUrl(preSignedUrl);
        boolean isSuccess = fileRepository.save(po);
        return isSuccess ? po.getId() : null;
    }

    public FileStorageTempStsConfigDTO getTosStsConfig(TosTempStsConfigCreateCommand command) {
        // 校验当前租户的桶的类型是否是tos
        Long tenant = CurrentUserHolder.getTenantId();
        KVStruct<String, GennAIFileStorageProperties> tenantConfig = gennAIHubProperties.getTenantStorageConfigs().get(tenant);
        if (tenantConfig != null && !"tos".equals(tenantConfig.getKey())) {
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        }
        GennAIFileStorageProperties tosProperties = tenantConfig.getValue();
        String roleArn = tosProperties.getIamRole();
        try {
            ISTSService stsService = STSServiceImpl.getInstance();
            stsService.setAccessKey(tosProperties.getAccessKey());
            stsService.setSecretKey(tosProperties.getSecretKey());
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleSessionName(command.getRoleSessionName());
            // 默认一小时过期
            request.setDurationSeconds(3600);
            //            request.setRoleTrn("trn:iam::2102045950:role/genn-frontend-STS");
            request.setRoleTrn(roleArn);
            AssumeRoleResponse resp = stsService.assumeRole(request);
            if (Objects.nonNull(resp)) {
                return FileStorageTempStsConfigDTO.builder()
                    .accessKeyId(resp.getResult().getCredentials().getAccessKeyId())
                    .secretAccessKey(resp.getResult().getCredentials().getSecretAccessKey())
                    // "CurrentTime":"2025-03-11T17:26:19+08:00","ExpiredTime":"2025-03-11T18:26:19+08:00"
                    .currentTime(LocalDateTime.parse(resp.getResult().getCredentials().getCurrentTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                    .expiredTime(LocalDateTime.parse(resp.getResult().getCredentials().getExpiredTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                    .sessionToken(resp.getResult().getCredentials().getSessionToken())
                    .endPoint(tosProperties.getEndpoint())
                    .bucketName(tosProperties.getBucketName())
                    .domain(tosProperties.getDomain())
                    .build();
            }
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        } catch (Exception e) {
            log.error("获取TOS临时STS配置失败", e);
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        }
    }

    public KbRepoFileDTO uploadWithoutLogin(Long tenantId, MultipartFile file) {
        return uploadFile(tenantId, file, null, null);
    }

    public KbRepoFileDTO uploadFile(Long tenantId, MultipartFile file, Long repoId, Long collectionId) {
        // 生成个objectKey
        String objectKey = "/userUpload/" + UUID.fastUUID() + "/" + file.getOriginalFilename();
        String filePlatform = null;
        // 如果租户为空 使用公共桶
        if (tenantId == null) {
            String defaultStorage = gennAIHubProperties.getDefaultStorage();
            switch (defaultStorage) {
                case "tos" -> {
                    filePlatform = gennAIHubProperties.getTos().getBucketName();
                }
                case "obs" -> {
                    filePlatform = gennAIHubProperties.getObs().getBucketName();
                }
                default -> throw new BusinessException("不支持的存储类型");
            }
            fileStorageOperation.saveFile(file, objectKey);
        } else {
            KVStruct<String, GennAIFileStorageProperties> tenantConfig = gennAIHubProperties.getTenantStorageConfigs().get(tenantId);
            if (Objects.isNull(tenantConfig)) {
                throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
            }
            GennAIFileStorageProperties fileStorageProperties = tenantConfig.getValue();
            filePlatform = fileStorageProperties.getBucketName();
            fileStorageOperation.saveFile(file, objectKey);
        }

        // markdown文件的content type需要特殊处理 写死 text/markdown
        String contentType = file.getContentType();
        if (file.getOriginalFilename().endsWith(".md")) {
            contentType = "text/markdown";
        }
        KbRepoFilePO po = KbRepoFilePO.builder()
            .repoId(Optional.ofNullable(repoId).orElse(0L))
            .collectionId(Optional.ofNullable(collectionId).orElse(0L))
            .filePlatform(filePlatform)
            .externalFileId(objectKey)
            .externalFileUrl("")
            .length((int) file.getSize())
            .fileName(file.getOriginalFilename())
            .contentType(contentType)
            .metadata(null)
            .build();
        // 生成一个外链
        String preSignedUrl = fileStorageOperation.preSignedUrlGetObject(po.getExternalFileId(), file.getOriginalFilename());
        po.setExternalFileUrl(preSignedUrl);
        fileRepository.save(po);
        return assembler.PO2DTO(po);
    }

    public FileStorageTempStsConfigDTO getObsTempStsConfig() {
        // 校验当前租户的桶的类型是否是obs
        Long tenant = CurrentUserHolder.getTenantId();
        KVStruct<String, GennAIFileStorageProperties> tenantConfig = gennAIHubProperties.getTenantStorageConfigs().get(tenant);
        if (tenantConfig != null && !"obs".equals(tenantConfig.getKey())) {
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        }
        GennAIFileStorageProperties obsProperties = tenantConfig.getValue();
        String endPoint = obsProperties.getEndpoint();
        String ak = obsProperties.getAccessKey();
        String sk = obsProperties.getSecretKey();
        String region = obsProperties.getRegion();
        ICredential auth = new GlobalCredentials()
            .withAk(ak)
            .withSk(sk);

        IamClient client = IamClient.newBuilder()
            .withCredential(auth)
            .withRegion(IamRegion.valueOf(region))
            .build();
        CreateTemporaryAccessKeyByAgencyRequest request = new CreateTemporaryAccessKeyByAgencyRequest();
        CreateTemporaryAccessKeyByAgencyRequestBody body = new CreateTemporaryAccessKeyByAgencyRequestBody();
        List listStatementResource = new ArrayList<>();
        listStatementResource.add("OBS:*:*:object:*");
        List listStatementAction = new ArrayList<>();
        listStatementAction.add("obs:object:PutObject");
        List listPolicyStatement = new ArrayList<>();
        listPolicyStatement.add(
            new ServiceStatement()
                .withAction(listStatementAction)
                .withEffect(ServiceStatement.EffectEnum.fromValue("Allow"))
                .withResource(listStatementResource)
        );
        ServicePolicy policyIdentity = new ServicePolicy();
        policyIdentity.withVersion("1.1")
            .withStatement(listPolicyStatement);
        List<AgencyAuthIdentity.MethodsEnum> listIdentityMethods = new ArrayList<>();
        listIdentityMethods.add(AgencyAuthIdentity.MethodsEnum.fromValue("token"));
        AgencyAuthIdentity identityAuth = new AgencyAuthIdentity();
        identityAuth.withMethods(listIdentityMethods)
            .withPolicy(policyIdentity);
        AgencyAuth authbody = new AgencyAuth();
        authbody.withIdentity(identityAuth);
        body.withAuth(authbody);
        request.withBody(body);
        try {
            CreateTemporaryAccessKeyByAgencyResponse response = client.createTemporaryAccessKeyByAgency(request);
            log.info("response: {}", JsonUtils.toJson(response));
            if (Objects.nonNull(response)) {
                // 使用 DateTimeFormatter 解析 Instant
                DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;
                Instant instant = Instant.parse(response.getCredential().getExpiresAt());
                LocalDateTime expiredTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
                return FileStorageTempStsConfigDTO.builder()
                    .accessKeyId(response.getCredential().getAccess())
                    .secretAccessKey(response.getCredential().getSecret())
                    .currentTime(LocalDateTime.now())
                    .expiredTime(expiredTime)
                    .sessionToken(response.getCredential().getSecuritytoken())
                    .endPoint(obsProperties.getEndpoint())
                    .bucketName(obsProperties.getBucketName())
                    .domain(obsProperties.getDomain())
                    .build();
            }
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        } catch (Exception e) {
            log.error("获取OBS临时STS配置失败", e);
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        }
    }

    public String getStoragePlatformType(String token) {
        String storage = gennAIHubProperties.getDefaultStorage();
        Long tenantId = null;
        try {
            SsoUserTokenQuery query = SsoUserTokenQuery.builder()
                .token(token)
                .build();
            LoginUserAuthInfoDTO loginUserAuthInfoDTO = ssoAuthClient.systemUserInfo(query);
            if (Objects.nonNull(loginUserAuthInfoDTO)) {
                tenantId = loginUserAuthInfoDTO.getTenantId();
            }
        } catch (Exception ignore) {
            log.info("get tenantId is null");
        }
        if (Objects.nonNull(tenantId)) {
            // 校验当前租户的桶的类型
            KVStruct<String, GennAIFileStorageProperties> tenantConfig = gennAIHubProperties.getTenantStorageConfigs().get(tenantId);
            if (tenantConfig == null) {
                throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
            }
            storage = tenantConfig.getKey();
        }
        return storage;
    }

    public String getFileUrl(String externalFileId) {
        return fileStorageOperation.preSignedUrlGetObject(externalFileId, null);
    }
    public KbRepoFilePO getFile(String externalFileId) {
        return fileRepository.getFileByExternalId(externalFileId);
    }

    public void updateFileLengthAndUrl(Long fileId, Integer length, String preSignedUrl) {
        fileRepository.updateFileLengthAndUrl(fileId, length, preSignedUrl);
    }
}

