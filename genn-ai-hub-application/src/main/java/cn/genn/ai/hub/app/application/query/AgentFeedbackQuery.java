package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.FeedbackSourceSystemEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 智能体反馈查询
 * @date 2025-07-02
 */
@Data
public class AgentFeedbackQuery {

    @Schema(description = "智能体ID")
    private String workflowId;

    @Schema(description = "会话id")
    private String chatId;

    @Schema(description = "单轮对话id")
    private String taskId;

    @Schema(description = "反馈系统来源")
    private FeedbackSourceSystemEnum sourceSystem;

    @Schema(description = "用户ID")
    private Long createUserId;

    @Schema(description = "是否仅查询我的反馈")
    private Boolean onlyMyFeedback;

}
