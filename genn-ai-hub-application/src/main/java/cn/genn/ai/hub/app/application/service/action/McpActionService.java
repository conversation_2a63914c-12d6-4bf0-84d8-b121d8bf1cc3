package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.McpAssembler;
import cn.genn.ai.hub.app.application.command.McpOperateCommand;
import cn.genn.ai.hub.app.application.command.McpUpdateStatusCommand;
import cn.genn.ai.hub.app.application.dto.mcp.McpDTO;
import cn.genn.ai.hub.app.application.dto.mcp.OpenapiParseResultDTO;
import cn.genn.ai.hub.app.application.service.query.McpQueryService;
import cn.genn.ai.hub.app.domain.mcp.model.entity.Mcp;
import cn.genn.ai.hub.app.domain.mcp.service.McpDomainService;
import cn.genn.ai.hub.app.infrastructure.utils.McpGatewayInvokeUtils;
import cn.genn.ai.hub.app.infrastructure.utils.smart.OpenapiSmartParse;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.BooleanTypeEnum;
import cn.genn.core.utils.TransactionUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class McpActionService {

    private final McpDomainService mcpDomainService;
    private final McpQueryService mcpQueryService;
    private final McpAssembler mcpAssembler;

    /**
     * 创建MCP
     *
     * @param command MCP操作命令
     * @return MCP ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(McpOperateCommand command) {
        // 使用装配器将命令转换为领域实体
        Mcp mcp = mcpAssembler.toEntity(command);
        // 调用领域服务创建MCP
        Long id = mcpDomainService.createMcp(mcp);
        TransactionUtils.doAfterCommit(() -> {
            //调用网关新增服务
            McpGatewayInvokeUtils.addMcpServer(mcpQueryService.getDetail(id));
        });
        return id;
    }

    /**
     * 更新MCP
     *
     * @param command MCP操作命令
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(McpOperateCommand command) {
        McpDTO mcpDTO = mcpQueryService.get(IdQuery.builder().id(command.getId()).build());
        Mcp mcp = mcpAssembler.toEntity(command);
        // 调用领域服务更新MCP
        boolean result = mcpDomainService.updateMcp(mcp);
        if (!JsonUtils.toJson(mcpDTO.getMcpConfig()).equals(JsonUtils.toJson(command.getMcpConfig()))) {
            //配置变更,重新刷新网关的mcp服务
            TransactionUtils.doAfterCommit(() -> {
                McpGatewayInvokeUtils.updateMcpServer(command.getId(), mcpQueryService.getDetail(command.getId()));
            });
        }
        return result;
    }

    /**
     * 删除MCP
     *
     * @param command ID命令
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(IdCommand command) {
        // 调用领域服务删除MCP
        boolean result = mcpDomainService.deleteMcp(command.getId());
        TransactionUtils.doAfterCommit(() -> {
            // 调用网关删除服务
            McpGatewayInvokeUtils.deleteMcpServer(command.getId());
        });
        return result;
    }

    /**
     * 更新MCP状态（启用/停用）
     *
     * @param command MCP状态更新命令
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(McpUpdateStatusCommand command) {
        boolean result = mcpDomainService.updateMcpStatus(command.getId(), command.getEnabled());
        TransactionUtils.doAfterCommit(() -> {
            if (command.getEnabled() == BooleanTypeEnum.TRUE) {
                McpGatewayInvokeUtils.updateMcpServer(command.getId(), mcpQueryService.getDetail(command.getId()));
            } else {
                McpGatewayInvokeUtils.deleteMcpServer(command.getId());
            }
        });
        return result;
    }

    public void restart(IdCommand command) {
        McpDTO mcpDTO = mcpQueryService.getDetail(command.getId());
        McpGatewayInvokeUtils.updateMcpServer(command.getId(), mcpDTO);
    }

    /**
     * 解析OpenAPI文档
     * @param input 用户输入
     */
    public String parseOpenApi(String input) {
        String content = OpenapiSmartParse.parse(input);
        if (content != null) {
            OpenapiParseResultDTO result = JsonUtils.parse(content, OpenapiParseResultDTO.class);
            if (result != null) {
                return JsonUtils.toJsonNotNull(result);
            } else {
                log.error("解析OpenAPI文档失败，返回内容不是有效的JSON格式");
            }
        } else {
            log.error("解析OpenAPI文档失败，返回内容为空");
        }
        return null;
    }
}
