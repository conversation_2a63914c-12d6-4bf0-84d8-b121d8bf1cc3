package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCAgentATO;
import cn.genn.ai.hub.app.application.dto.rtc.RTCAgentType;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.RTCProperties;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "RTC")
@RestController
@RequestMapping("/rtc")
@RequiredArgsConstructor
public class RTCController {

    private final GennAIHubProperties gennAIHubProperties;
    private final AgentInfoRepositoryImpl agentInfoRepository;


    @PostMapping("/agentList")
    @Operation(summary = "获取rtc智能体列表")
    public List<RTCAgentATO> getAgentList() {
        List<RTCProperties.Agent> agentList = gennAIHubProperties.getRtc().getAgents().get(CurrentUserHolder.getTenantId());
        List<RTCAgentATO> agentDTOS = agentList.stream()
            .map(agent -> RTCAgentATO.builder()
                .agentType(RTCAgentType.fromCode(agent.getType()))
                .appId(agent.getAppId())
                .build())
            .toList();
        // 设置智能体名称、描述和图标
        agentDTOS.forEach(agentDTO -> {
            AgentInfoDTO agentInfo = agentInfoRepository.getAgentByWorkflowId(agentDTO.getAppId());
            if (agentInfo != null) {
                agentDTO.setName(agentInfo.getName());
                agentDTO.setDescription(agentInfo.getDescription());
                agentDTO.setAvatar(agentInfo.getAvatar());
            }
        });
        return agentDTOS;
    }
}
