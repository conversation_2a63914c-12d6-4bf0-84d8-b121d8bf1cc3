package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.dto.ToolInfoDTO;
import cn.genn.ai.hub.app.application.query.ToolInfoQuery;
import cn.genn.ai.hub.app.infrastructure.repository.po.ToolInfoPO;
import cn.genn.core.model.assembler.QueryAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToolInfoAssembler extends QueryAssembler<ToolInfoQuery, ToolInfoPO, ToolInfoDTO>{

    ToolInfoAssembler INSTANCE = Mappers.getMapper(ToolInfoAssembler.class);

}

