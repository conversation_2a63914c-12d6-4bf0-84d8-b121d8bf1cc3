package cn.genn.ai.hub.app.application.command;

import cn.genn.core.model.enums.BooleanTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * MCP状态更新命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpUpdateStatusCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "MCP ID")
    @NotNull(message = "MCP ID不能为空")
    private Long id;

    @Schema(description = "是否启用")
    @NotNull(message = "启用状态不能为空")
    private BooleanTypeEnum enabled;
}
