package cn.genn.ai.hub.app.infrastructure.config.auth;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class AuthSqlInjectAspect {

    public static ThreadLocal<Boolean> AUTH_SQL_INJECT_HOLDER = new ThreadLocal<>();


    @Around("@annotation(cn.genn.ai.hub.app.infrastructure.config.auth.AuthSqlInject)")
    public Object auth(ProceedingJoinPoint pjp) throws Throwable {
        try {
            AUTH_SQL_INJECT_HOLDER.set(true);
            return pjp.proceed();
        }finally {
            AUTH_SQL_INJECT_HOLDER.remove();
        }
    }
}
