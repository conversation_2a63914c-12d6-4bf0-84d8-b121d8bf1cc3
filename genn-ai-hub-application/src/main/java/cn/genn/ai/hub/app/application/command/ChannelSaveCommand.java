package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.core.api.channel.ChannelTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 渠道保存命令
 * @date 2025-04-21
 */
@Data
public class ChannelSaveCommand {

    @Schema(description = "外部渠道id")
    private Long id;

    @Schema(description = "团队id，如果为空，代表是个人空间创建")
    private Long teamId;

    @NotNull(message = "渠道类型不能为空")
    @Schema(description = "渠道类型, feishu, wechat")
    private ChannelTypeEnum channelType;

    @NotBlank(message = "渠道唯一标识，飞书渠道为appId")
    private String channelUniqueIdentifier;

    @NotBlank(message = "渠道配置不能为空")
    @Schema(description = "渠道配置的JSON元数据")
    private String channelConfig;

}
