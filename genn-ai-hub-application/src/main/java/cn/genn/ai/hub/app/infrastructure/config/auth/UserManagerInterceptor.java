package cn.genn.ai.hub.app.infrastructure.config.auth;

import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.core.exception.CheckException;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class UserManagerInterceptor implements HandlerInterceptor {
    public static final Set<String> ADMIN_ROLES = Set.of("admin", "super_admin");

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Set<String> roles = CurrentUserHolder.getRoles();
        roles.retainAll(ADMIN_ROLES);
        if (roles.isEmpty()) {
            throw new CheckException(MessageCode.AUTH_API_AUTH_FAILED);
        }
        return true;
    }
}
