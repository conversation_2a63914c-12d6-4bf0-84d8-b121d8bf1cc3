package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.dto.feishu.ChatInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.openai.api.OpenAiApi;

import java.util.List;
import java.util.Map;

/**
 * 插件调用命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "插件调用命令")
public class PluginInvokeCommand {

    @Schema(description = "工作流ID")
    @NotEmpty(message = "工作流ID不能为空")
    private String workflowId;

    @Schema(description = "是否流式处理")
    private boolean stream;

    @Schema(description = "插件的唯一标识")
    @NotEmpty(message = "插件的唯一标识不能为空")
    private String pluginKey;

    @Schema(description = "插件版本号")
    @NotEmpty(message = "插件版本号不能为空")
    private String version;

    @Schema(description = "插件调用参数")
    private Map<String, Object> params;

    @Schema(description = "聊天历史")
    private List<OpenAiApi.ChatCompletionMessage> chatHistory;

    @Schema(description = "对话渠道相关信息")
    private ChatInfo chatInfo;

}
