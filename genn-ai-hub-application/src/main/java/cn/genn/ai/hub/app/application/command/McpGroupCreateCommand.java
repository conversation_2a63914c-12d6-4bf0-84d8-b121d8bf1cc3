package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.domain.mcp.model.valobj.McpConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * MCP分组创建命令
 *
 * <AUTHOR>
 */
@Data
public class McpGroupCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分组名称")
    private String name;

    @Schema(description = "分组描述")
    private String description;

    @Schema(description = "MCP ID列表")
    private List<Long> mcpIds;

    @Schema(description = "服务配置")
    private McpConfig serverConfig;
}
