package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 敏感词类型
 * @date 2025-05-16
 */
@Getter
@AllArgsConstructor
public enum SensitiveWordType {

    SYSTEM(1, "系统敏感词"),
    USER(2, "用户自定义敏感词"),
    ;

    @EnumValue
    @JsonValue
    private final Integer type;

    private final String desc;
}
