package cn.genn.ai.hub.app.application.query;

import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import cn.genn.database.mybatisplus.query.annotation.QueryType;
import cn.genn.database.mybatisplus.query.constant.Condition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Tags查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagsQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队id,为空代表查询个人")
    private Long teamId;

    @Schema(description = "标签类型，agent：AGENT-智能体, knowledge：KNOWLEDGE-知识库, general_tool：GENERAL_TOOL-通用工具, workflow_tool：WORKFLOW_TOOL-工作流工具, custom_tool：CUSTOM_TOOL-自定义工具")
    @QueryType
    @NotNull(message = "标签类型不能为空")
    private TagTypeEnum tagType;

    @Schema(description = "标签名称")
    @QueryType(condition = Condition.LIKE)
    private String name;

    @Schema(description = "标签ID列表")
    private List<Long> tagIds;

}

