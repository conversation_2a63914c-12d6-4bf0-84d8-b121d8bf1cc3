package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTypeEnum;
import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


/**
 * 飞书文档同步
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "飞书文档同步")
public class FSSheetSyncCommand {

    @Schema(description = "父id")
    private Long pId;

    @Schema(description = "知识库ID", required = true)
    @NotEmpty(message = "知识库ID")
    private Long repoId;

    @Schema(description = "sheetId", required = true)
    @NotEmpty(message = "sheetId 不能为空")
    private String sheetId;

    @Schema(description = "表格token", required = true)
    private String objToken;

    @Schema(description = "同步范围 <开始位置>:<结束位置>", example = "A1:C8", required = true)
    private String ranges;
    /**
     *
     * 指定单元格数据的格式。可选值如下所示。当参数缺省时，默认不进行公式计算，返回公式本身，且单元格为数值格式。
     *
     * ToString：返回纯文本的值（数值类型除外）
     * Formula：单元格中含有公式时，返回公式本身
     * FormattedValue：计算并格式化单元格
     * UnformattedValue：计算但不对单元格进行格式化
     */
    @Schema(description = "指定单元格数据的格式 可用值：ToString(返回纯文本的值)、Formula(单元格中含有公式时，返回公式本身)、FormattedValue(计算并格式化单元格)、UnformattedValue(计算但不对单元格进行格式化)")
    private String valueRenderOption;

    @Schema(description = "表格多少行作为一个分块", example = "1")
    private Integer chunkSize = 1;

    @Schema(description = "文件名")
    private String title;

    @Schema(description = "数据集类型", hidden = true)
    private CollectionTypeEnum collectionType = CollectionTypeEnum.FS_SHEET;

    @Schema(description = "训练模式", hidden = true)
    private TrainingTypeEnum trainingType = TrainingTypeEnum.AUTO;

    @Schema(description = "处理方式 直接分段 增强分段 问答拆分", hidden = true)
    private TrainingModelEnum handleType = TrainingModelEnum.CHUNK;

    @Schema(description = "智能分块摘要开关", hidden = true)
    private Boolean smartChunkSummary = false;
}
