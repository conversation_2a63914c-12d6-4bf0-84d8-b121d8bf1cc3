package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 渠道业务类型枚举
 * @date 2025-04-08
 */
@Getter
@AllArgsConstructor
public enum ChannelBizTypeEnum {

    /**
     * 智能体应用
     */
    AGENT_APP("agent_app", "智能体应用"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String name;

    public static ChannelBizTypeEnum of(String code) {
        for (ChannelBizTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
