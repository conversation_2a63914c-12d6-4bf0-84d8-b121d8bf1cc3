package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.SensitiveWordType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 敏感词保存命令
 * @date 2025-05-16
 */
@Data
public class SensitiveWordSaveCommand {

    @Schema(description = "敏感词id")
    private Long id;

    @Schema(description = "团队空间ID")
    private Long teamId;

    @Schema(description = "敏感词类型 1：系统 2：用户")
    private SensitiveWordType wordsType;

    @Schema(description = "敏感词")
    private String words;

}
