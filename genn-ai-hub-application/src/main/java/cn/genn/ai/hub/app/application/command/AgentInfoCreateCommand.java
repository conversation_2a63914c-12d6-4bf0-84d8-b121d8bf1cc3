package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.AgentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 创建智能体命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentInfoCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队id，如果为空，代表是个人空间创建")
    private Long teamId;

    @Schema(description = "mongo对应的工作流id")
    @NotBlank(message = "工作流ID不能为空")
    private String workflowId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "智能体类型，chat：CHAT-对话智能体, workflow：WORKFLOW-工作流智能体, tool_workflow：TOOL_WORKFLOW-工作流工具, tool_http：TOOL_HTTP-HTTP工具")
    @NotNull(message = "智能体类型不能为空")
    private AgentTypeEnum agentType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "额外配置")
    private Map<String, Object> extraConfig;

    @Schema(description = "标签ID列表")
    private List<Long> tagIds;
}
