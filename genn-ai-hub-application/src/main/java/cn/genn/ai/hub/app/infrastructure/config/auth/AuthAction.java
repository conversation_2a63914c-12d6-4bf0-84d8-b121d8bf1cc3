package cn.genn.ai.hub.app.infrastructure.config.auth;


import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthActionWrapper;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.wrapper.DefaultAuthActionWrapper;

import java.lang.annotation.*;

/**
 * 权限注解,控制接口返回值行权限过滤
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface AuthAction {

    /**
     * 是否启用
     */
    boolean enabled() default true;

    /**
     * 资源类型
     */
    ResourceType resourceType() default ResourceType.EMPTY;

    /**
     * 资源实例属性，例如id
     */
    String resourceField() default "id";

    /**
     * 主体所属关系字段
     * 例如用户属于团队,这里指定团队id
     */
    SubjectRelation[] subjectRelation() default {};

    /**
     * 查询的action范围，默认为全部
     */
    ActionType[] actionRange() default {};

    /**
     * 复杂结构包装类
     * @return
     */
    Class<? extends AuthActionWrapper> resultWrapper() default DefaultAuthActionWrapper.class;

}
