package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.assembler.CodeRepoAssembler;
import cn.genn.ai.hub.app.application.command.CodeRepoSaveCommand;
import cn.genn.ai.hub.app.domain.auth.model.entity.AuthCommon;
import cn.genn.ai.hub.app.domain.auth.model.event.AuthCommonModifyEvent;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.AuthUniqueCriteriaEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.CodeRepoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.CodeRepoPO;
import cn.genn.ai.hub.app.infrastructure.utils.auth.AuthUtils;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 代码库应用服务
 * @date 2025-05-16
 */
@Service
@RequiredArgsConstructor
public class CodeRepoActionService {

    private final CodeRepoRepositoryImpl codeRepoRepository;

    private final CodeRepoAssembler codeRepoAssembler;

    private final SpringEventPublish springEventPublish;

    @Transactional
    public void save(CodeRepoSaveCommand command) {
        CodeRepoPO codeRepoPO = codeRepoAssembler.toPO(command);
        if (codeRepoPO.getId() == null) {
            codeRepoRepository.save(codeRepoPO);
            handleCreateAuth(codeRepoPO.getId());
        } else {
            codeRepoRepository.updateById(codeRepoPO);
        }
    }

    public void delete(Long id) {
        codeRepoRepository.removeById(id);
        // 删除代码库的权限
        springEventPublish.publish(AuthCommonModifyEvent.buildDeleteEvent(
            Map.of(AuthUniqueCriteriaEnum.RESOURCE_TYPE, ResourceType.CODE_REPO,
                AuthUniqueCriteriaEnum.RESOURCE_KEY, String.valueOf(id)), this));
    }

    private void handleCreateAuth(Long codeRepoId) {
        List<AuthCommon> authCommonList = new ArrayList<>();
        authCommonList.add(AuthUtils.buildUserCodeRepoAuth(codeRepoId, CurrentUserHolder.getUserId(), ActionType.MANAGER));
        springEventPublish.publish(AuthCommonModifyEvent
            .buildAddEvent(authCommonList, this));
    }
}
