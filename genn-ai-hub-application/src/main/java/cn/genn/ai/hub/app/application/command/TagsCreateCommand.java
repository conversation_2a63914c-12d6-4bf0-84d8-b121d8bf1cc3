package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.app.application.enums.TagTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 创建标签命令
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagsCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "团队id")
    private Long teamId;

    @Schema(description = "标签类型，agent：AGENT-智能体, knowledge：KNOWLEDGE-知识库, general_tool：GENERAL_TOOL-通用工具, workflow_tool：WORKFLOW_TOOL-工作流工具, custom_tool：CUSTOM_TOOL-自定义工具")
    @NotNull(message = "标签类型不能为空")
    private TagTypeEnum tagType;

    @Schema(description = "标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String name;

    @Schema(description = "图标链接")
    private String avatar;
}
