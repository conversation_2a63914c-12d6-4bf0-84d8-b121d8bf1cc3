package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.dto.KbRepoBaseInfoDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCategoryDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatDetailEditCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatMoveCommand;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoCategoryRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCategoryPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.enums.DeletedEnum;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoCategoryActionService {

    @Resource
    private KbRepoCategoryRepositoryImpl categoryRepository;
    @Resource
    private KbRepoBaseInfoRepositoryImpl baseInfoRepository;

    /**
     * 新建文件夹
     *
     * @param command
     * @return
     */
    public Long createCat(KbRepoCatCreateCommand command) {
        // 确认父目录是否存在
        KbRepoCategoryPO parentCat = null;
        if (Objects.nonNull(command.getCatpId()) && command.getCatpId() != 0L) {
            QueryWrapper<KbRepoCategoryPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                .eq(KbRepoCategoryPO::getId, command.getCatpId())
                .eq(KbRepoCategoryPO::getDeleted, DeletedEnum.NOT_DELETED);
            parentCat = categoryRepository.getBaseMapper().selectOne(queryWrapper);
            if (parentCat == null) {
                log.error("父目录不存在");
                return null;
            }
        }
        try {
            KbRepoCategoryPO categoryPO = KbRepoCategoryPO.builder()
                .catName(command.getCatName())
                .description(command.getDescription())
                .pid(command.getCatpId())
                .level(Objects.nonNull(parentCat) ? parentCat.getLevel() + 1 : 1)
                .build();
            categoryRepository.save(categoryPO);
            return categoryPO.getId();
        } catch (Exception e) {
            log.error("createCat error, command: {}", command, e);
        }
        return null;
    }

    public Boolean moveKbRepoCat(KbRepoCatMoveCommand command) {
        if (command.getIsCat() != null && command.getIsCat()) {
            return categoryRepository.moveCat(command.getCurId(), command.getTagCatId());
        } else {
            return baseInfoRepository.moveRepo(command.getCurId(), command.getTagCatId());
        }
    }

    public Boolean editCatDetail(KbRepoCatDetailEditCommand command) {
        return categoryRepository.editCatDetail(command);
    }

    public Boolean deleteCat(IdQuery command) {
        List<KbRepoCategoryDTO> cats = categoryRepository.getByPId(command.getId());
        if (CollectionUtils.isNotEmpty(cats)) {
            throw new BusinessException(MessageCode.KB_CATE_NOT_DELETE);
        }
        List<KbRepoBaseInfoDTO> infos = baseInfoRepository.getByCateId(command.getId());
        if (CollectionUtils.isNotEmpty(infos)) {
            throw new BusinessException(MessageCode.KB_CATE_NOT_DELETE);
        }

        return categoryRepository.deleteById(command.getId());
    }
}

