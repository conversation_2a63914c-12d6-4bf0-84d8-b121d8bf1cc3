package cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb;

import cn.genn.ai.hub.app.infrastructure.config.auth.Auth;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthInterceptorParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParam;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.AuthParamResolver;
import cn.hutool.core.util.ReflectUtil;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public abstract class AbstractRepoAuthParamResolver implements AuthParamResolver {

    @Override
    @SuppressWarnings("unchecked")
    public List<AuthParam> authParams(AuthInterceptorParam interceptor) {
        Auth auth = interceptor.getAuth();
        Map<String, Object> paramsMap = interceptor.getParamsMap();
        //取第一个参数
        Object firstValue = paramsMap.entrySet().iterator().next().getValue();
        String idFieldName = idFieldName();
        Object idFieldValue = ReflectUtil.getFieldValue(firstValue, idFieldName);
        if (idFieldValue == null) {
            idFieldValue = ((List<Long>) ReflectUtil.getFieldValue(firstValue, "ids")).getFirst();
        }
        Long id = (Long) idFieldValue;
        Long repoId = getRepoIdById(id);
        return List.of(AuthParam
            .builder()
            .subjectType(auth.subjectType())
            .resourceType(auth.resourceType())
            .resourceKey(String.valueOf(repoId))
            .actionType(auth.actionType())
            .build());
    }

    // 抽象方法，由子类实现具体的获取repoId逻辑
    protected abstract Long getRepoIdById(Long id);

    protected String idFieldName() {
        return "id";
    }
}

