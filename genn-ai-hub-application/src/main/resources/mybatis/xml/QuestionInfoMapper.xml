<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.ai.hub.app.infrastructure.repository.mapper.QuestionInfoMapper">

    <select id="getListByAppIdAndSource" resultType="cn.genn.ai.hub.app.application.dto.question.HighFreAnalysisInfo">
        SELECT id,
        app_Id as appId,
        source,
        question
        FROM question_info
        WHERE app_Id = #{appId} AND source = #{source}
    </select>

    <select id="getAllAnalysisInfo" resultType="cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO">
        SELECT
        t.app_id as appId,
        t.source
        FROM question_info t
        <where>
            <if test="appId != null and appId != ''">
                and t.app_id = #{appId}
            </if>
        </where>
        group by t.app_id, t.source;
    </select>

    <select id="getTopUsers" resultType="cn.genn.ai.hub.app.application.dto.question.UserQuestionCountDTO">
        SELECT
        user_id as userId,
        open_id as openId,
        COUNT(*) as count,
        MAX(create_time) as lastQuestionTime
        FROM question_info
        WHERE app_id = #{query.appId}
        <if test="query.sources != null">
            and source in
            <foreach collection="query.sources" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.startTime != null and query.endTime != null ">
            AND create_time BETWEEN #{query.startTime} AND #{query.endTime}
        </if>
        GROUP BY user_id
        ORDER BY count DESC
        LIMIT #{query.limit}
    </select>


    <select id="getDepartmentTop10" resultType="cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO">
        WITH department_type_stats AS (
        SELECT
        first_department,
        type,
        COUNT(*) as type_count,
        ROW_NUMBER() OVER (
        PARTITION BY first_department
        ORDER BY COUNT(*) DESC
        ) as type_rank
        FROM question_info
        WHERE app_id = #{query.appId}
        <if test="query.sources != null">
            and source in
            <foreach collection="query.sources" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.startTime != null and query.endTime != null ">
            AND create_time BETWEEN #{query.startTime} AND #{query.endTime}
        </if>
        AND type IS NOT NULL
        AND type != ''
        AND first_department IS NOT NULL
        AND first_department != ''
        GROUP BY first_department, type
        ),

        department_stats AS (
        SELECT
        first_department,
        COUNT(*) as questionCount,
        MAX(create_time) as lastQuestionTime
        FROM question_info
        WHERE app_id = #{query.appId}
        <if test="query.sources != null">
            and source in
            <foreach collection="query.sources" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.startTime != null and query.endTime != null ">
            AND create_time BETWEEN #{query.startTime} AND #{query.endTime}
        </if>
        AND first_department IS NOT NULL
        AND first_department != ''
        GROUP BY first_department
        )

        SELECT
        ds.first_department as firstDepartment,
        ds.questionCount as `count`,
        ds.lastQuestionTime as lastQuestionTime,
        dts.type as type
        FROM department_stats ds
        LEFT JOIN department_type_stats dts
        ON ds.first_department = dts.first_department
        AND dts.type_rank = 1
        ORDER BY `count` DESC
        LIMIT #{query.limit}
    </select>

    <select id="getDepartmentUsers" resultType="cn.genn.ai.hub.app.application.dto.question.DepartmentUserDTO">
        SELECT
        user_id as userId,
        open_id as openId,
        last_department as lastDepartment,
        first_department as firstDepartment,
        COUNT(*) as questionCount,
        MAX(create_time) as lastQuestionTime
        FROM question_info
        WHERE app_id = #{query.appId}
        <if test="query.startTime != null and query.endTime != null ">
            AND create_time BETWEEN #{query.startTime} AND #{query.endTime}
        </if>
        AND first_department IS NOT NULL
        AND first_department != ''
        AND first_department IN
        <foreach collection="departments" item="department" open="(" separator="," close=")">
            #{department}
        </foreach>
        GROUP BY user_id, open_id, last_department, first_department
        ORDER BY first_department, questionCount DESC
    </select>

    <select id="getDepartmentHistoryCount" resultType="cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO">
        SELECT
            first_department as firstDepartment,
            COUNT(*) as historyCount
        FROM question_info
        WHERE app_id = #{query.appId}
        <if test="query.sources != null">
            and source in
            <foreach collection="query.sources" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.endTime != null">
            AND create_time &lt;= #{query.endTime}
        </if>
        AND first_department IS NOT NULL
        AND first_department != ''
        GROUP BY first_department
    </select>

    <select id="getUserHistoryCount" resultType="long">
        SELECT COUNT(DISTINCT user_id) as count
        FROM question_info
        WHERE app_id = #{query.workflowId}
        <if test="query.source != null">
            and source in
            <foreach collection="query.source" item="source" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>
        <if test="query.createTimeRight != null">
            AND create_time &lt;= #{query.createTimeRight}
        </if>
    </select>

</mapper>
