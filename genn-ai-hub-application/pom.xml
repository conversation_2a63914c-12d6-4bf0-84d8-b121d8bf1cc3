<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-ai-hub</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-ai-hub-application</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <javax.servlet-api.version>4.0.1</javax.servlet-api.version>
        <jakarta.servlet-api.version>5.0.0</jakarta.servlet-api.version>
        <ve-tos-java-sdk.version>2.8.8</ve-tos-java-sdk.version>
        <volc-sdk-java.version>1.0.230</volc-sdk-java.version>
        <esdk-obs-java.version>3.22.12</esdk-obs-java.version>
        <milvus-sdk-java.version>2.5.5</milvus-sdk-java.version>
    </properties>

    <dependencies>
        <!-- ==================== genn 内部依赖   ===================      -->

        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-database</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-skywalking</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-bridge</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-event-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-event-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-xxl-job</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot.nova</groupId>
            <artifactId>genn-spring-boot-starter-upm</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.nova</groupId>
            <artifactId>genn-feishu</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <!-- ======================= 服务api依赖  ===========================      -->
        <!-- 其他项目 api依赖       -->
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-ai-hub-core</artifactId>
            <version>1.3.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-ai-hub-plugin</artifactId>
            <version>1.3.2-RELEASE</version>
        </dependency>


        <!-- ======================= 其他三方依赖   =======================      -->
        <!-- WebSocket客户端 -->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.5.3</version>
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta.servlet-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>ve-tos-java-sdk</artifactId>
            <version>${ve-tos-java-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>volc-sdk-java</artifactId>
            <version>${volc-sdk-java.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 华为云 OBS -->
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java</artifactId>
            <version>${esdk-obs-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-ecs</artifactId>
            <version>3.1.142</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-iam</artifactId>
            <version>3.1.142</version>
        </dependency>
        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>${milvus-sdk-java.version}</version>
        </dependency>

        <!-- FastExcel 依赖 -->
        <dependency>
            <groupId>cn.idev.excel</groupId>
            <artifactId>fastexcel</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- 解析xml -->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.21</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.15.2</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>genn-ai-hub</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--  自身和外部api包管理,每次依赖一个新的外部api,在这里定义版本号,dev和test保持 为 genn-service-api.version     -->
        <profile>
            <id>local</id>
            <properties>
                <genn-test-nova-api.version>1.0.0-SNAPSHOT</genn-test-nova-api.version>
                <genn-trans-support-upm-api.version>1.0.0-SNAPSHOT</genn-trans-support-upm-api.version>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <genn-test-nova-api.version>1.0.0-RELEASE</genn-test-nova-api.version>
                <genn-trans-support-upm-api.version>1.1.1-RELEASE</genn-trans-support-upm-api.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <genn-test-nova-api.version>${genn-service-api.version}</genn-test-nova-api.version>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <genn-test-nova-api.version>${genn-service-api.version}</genn-test-nova-api.version>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>Central Repository</id>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
    </repositories>
</project>
